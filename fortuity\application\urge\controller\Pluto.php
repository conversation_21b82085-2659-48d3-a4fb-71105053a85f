<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');


use app\common\Playful;
use app\common\Remotely;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;


class Pluto extends Base
{
    /*
     * 商家类型
     */
    public function merchant_type()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('easy_info_type')
            ->where('name', 'like', "%{$hazy_name}%")
            ->where('much_id', $this->much_id)
            ->order(['sort' => 'asc', 'id' => 'asc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 商家类型排序
     */
    public function merchant_type_sort()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $sort = request()->post('dalue');
            Db::startTrans();
            try {
                Db::name('easy_info_type')->where('id', $syid)->where('much_id', $this->much_id)->update(['sort' => $sort]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 新增商家类型
     */
    public function new_merchant_type()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            $data['create_time'] = time();
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('easy_info_type')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            return $this->fetch();
        }
    }

    /*
     * 编辑商家分类
     */
    public function edit_merchant_type()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $data['name'] = trim(input('post.name'));
            $data['icon'] = trim(input('post.icon'));
            $data['status'] = intval(input('post.status'));
            $data['sort'] = intval(input('post.sort'));
            Db::startTrans();
            try {
                Db::name('easy_info_type')->where('id', $fid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $fid = intval(input('get.layid'));
            $list = Db::name('easy_info_type')->where('id', $fid)->where('much_id', $this->much_id)->find();
            if ($list) {
                $this->assign('list', $list);
                return $this->fetch();
            } else {
                abort(404);
            }
        }
    }

    /*
     * 删除商家类型
     */
    public function del_merchant_type()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            Db::startTrans();
            try {
                Db::name('easy_info_list')->where('merchant_type', $fid)->where('much_id', $this->much_id)->delete();
                Db::name('easy_info_type')->where('id', $fid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }


    /*
     * 商家列表
     */
    public function merchant_list()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        $url = $this->defaultQuery();

        $fid = intval(input('get.fid'));
        $name = trim(input('get.name'));

        $when = array();
        ($fid !== 0 && $when['eil.id'] = $fid) || ($fid === 0 && $fid = '');

        $page = request()->get('page', 1);
        $list = Db::name('easy_info_list')
            ->alias('eil')
            ->join('easy_info_type eit', 'eil.merchant_type = eit.id', 'left')
            ->where($when)
            ->where('eil.merchant_name', 'like', "%{$name}%")
            ->where('eil.much_id&eit.much_id', $this->much_id)
            ->order(['eil.sort' => 'asc', 'eil.id' => 'asc'])
            ->field('eil.*,eit.name')
            ->paginate(10, false, ['query' => ['s' => $url, 'fid' => $fid, 'name' => $name]]);
        $this->assign('list', $list);
        $this->assign('fid', $fid);
        $this->assign('name', $name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 商家列表排序
     */
    public function merchant_list_sort()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $sort = request()->post('dalue');
            Db::startTrans();
            try {
                Db::name('easy_info_list')->where('id', $syid)->where('much_id', $this->much_id)->update(['sort' => $sort]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 新增商家信息
     */
    public function new_merchant_list()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data['merchant_type'] = intval(input('post.type'));
            $data['merchant_name'] = trim(input('post.name'));
            $data['merchant_icon_carousel'] = json_encode(input('post.iconCarousel/a'), 320);
            $data['address_name'] = trim(input('post.address'));
            $data['address_longitude'] = trim(input('post.longitude'));
            $data['address_latitude'] = trim(input('post.latitude'));
            $data['merchant_phone'] = trim(input('post.phone'));
            $data['merchant_introduce'] = $this->safe_html(trim(input('post.content')));
            $data['status'] = intval(input('post.status'));
            $data['create_time'] = time();
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('easy_info_list')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $eitList = Db::name('easy_info_type')->order(['sort' => 'asc', 'id' => 'asc'])->where('much_id', $this->much_id)->select();
            $this->assign('eitList', $eitList);
            return $this->fetch();
        }
    }

    /*
     * 编辑商家信息
     */
    public function edit_merchant_list()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $data['merchant_type'] = trim(input('post.type'));
            $data['merchant_name'] = trim(input('post.name'));
            $data['merchant_icon_carousel'] = json_encode(input('post.iconCarousel/a'), 320);
            $data['address_name'] = trim(input('post.address'));
            $data['address_longitude'] = trim(input('post.longitude'));
            $data['address_latitude'] = trim(input('post.latitude'));
            $data['merchant_phone'] = trim(input('post.phone'));
            $data['merchant_introduce'] = $this->safe_html(trim(input('post.content')));
            $data['status'] = trim(input('post.status'));
            Db::startTrans();
            try {
                Db::name('easy_info_list')->where('id', $fid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $fid = intval(input('get.layid'));
            $eilInfo = Db::name('easy_info_list')->where('id', $fid)->where('much_id', $this->much_id)->find();
            if ($eilInfo) {
                $eitList = Db::name('easy_info_type')->order(['sort' => 'asc', 'id' => 'asc'])->where('much_id', $this->much_id)->select();
                $this->assign('eitList', $eitList);
                $eilInfo['merchant_icon_carousel'] = json_decode($eilInfo['merchant_icon_carousel'], true);
                $eilInfo['merchant_introduce'] = base64_encode(rawurlencode($eilInfo['merchant_introduce']));
                $this->assign('list', $eilInfo);
                return $this->fetch();
            } else {
                abort(404);
            }
        }
    }

    /*
     * 删除商家信息
     */
    public function del_merchant_list()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            Db::startTrans();
            try {
                Db::name('easy_info_list')->where('id', $fid)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, array(), 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    /*
     * 信息设置
     */
    public function merchant_config()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data['custom_title'] = trim(input('post.title'));
            $data['is_show_btn'] = intval(input('post.showBtn'));
            $images = input('post.images/a', array());
            $data['btn_icon'] = trim($images[0]);
            $data['waiter_qrcode'] = trim($images[1]);
            $data['precautions'] = $this->safe_html(trim(input('post.content')));
            Db::startTrans();
            try {
                Db::name('easy_info_config')->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $eicInfo = Db::name('easy_info_config')->where('much_id', $this->much_id)->find();
            if (!$eicInfo) {
                $domain = request()->domain();
                $eicInfo = [
                    'custom_title' => '同城信息',
                    'is_show_btn' => 0,
                    'btn_icon' => "{$domain}/addons/yl_welore/web/static/applet_icon/esay.png",
                    'waiter_qrcode' => "{$domain}/addons/yl_welore/web/static/disappear/default.png",
                    'precautions' => '',
                    'much_id' => $this->much_id
                ];
                Db::startTrans();
                try {
                    $eicInfo['id'] = Db::name('easy_info_config')->insertGetId($eicInfo);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
            }
            $eicInfo['precautions'] = base64_encode(rawurlencode($eicInfo['precautions']));
            $this->assign('list', $eicInfo);
            return $this->fetch();
        }
    }

    /*
     * 店员列表
     */
    public function merchant_sales_clerk()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        $url = $this->defaultQuery();
        $list = Db::name('easy_info_shop_assistant')
            ->alias('eisa')
            ->join('easy_info_list eil', 'eisa.eil_id=eil.id')
            ->join('user us', 'eisa.user_id=us.id')
            ->where('eisa.much_id&eil.much_id&us.much_id', $this->much_id)
            ->field('eisa.id,eisa.status,eisa.create_time,eil.merchant_name,us.user_head_sculpture,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
            ->paginate(10, false, ['query' => ['s' => $url]]);
        $this->assign('list', $list);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 新增店员
     */
    public function new_merchant_sales_clerk()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data['eil_id'] = intval(input('post.eilId'));
            $eilInfo = Db::name('easy_info_list')->where('id', $data['eil_id'])->where('much_id', $this->much_id)->find();
            //  判断商家编号是否正确
            if (!$eilInfo) {
                return json(['code' => 0, 'msg' => '商家信息错误']);
            }
            $openId = trim(input('post.openId'));
            $data['user_id'] = Db::name('user')->where('user_wechat_open_id', $openId)->where('much_id', $this->much_id)->value('id');
            //  判断用户编号是否正确
            if (!$data['user_id']) {
                return json(['code' => 0, 'msg' => '用户信息错误']);
            }
            $eisaInfo = Db::name('easy_info_shop_assistant')->where('eil_id', $data['eil_id'])->where('user_id', $data['user_id'])->where('much_id', $this->much_id)->find();
            //  判断数据是否已存在
            if ($eisaInfo) {
                return json(['code' => 0, 'msg' => '数据已存在，请勿重复添加']);
            }
            $data['status'] = trim(input('post.status'));
            $data['create_time'] = time();
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('easy_info_shop_assistant')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            $eilList = Db::name('easy_info_list')->where('much_id', $this->much_id)->select();
            $this->assign('list', $eilList);
            return $this->fetch();
        }
    }

    /*
     * 更改店员状态
     */
    public function update_merchant_sales_clerk_status()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('usid');
            $status = request()->post('status');
            Db::startTrans();
            try {
                Db::name('easy_info_shop_assistant')->where('id', $usid)->where('much_id', $this->much_id)->update(['status' => $status]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 删除店员信息
     */
    public function del_merchant_sales_clerk()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = request()->post('fid');
            Db::startTrans();
            try {
                Db::name('easy_info_shop_assistant')->where('id', $fid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 商品列表
     */
    public function merchant_commodity()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        $url = $this->defaultQuery();

        $fid = intval(input('get.fid'));
        $pid = intval(input('get.pid'));
        $eid = intval(input('get.eid'));

        $when = array();
        ($fid !== 0 && $when['eisp.id'] = $fid) || ($fid === 0 && $fid = '');
        ($pid !== 0 && $when['eisp.product_id'] = $pid) || ($pid === 0 && $pid = '');
        ($eid !== 0 && $when['eisp.eil_id'] = $eid) || ($eid === 0 && $eid = '');

        $list = Db::name('easy_info_shop_products')
            ->alias('eisp')
            ->join('easy_info_list eil', 'eisp.eil_id=eil.id')
            ->join('shop sp', 'eisp.product_id=sp.id')
            ->where($when)
            ->where('eisp.much_id&eil.much_id&sp.much_id', $this->much_id)
            ->field('eisp.*,eil.merchant_name,sp.product_name')
            ->paginate(10, false, ['query' => ['s' => $url, 'fid' => $fid, 'pid' => $pid, 'eid' => $eid]]);
        $this->assign('list', $list);
        $this->assign('fid', $fid);
        $this->assign('pid', $pid);
        $this->assign('eid', $eid);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 新增商家商品
     */
    public function new_merchant_commodity()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data['product_id'] = intval(input('post.pid'));
            $eispInfo = Db::name('easy_info_shop_products')->where('product_id', $data['product_id'])->where('much_id', $this->much_id)->find();
            //  判断是否已保存
            if ($eispInfo) {
                return json(['code' => 0, 'msg' => '保存失败，商品已绑定商家']);
            }
            $data['eil_id'] = intval(input('post.eid'));
            //  查询商品类型
            $isOffline = Db::name('shop')->where('id', $data['product_id'])->where('much_id', $this->much_id)->value('is_offline');
            //  判断是否为商家商品
            if (!$isOffline) {
                return json(['code' => 0, 'msg' => '保存失败，商品类型非商家商品']);
            }
            $data['create_time'] = time();
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('easy_info_shop_products')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 删除商家商品
     */
    public function del_merchant_commodity()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = request()->post('fid');
            Db::startTrans();
            try {
                Db::name('easy_info_shop_products')->where('id', $fid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 订单列表
     */
    public function merchant_commodity_order()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        $url = $this->defaultQuery();

        $fid = intval(input('get.fid'));
        $orderNumber = trim(input('get.orderNumber'));
        $eid = intval(input('get.eid'));
        $code = trim(input('get.code'));

        $when = array();

        ($fid !== 0 && $when['eiso.id'] = $fid) || ($fid === 0 && $fid = '');
        ($eid !== 0 && $when['eiso.eil_id'] = $eid) || ($eid === 0 && $eid = '');
        ($orderNumber !== '' && $when['so.order_number'] = $orderNumber);
        ($code !== '' && $when['eiso.redemption_code'] = $code);

        $list = Db::name('easy_info_shop_order')
            ->alias('eiso')
            ->join('easy_info_list eil', 'eiso.eil_id=eil.id')
            ->join('user us', 'eiso.user_id=us.id')
            ->join('shop sp', 'eiso.product_id=sp.id')
            ->join('shop_order so', 'eiso.so_id=so.id')
            ->where($when)
            ->where('eiso.much_id&eil.much_id&us.much_id&sp.much_id&so.much_id', $this->much_id)
            ->field('eiso.*,eil.merchant_name,so.order_number,so.product_name,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
            ->order(['eiso.id' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'fid' => $fid, 'orderNumber' => $orderNumber, 'eid' => $eid, 'code' => $code]]);
        $this->assign('list', $list);
        $this->assign('fid', $fid);
        $this->assign('orderNumber', $orderNumber);
        $this->assign('eid', $eid);
        $this->assign('code', $code);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 核销列表
     */
    public function merchant_commodity_order_redeem()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'))) {
            abort(404);
        }
        $url = $this->defaultQuery();

        $fid = intval(input('get.fid'));
        $orderNumber = trim(input('get.orderNumber'));
        $eid = intval(input('get.eid'));
        $pid = intval(input('get.pid'));
        $uid = intval(input('get.uid'));

        $when = array();

        ($fid !== 0 && $when['eisovl.id'] = $fid) || ($fid === 0 && $fid = '');
        ($orderNumber !== '' && $when['so.order_number'] = $orderNumber);
        ($eid !== 0 && $when['eisovl.eil_id'] = $eid) || ($eid === 0 && $eid = '');
        ($pid !== 0 && $when['eisovl.eiso_id'] = $pid) || ($pid === 0 && $pid = '');
        ($uid !== 0 && $when['eisovl.user_id'] = $uid) || ($uid === 0 && $uid = '');


        $list = Db::name('easy_info_shop_order_verify_log')
            ->alias('eisovl')
            ->join('easy_info_shop_order eiso', 'eisovl.eiso_id=eiso.id')
            ->join('user us', 'eisovl.user_id=us.id')
            ->join('easy_info_list eil', 'eisovl.eil_id=eil.id')
            ->join('shop_order so', 'eisovl.so_id=so.id')
            ->where('eisovl.much_id&eiso.much_id&us.much_id&eil.much_id&so.much_id', $this->much_id)
            ->where($when)
            ->field('eisovl.*,eil.merchant_name,so.order_number,so.product_name,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
            ->order(['eisovl.id' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'fid' => $fid, 'orderNumber' => $orderNumber, 'eid' => $eid, 'pid' => $pid, 'uid' => $uid]]);
        $this->assign('list', $list);
        $this->assign('fid', $fid);
        $this->assign('orderNumber', $orderNumber);
        $this->assign('eid', $eid);
        $this->assign('pid', $pid);
        $this->assign('uid', $uid);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }
}