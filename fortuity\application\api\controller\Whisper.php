<?php


namespace app\api\controller;


use app\api\service\Alternative;
use app\api\service\TmplService;
use app\api\service\Util;
use think\Db;

class Whisper extends Base
{

    function getChar()  // $num为生成汉字的数量
    {
        $randomXing = array('快乐的', '冷静的', '醉熏的', '潇洒的', '糊涂的', '积极的', '冷酷的', '深情的', '粗暴的', '温柔的', '可爱的', '愉快的', '义气的', '认真的', '威武的', '帅气的', '传统的', '潇洒的', '漂亮的', '自然的', '专一的', '听话的', '昏睡的', '狂野的', '等待的', '搞怪的', '幽默的', '魁梧的', '活泼的', '开心的', '高兴的', '超帅的', '留胡子的', '坦率的', '直率的', '轻松的', '痴情的', '完美的', '精明的', '无聊的', '有魅力的', '丰富的', '繁荣的', '饱满的', '炙热的', '暴躁的', '碧蓝的', '俊逸的', '英勇的', '健忘的', '故意的', '无心的', '土豪的', '朴实的', '兴奋的', '幸福的', '淡定的', '不安的', '阔达的', '孤独的', '独特的', '疯狂的', '时尚的', '落后的', '风趣的', '忧伤的', '大胆的', '爱笑的', '矮小的', '健康的', '合适的', '玩命的', '沉默的', '斯文的', '香蕉', '苹果', '鲤鱼', '鳗鱼', '任性的', '细心的', '粗心的', '大意的', '甜甜的', '酷酷的', '健壮的', '英俊的', '霸气的', '阳光的', '默默的', '大力的', '孝顺的', '忧虑的', '着急的', '紧张的', '善良的', '凶狠的', '害怕的', '重要的', '危机的', '欢喜的', '欣慰的', '满意的', '跳跃的', '诚心的', '称心的', '如意的', '怡然的', '娇气的', '无奈的', '无语的', '激动的', '愤怒的', '美好的', '感动的', '激情的', '激昂的', '震动的', '虚拟的', '超级的', '寒冷的', '精明的', '明理的', '犹豫的', '忧郁的', '寂寞的', '奋斗的', '勤奋的', '现代的', '过时的', '稳重的', '热情的', '含蓄的', '开放的', '无辜的', '多情的', '纯真的', '拉长的', '热心的', '从容的', '体贴的', '风中的', '曾经的', '追寻的', '儒雅的', '优雅的', '开朗的', '外向的', '内向的', '清爽的', '文艺的', '长情的', '平常的', '单身的', '伶俐的', '高大的', '懦弱的', '柔弱的', '爱笑的', '乐观的', '耍酷的', '酷炫的', '神勇的', '年轻的', '唠叨的', '瘦瘦的', '无情的', '包容的', '顺心的', '畅快的', '舒适的', '靓丽的', '负责的', '背后的', '简单的', '谦让的', '彩色的', '缥缈的', '欢呼的', '生动的', '复杂的', '慈祥的', '仁爱的', '魔幻的', '虚幻的', '淡然的', '受伤的', '雪白的', '高高的', '糟糕的', '顺利的', '闪闪的', '羞涩的', '缓慢的', '迅速的', '优秀的', '聪明的', '含糊的', '俏皮的', '淡淡的', '坚强的', '平淡的', '欣喜的', '能干的', '灵巧的', '友好的', '机智的', '机灵的', '正直的', '谨慎的', '俭朴的', '殷勤的', '虚心的', '辛勤的', '自觉的', '无私的', '无限的', '踏实的', '老实的', '现实的', '可靠的', '务实的', '拼搏的', '个性的', '粗犷的', '活力的', '成就的', '勤劳的', '单纯的', '落寞的', '朴素的', '悲凉的', '忧心的', '洁净的', '清秀的', '自由的', '小巧的', '单薄的', '贪玩的', '刻苦的', '干净的', '壮观的', '和谐的', '文静的', '调皮的', '害羞的', '安详的', '自信的', '端庄的', '坚定的', '美满的', '舒心的', '温暖的', '专注的', '勤恳的', '美丽的', '腼腆的', '优美的', '甜美的', '甜蜜的', '整齐的', '动人的', '典雅的', '尊敬的', '舒服的', '妩媚的', '秀丽的', '喜悦的', '甜美的', '彪壮的', '强健的', '大方的', '俊秀的', '聪慧的', '迷人的', '陶醉的', '悦耳的', '动听的', '明亮的', '结实的', '魁梧的', '标致的', '清脆的', '敏感的', '光亮的', '大气的', '老迟到的', '知性的', '冷傲的', '呆萌的', '野性的', '隐形的', '笑点低的', '微笑的', '笨笨的', '难过的', '沉静的', '火星上的', '失眠的', '安静的', '纯情的', '要减肥的', '迷路的', '烂漫的', '哭泣的', '贤惠的', '苗条的', '温婉的', '发嗲的', '会撒娇的', '贪玩的', '执着的', '眯眯眼的', '花痴的', '想人陪的', '眼睛大的', '高贵的', '傲娇的', '心灵美的', '爱撒娇的', '细腻的', '天真的', '怕黑的', '感性的', '飘逸的', '怕孤独的', '忐忑的', '高挑的', '傻傻的', '冷艳的', '爱听歌的', '还单身的', '怕孤单的', '懵懂的');

        $randomMing = array('嚓茶', '凉面', '便当', '毛豆', '花生', '可乐', '灯泡', '哈密瓜', '野狼', '背包', '眼神', '缘分', '雪碧', '人生', '牛排', '蚂蚁', '飞鸟', '灰狼', '斑马', '汉堡', '悟空', '巨人', '绿茶', '自行车', '保温杯', '大碗', '墨镜', '魔镜', '煎饼', '月饼', '月亮', '星星', '芝麻', '啤酒', '玫瑰', '大叔', '小伙', '哈密瓜，数据线', '太阳', '树叶', '芹菜', '黄蜂', '蜜粉', '蜜蜂', '信封', '西装', '外套', '裙子', '大象', '猫咪', '母鸡', '路灯', '蓝天', '白云', '星月', '彩虹', '微笑', '摩托', '板栗', '高山', '大地', '大树', '电灯胆', '砖头', '楼房', '水池', '鸡翅', '蜻蜓', '红牛', '咖啡', '机器猫', '枕头', '大船', '诺言', '钢笔', '刺猬', '天空', '飞机', '大炮', '冬天', '洋葱', '春天', '夏天', '秋天', '冬日', '航空', '毛衣', '豌豆', '黑米', '玉米', '眼睛', '老鼠', '白羊', '帅哥', '美女', '季节', '鲜花', '服饰', '裙子', '白开水', '秀发', '大山', '火车', '汽车', '歌曲', '舞蹈', '老师', '导师', '方盒', '大米', '麦片', '水杯', '水壶', '手套', '鞋子', '自行车', '鼠标', '手机', '电脑', '书本', '奇迹', '身影', '香烟', '夕阳', '台灯', '宝贝', '未来', '皮带', '钥匙', '心锁', '故事', '花瓣', '滑板', '画笔', '画板', '学姐', '店员', '电源', '饼干', '宝马', '过客', '大白', '时光', '石头', '钻石', '河马', '犀牛', '西牛', '绿草', '抽屉', '柜子', '往事', '寒风', '路人', '橘子', '耳机', '鸵鸟', '朋友', '苗条', '铅笔', '钢笔', '硬币', '热狗', '大侠', '御姐', '萝莉', '毛巾', '期待', '盼望', '白昼', '黑夜', '大门', '黑裤', '钢铁侠', '哑铃', '板凳', '枫叶', '荷花', '乌龟', '仙人掌', '衬衫', '大神', '草丛', '早晨', '心情', '茉莉', '流沙', '蜗牛', '战斗机', '冥王星', '猎豹', '棒球', '篮球', '乐曲', '鱼', '老虎', '雨', '羽毛', '翅膀', '外套', '火', '丝袜', '书包', '钢笔', '冷风', '八宝粥', '烤鸡', '大雁', '音响', '招牌', '胡萝卜', '冰棍', '帽子', '菠萝', '蛋挞', '香水', '泥猴桃', '吐司', '溪流', '黄豆', '樱桃', '小鸽子', '小蝴蝶', '爆米花', '花卷', '小鸭子', '小海豚', '日记本', '小熊猫', '小懒猪', '小懒虫', '荔枝', '镜子', '曲奇', '金针菇', '小松鼠', '小虾米', '酒窝', '紫菜', '金鱼', '柚子', '果汁', '百褶裙', '项链', '帆布鞋', '火龙果', '奇异果', '煎蛋', '唇彩', '小土豆', '高跟鞋', '戒指', '雪糕', '睫毛', '铃铛', '手链', '香氛', '红酒', '月光', '酸奶', '银耳汤', '咖啡豆', '小蜜蜂', '小蚂蚁', '蜡烛', '棉花糖', '向日葵', '水蜜桃', '小蝴蝶', '小刺猬', '小丸子', '指甲油', '康乃馨', '糖豆', '薯片', '口红', '超短裙', '乌冬面', '冰淇淋', '棒棒糖', '长颈鹿', '豆芽', '发箍', '发卡', '发夹', '发带', '铃铛', '小馒头', '小笼包', '小甜瓜', '冬瓜', '香菇', '小兔子', '含羞草', '短靴', '睫毛膏', '小蘑菇', '跳跳糖', '小白菜', '草莓', '柠檬', '月饼', '百合', '纸鹤', '小天鹅', '云朵', '芒果', '面包', '海燕', '小猫咪', '龙猫', '唇膏', '鞋垫', '羊', '黑猫', '白猫', '万宝路', '金毛', '山水', '音响');

        $nicName = $randomXing[rand(0, count($randomXing) - 1)] . $randomMing[rand(0, count($randomMing) - 1)];

        return $nicName; // 返回生成的昵称

    }

    /**
     * 获取秘密
     */
    public function get_secret()
    {
        $data = input('param.');
        $order = ['s.send_time' => 'desc'];
        $where = array();
        switch ($data['sort_index']) {
            case '1':
                $order = ['p.reply_time' => 'desc'];
                break;
            case '2':
                $order = ['s.praise_number' => 'desc'];
                break;
            case '3':
                $where['s.user_id'] = ['eq', $this->user_info['id']];
                break;
            case '4':
                $where['s.at_user_id'] = ['eq', $this->user_info['id']];
                break;
        }

        $list = Db::name('sprout')->alias('s')
            ->join('sprout_reply p', 'p.sp_id=s.id', 'LEFT')
            ->where('s.much_id', $data['much_id'])
            ->where('s.status', 1)
            ->where('s.is_del', 0)
            ->where($where)
            ->field('s.id,s.templet_id,s.content,s.at_user_id,s.praise_number,s.send_time,s.status,s.temp_name')
            ->order($order)
            ->group('s.id')
            ->page($data['page'], 10)
            ->select();
        $domain = explode(':', $_SERVER['HTTP_HOST']);
        $absRess = explode("index.php", $_SERVER['SCRIPT_NAME']);
        foreach ($list as $k => $v) {
            if ($v['at_user_id'] != 0) {
                $user = Db::name('user')->where('id', $v['at_user_id'])->where('much_id', $data['much_id'])->field('id,user_nick_name')->find();
                $list[$k]['user_name'] = emoji_decode($user['user_nick_name']);
                $list[$k]['user_id'] = $user['id'];
            } else {
                $list[$k]['user_name'] = '';
                $list[$k]['user_id'] = 0;
            }
            $list[$k]['send_time'] = formatTime($v['send_time']);

            $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
            preg_match_all($preg, emoji_decode($v['content']), $match);
            if (!empty($match[0])) {
                $img = array();
                foreach ($match[1] as $a => $b) {
                    $img[$a] = htmlspecialchars_decode($b);
                }
                $list[$k]['image_part'] = $img;
            } else {
                $list[$k]['image_part'] = array();
            }

            $list[$k]['content'] = strip_tags(emoji_decode($v['content']));
            $list[$k]['content'] = Alternative::ExpressionHtml($list[$k]['content']);
            //评论数量
            $list[$k]['reply_content'] = Db::name('sprout_reply')
                ->where('sp_id', $v['id'])
                ->where('status', 1)
                ->where('is_del', 0)
                ->count();
            //查询是否点赞该说说
            $list[$k]['applaud'] = Db::name('user_applaud')
                ->where('paper_id', $v['id'])
                ->where('user_id', $this->user_info['id'])
                ->where('applaud_type', 2)
                ->count();
            $payReactURLReplace = "https://{$domain[0]}{$absRess[0]}static/sprout_icon/" . substr($v['id'], -1) . '.png';
            $payReactURL = str_replace('\\', '/', $payReactURLReplace);
            $list[$k]['reply_head'] = $payReactURL;

            $nike_name = $this->getChar();
            if (empty($v['temp_name'])) {
                Db::name('sprout')->where('id', $v['id'])->update(['temp_name' => $nike_name]);
                $list[$k]['temp_name'] = $nike_name;
            }
        }
        //获取数量
        $count = Db::name('sprout')
            ->where('status', 1)
            ->where('is_del', 0)
            ->where('much_id', $data['much_id'])
            ->count();
        $setting = Db::name('paper_smingle')->where('much_id', $data['much_id'])->find();
        $info['custom_hiss_title'] = empty($setting['custom_hiss_title']) ? '树洞' : $setting['custom_hiss_title'];
        $info['count'] = formatNumber($count);
        return $this->json_rewrite(['list' => $list, 'info' => $info]);
    }

    public function get_secret_admin()
    {
        $data = input('param.');
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            return $this->json_rewrite(['list' => []]);
        }
        $list = Db::name('sprout')->alias('s')
            ->where('s.much_id', $data['much_id'])
            ->where('s.status', 0)
            ->where('s.is_del', 0)
            ->field('s.id,s.templet_id,s.content,s.at_user_id,s.praise_number,s.send_time,s.status,s.temp_name')
            ->group('s.id')
            ->page($data['page'], 10)
            ->select();
        $domain = explode(':', $_SERVER['HTTP_HOST']);
        $absRess = explode("index.php", $_SERVER['SCRIPT_NAME']);
        foreach ($list as $k => $v) {
            if ($v['at_user_id'] != 0) {
                $user = Db::name('user')->where('id', $v['at_user_id'])->where('much_id', $data['much_id'])->field('id,user_nick_name')->find();
                $list[$k]['user_name'] = emoji_decode($user['user_nick_name']);
                $list[$k]['user_id'] = $user['id'];
            } else {
                $list[$k]['user_name'] = '';
                $list[$k]['user_id'] = 0;
            }
            $list[$k]['send_time'] = formatTime($v['send_time']);

            $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
            preg_match_all($preg, emoji_decode($v['content']), $match);
            if (!empty($match[0])) {
                $img = array();
                foreach ($match[1] as $a => $b) {
                    $img[$a] = htmlspecialchars_decode($b);
                }
                $list[$k]['image_part'] = $img;
            } else {
                $list[$k]['image_part'] = array();
            }

            $list[$k]['content'] = strip_tags(emoji_decode($v['content']));
            $list[$k]['content'] = Alternative::ExpressionHtml($list[$k]['content']);
            //评论数量
            $list[$k]['reply_content'] = Db::name('sprout_reply')
                ->where('sp_id', $v['id'])
                ->where('status', 1)
                ->where('is_del', 0)
                ->count();
            //查询是否点赞该说说
            $list[$k]['applaud'] = Db::name('user_applaud')
                ->where('paper_id', $v['id'])
                ->where('user_id', $this->user_info['id'])
                ->where('applaud_type', 2)
                ->count();
            $payReactURLReplace = "https://{$domain[0]}{$absRess[0]}static/sprout_icon/" . substr($v['id'], -1) . '.png';
            $payReactURL = str_replace('\\', '/', $payReactURLReplace);
            $list[$k]['reply_head'] = $payReactURL;

            $nike_name = $this->getChar();
            if (empty($v['temp_name'])) {
                Db::name('sprout')->where('id', $v['id'])->update(['temp_name' => $nike_name]);
                $list[$k]['temp_name'] = $nike_name;
            }
        }
        return $this->json_rewrite(['list' => $list]);
    }

    /**
     * 小秘密详情
     */
    public function get_secret_info()
    {
        $data = input('param.');
        $info = Db::name('sprout')->alias('s')
            ->where('s.much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->where('s.is_del', 0)
            ->field('s.id,s.content,s.at_user_id,s.praise_number,s.send_time,s.status,s.temp_name')
            ->find();
        $domain = explode(':', $_SERVER['HTTP_HOST']);
        $absRess = explode("index.php", $_SERVER['SCRIPT_NAME']);
        $payReactURLReplace = "https://{$domain[0]}{$absRess[0]}static/sprout_icon/" . substr($info['id'], -1) . '.png';
        $payReactURL = str_replace('\\', '/', $payReactURLReplace);
        $info['reply_head'] = $payReactURL;
        $info['send_time'] = formatTime($info['send_time']);
        $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
        preg_match_all($preg, emoji_decode($info['content']), $match);
        if (!empty($match[0])) {
            $img = array();
            foreach ($match[1] as $a => $b) {
                $img[$a] = htmlspecialchars_decode($b);
            }
            $info['image_part'] = $img;
        } else {
            $info['image_part'] = array();
        }
        $info['content'] = strip_tags(emoji_decode($info['content']));
        $info['content'] = Alternative::ExpressionHtml($info['content']);
        //评论数量
        $info['reply_content'] = Db::name('sprout_reply')
            ->where('sp_id', $info['id'])
            ->where('status', 1)
            ->where('is_del', 0)
            ->count();
        //查询是否点赞该说说
        $info['applaud'] = Db::name('user_applaud')
            ->where('paper_id', $info['id'])
            ->where('user_id', $this->user_info['id'])
            ->where('applaud_type', 2)
            ->count();
        if ($info['at_user_id'] != 0) {
            $user = Db::name('user')->where('id', $info['at_user_id'])->where('much_id', $data['much_id'])->field('id,user_nick_name')->find();
            $info['user_name'] = emoji_decode($user['user_nick_name']);
            $info['user_id'] = $user['id'];
        } else {
            $info['user_name'] = '';
            $info['user_id'] = 0;
        }
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        $info['admin'] = $cg;
        return $this->json_rewrite($info);
    }

    /**
     * 发布小秘密
     */
    public function add_secret()
    {
        $data = input('param.');

        //内容为空
        if (empty(preg_replace('# #', '', $data['secret']))) {
            $rs = ['code' => '1', 'msg' => '内容不能为空'];
            return $this->json_rewrite($rs);
        }
        //验证内容
        $util = new Util();
        $check_msg = $util->get_check_msg($data['secret'], $data['much_id'], $data['openid']);
        if ($check_msg['code'] == 1) {
            return $this->json_rewrite($check_msg);
        }
        //查询发布次数限制
        $paper_sim = Db::name('paper_smingle')
            ->where('much_id', $data['much_id'])
            ->field('auto_hiss,hiss_limit,reply_auto_hiss,reply_hiss_limit,custom_hiss_title')
            ->find();
        //hiss_limit 发布次数
        if ($paper_sim['hiss_limit'] != 0) {
            //查询我今天发布的
            $check_sprout = Db::name('sprout')
                ->where('user_id', $this->user_info['id'])
                ->whereTime('send_time', 'today')
                ->where('much_id', $data['much_id'])
                ->count();
            if ($check_sprout >= $paper_sim['hiss_limit']) {
                return $this->json_rewrite(['code' => 1, 'msg' => '今日发布已达上限！']);
            }
        }
        $d['templet_id'] = '';
        $d['user_id'] = $this->user_info['id'];
        $d['at_user_id'] = $data['user_info'];
        $d['content'] = emoji_encode($data['secret']);
        $d['praise_number'] = 0;
        $d['status'] = $paper_sim['auto_hiss'];
        $d['send_time'] = time();
        $d['check_time'] = $paper_sim['auto_hiss'] == 0 ? 0 : time();
        $d['reject_reason'] = '';
        $d['is_del'] = 0;
        $d['much_id'] = $data['much_id'];
        $sql_content = '';
        if ($data['img_arr']) {
            $data['img_arr'] = json_decode($data['img_arr'], true);
            foreach ($data['img_arr'] as $k => $v) {
                $sql_content .= "<img src='{$v}' >";
            }
        }
        $d['content'] .= $sql_content;
        $res = Db::name('sprout')->insert($d);
        if (!$res) {
            return $this->json_rewrite(['code' => 1, 'msg' => '发布失败，请稍候重试！']);
        }
        $msg = $paper_sim['auto_hiss'] == 0 ? '请等待审核' : '发布成功';
        if ($paper_sim['auto_hiss'] == 1 && $data['user_info'] != '') {
            //发送模版
            $util->add_template(['much_id' => $data['much_id'],
                'at_id' => 'YL0009',
                'user_id' => $data['user_info'],
                'page' => 'yl_welore/pages/packageD/whisper/index',
                'keyword1' => 'Ta给你发了一条' . $paper_sim['custom_hiss_title'],
                'keyword2' => date('Y年m月d日 H:i:s', time())
            ]);

        }
        if ($paper_sim['auto_hiss'] == 0) {
            Db::name('prompt_msg')->insert(['capriole' => 14, 'tyid' => 0, 'msg_time' => time(), 'type' => 0, 'retter' => '用户：【' . $this->user_info['user_nick_name'] . '】发布了一条' . $paper_sim['custom_hiss_title'] . '待审核！', 'status' => 0, 'much_id' => $data['much_id']]);
            $notices = Db::name('prompt_msg')
                ->where('status', 0)
                ->where('type', 0)
                ->where('much_id', $data['much_id'])
                ->count('*');
            cache('notices_' . $data['much_id'], $notices);
        }
        return $this->json_rewrite(['code' => 0, 'msg' => $msg]);
    }

    /**
     * 搜索出来的
     */
    public function get_search_list()
    {
        $data = input('param.');
        $search = emoji_encode(preg_replace('# #', '', $data['search']));
        $list = Db::name('user')
            ->where('tourist', 0)
            ->where('uvirtual', 0)
            ->where('status', 1)
            ->where('much_id', $data['much_id'])
            ->where('user_nick_name', 'LIKE', "%{$search}%")
            ->where('id', 'neq', $this->user_info['id'])
            ->field('id,user_nick_name,user_head_sculpture,autograph')
            ->page($data['page'], 30)
            ->select();
        foreach ($list as $k => $v) {
            $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            $list[$k]['autograph'] = emoji_decode($v['autograph']);
        }

        return $this->json_rewrite($list);
    }

    /**
     * 我关注的
     */
    public function get_my_search_list()
    {
        $data = input('param.');
        //我关注的用户
        $my_list = Db::name('user_track')->alias('t')
            ->join('user u', 'u.id=t.qu_user_id')
            ->where('at_user_id', $this->user_info['id'])
            ->where('u.tourist', 0)
            ->where('u.uvirtual', 0)
            ->where('u.status', 1)
            ->where('t.much_id', $data['much_id'])
            ->order('fo_time')
            ->field('u.id,u.user_nick_name,u.user_head_sculpture,u.autograph')
            ->page($data['page'], 30)
            ->select();
        foreach ($my_list as $k => $v) {
            $my_list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            $my_list[$k]['autograph'] = emoji_decode($v['autograph']);
        }
        return $this->json_rewrite($my_list);
    }

    /**
     * 赞小秘密
     */
    public function secret_zan()
    {
        $data = input('param.');
        //查询是否关注
        $check = Db::name('user_applaud')
            ->where('paper_id', $data['id'])
            ->where('user_id', $this->user_info['id'])
            ->where('applaud_type', 2)
            ->count();
        if ($check == 0) { //未关注
            Db::name('user_applaud')->insert(['paper_id' => $data['id'], 'user_id' => $this->user_info['id'], 'applaud_type' => 2, 'laud_time' => time(), 'much_id' => $data['much_id']]);
            Db::name('sprout')->where('id', $data['id'])->where('much_id', $data['much_id'])->setInc('praise_number');
        } else {
            Db::name('user_applaud')
                ->where('paper_id', $data['id'])
                ->where('user_id', $this->user_info['id'])
                ->where('applaud_type', 2)
                ->delete();
            Db::name('sprout')->where('id', $data['id'])->where('much_id', $data['much_id'])->setDec('praise_number');
        }
        return $this->json_rewrite(['code' => 0, 'msg' => '赞！']);
    }

    /**
     * 回复小秘密
     */
    public function secret_hui()
    {
        $data = input('param.');
        //内容为空
        if (empty(preg_replace('# #', '', $data['hui']))) {
            $rs = ['code' => '1', 'msg' => '内容不能为空'];
            return $this->json_rewrite($rs);
        }
        //验证内容
        $util = new Util();
        $check_msg = $util->get_check_msg($data['hui'], $data['much_id'], $data['openid']);
        if ($check_msg['code'] == 1) {
            return $this->json_rewrite($check_msg);
        }
        $paper_sim = Db::name('paper_smingle')
            ->where('much_id', $data['much_id'])
            ->field('auto_hiss,hiss_limit,reply_auto_hiss,reply_hiss_limit,custom_hiss_title')
            ->find();
        //hiss_limit 发布次数
        if ($paper_sim['reply_hiss_limit'] != 0) {
            //查询我今天发布的
            $check_sprout = Db::name('sprout_reply')
                ->where('sp_id', $data['id'])
                ->where('re_user_id', $this->user_info['id'])
                ->whereTime('reply_time', 'today')
                ->where('much_id', $data['much_id'])
                ->count();
            if ($check_sprout >= $paper_sim['reply_hiss_limit']) {
                return $this->json_rewrite(['code' => 1, 'msg' => '今日回复' . $paper_sim['custom_hiss_title'] . '已达上限！']);
            }
        }
        $d['sp_id'] = $data['id'];
        $d['re_user_id'] = $this->user_info['id'];
        $d['re_content'] = emoji_encode($data['hui']);
        $d['status'] = $paper_sim['reply_auto_hiss'];
        $d['check_time'] = $paper_sim['reply_auto_hiss'] == 0 ? 0 : time();
        $d['reply_time'] = time();
        $d['is_del'] = 0;
        $d['reject_reason'] = '';
        $d['much_id'] = $data['much_id'];
        $res = Db::name('sprout_reply')->insert($d);
        if (!$res) {
            return $this->json_rewrite(['code' => 1, 'msg' => '回复失败，请稍候重试！']);
        }
        $msg = '回复成功！';
        if ($paper_sim['reply_auto_hiss'] == 0) {
            Db::name('prompt_msg')->insert(['capriole' => 15, 'tyid' => 0, 'msg_time' => time(), 'type' => 0, 'retter' => '用户【' . $this->user_info['user_nick_name'] . '】回复了一条' . $paper_sim['custom_hiss_title'] . '待审核！', 'status' => 0, 'much_id' => $data['much_id']]);
            $notices = Db::name('prompt_msg')
                ->where('status', 0)
                ->where('type', 0)
                ->where('much_id', $data['much_id'])
                ->count('*');
            cache('notices_' . $data['much_id'], $notices);
            $msg = '请等待审核！';
        }
        //查询小秘密发布人
        $sprout = Db::name('sprout')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        $page_title = subtext($sprout['content'], 10);
        $page_title = strip_tags(emoji_decode($page_title));

        //发送模版
        $util->add_template(['much_id' => $data['much_id'],
            'at_id' => 'YL0004',
            'user_id' => $sprout['user_id'],
            'page' => 'yl_welore/pages/packageD/whisper/index',
            'keyword1' => "【" . $paper_sim['custom_hiss_title'] . "】" . $page_title,
            'keyword2' => "匿名",
            'keyword3' => subtext($data['hui'], 15),
            'keyword4' => date('Y-m-d H:i', time()),
        ]);
        return $this->json_rewrite(['code' => 0, 'msg' => $msg]);
    }

    /**
     * 获取回复列表
     */
    public function get_reply()
    {
        $data = input('param.');
        //查询我今天发布的
        $sprout = Db::name('sprout_reply')
            ->where('sp_id', $data['id'])
            ->where('status', 1)
            ->where('is_del', 0)
            ->where('much_id', $data['much_id'])
            ->field('id,re_content,reply_time,re_user_id,temp_name')
            ->order('reply_time desc')
            ->page($data['page'], 15)
            ->select();
        $domain = explode(':', $_SERVER['HTTP_HOST']);
        $absRess = explode("index.php", $_SERVER['SCRIPT_NAME']);
        foreach ($sprout as $k => $v) {
            if ($v['re_user_id'] == $this->user_info['id']) {
                $sprout[$k]['re_user_id'] = 1;
            } else {
                $sprout[$k]['re_user_id'] = 0;
            }
            $sprout[$k]['re_content'] = strip_tags(emoji_decode($v['re_content']));
            $sprout[$k]['re_content'] = Alternative::ExpressionHtml($sprout[$k]['re_content']);
            $sprout[$k]['reply_time'] = formatTime($v['reply_time']);
            $payReactURLReplace = "https://{$domain[0]}{$absRess[0]}static/secret_icon/" . substr($v['id'], -1) . '.png';
            $payReactURL = str_replace('\\', '/', $payReactURLReplace);
            $sprout[$k]['reply_head'] = $payReactURL;
            $nike_name = $this->getChar();
            if (empty($v['temp_name'])) {
                Db::name('sprout_reply')->where('id', $v['id'])->update(['temp_name' => $nike_name]);
                $sprout[$k]['temp_name'] = $nike_name;
            }

        }
        return $this->json_rewrite($sprout);
    }

    /**
     * 删除回复
     */
    public function del_reply()
    {
        $data = input('param.');
        $check = Db::name('sprout_reply')
            ->where('id', $data['id'])
            ->find();
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($check['re_user_id'] != $this->user_info['id'] && $cg == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '您没有权限！']);
        }
        if ($check['is_del'] == 1) {
            return $this->json_rewrite(['code' => 1, 'msg' => '当前回复已被删除！']);
        }
        $res = Db::name('sprout_reply')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['is_del' => 1]);
        if ($res === false) {
            return $this->json_rewrite(['code' => 1, 'msg' => '删除失败，请稍候重试！']);
        }
        return $this->json_rewrite(['code' => 0, 'msg' => '删除成功！']);
    }

    /**
     * 删除我的秘密
     */
    public function del_my_secret()
    {
        $data = input('param.');
        $check = Db::name('sprout')
            ->where('id', $data['id'])
            ->find();
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($check['user_id'] != $this->user_info['id'] && $cg == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '您没有权限！']);
        }
        if ($check['is_del'] == 1) {
            return $this->json_rewrite(['code' => 1, 'msg' => '当前内容已被删除！']);
        }
        $res = Db::name('sprout')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['is_del' => 1]);
        if ($res === false) {
            return $this->json_rewrite(['code' => 1, 'msg' => '删除失败，请稍候重试！']);
        }
        return $this->json_rewrite(['code' => 0, 'msg' => '删除成功！']);
    }


    /**
     * 审核DO
     */
    public function status_ok()
    {
        $data = input('param.');
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '您没有权限！']);
        }
        $util = new Util();
        if ($data['tab_cur_tow'] == 0) {
            $check = Db::name('sprout')
                ->where('id', $data['id'])
                ->find();
            if ($check['is_del'] == 1) {
                return $this->json_rewrite(['code' => 1, 'msg' => '当前内容已被删除！']);
            }
            $status = $data['key'];
            $res = Db::name('sprout')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['status' => $status, 'reject_reason' => $data['reject_reason'], 'check_time' => time()]);
            if ($res === false) {
                return $this->json_rewrite(['code' => 1, 'msg' => '审核失败，请稍候重试！']);
            }
            if ($status == 1 && $check['at_user_id'] != 0) {
                $paper_sim = Db::name('paper_smingle')
                    ->where('much_id', $data['much_id'])
                    ->field('auto_hiss,hiss_limit,reply_auto_hiss,reply_hiss_limit,custom_hiss_title')
                    ->find();
                //发送模版
                $util->add_template(['much_id' => $data['much_id'],
                    'at_id' => 'YL0009',
                    'user_id' => $check['at_user_id'],
                    'page' => 'yl_welore/pages/packageD/whisper/index',
                    'keyword1' => 'Ta给你发了一条' . $paper_sim['custom_hiss_title'],
                    'keyword2' => date('Y年m月d日 H:i:s', time())
                ]);
            }
            return $this->json_rewrite(['code' => 0, 'msg' => '审核成功！']);
        } else {
            $status = $data['key'];
            $res = Db::name('sprout_reply')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['status' => $status, 'reject_reason' => $data['reject_reason'], 'check_time' => time()]);
            if ($res === false) {
                return $this->json_rewrite(['code' => 1, 'msg' => '审核失败，请稍候重试！']);
            }
            return $this->json_rewrite(['code' => 0, 'msg' => '审核成功！']);
        }

    }

    /**
     * 获取审核所有回复
     */
    public function get_reply_all()
    {
        $data = input('param.');
        //查询我今天发布的
        $sprout = Db::name('sprout_reply')
            ->where('status', 0)
            ->where('is_del', 0)
            ->where('much_id', $data['much_id'])
            ->field('id,re_content,reply_time,re_user_id,temp_name')
            ->order('reply_time desc')
            ->page($data['page'], 15)
            ->select();
        $domain = explode(':', $_SERVER['HTTP_HOST']);
        $absRess = explode("index.php", $_SERVER['SCRIPT_NAME']);
        foreach ($sprout as $k => $v) {
            if ($v['re_user_id'] == $this->user_info['id']) {
                $sprout[$k]['re_user_id'] = 1;
            } else {
                $sprout[$k]['re_user_id'] = 0;
            }
            $sprout[$k]['re_content'] = strip_tags(emoji_decode($v['re_content']));
            $sprout[$k]['re_content'] = Alternative::ExpressionHtml($sprout[$k]['re_content']);
            $sprout[$k]['reply_time'] = formatTime($v['reply_time']);
            $payReactURLReplace = "https://{$domain[0]}{$absRess[0]}static/secret_icon/" . substr($v['id'], -1) . '.png';
            $payReactURL = str_replace('\\', '/', $payReactURLReplace);
            $sprout[$k]['reply_head'] = $payReactURL;
            $nike_name = $this->getChar();
            if (empty($v['temp_name'])) {
                Db::name('sprout_reply')->where('id', $v['id'])->update(['temp_name' => $nike_name]);
                $sprout[$k]['temp_name'] = $nike_name;
            }

        }
        return $this->json_rewrite($sprout);
    }

    /**
     * 获取列表
     */
    public function get_v_list()
    {
        $data = input('param.');
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        $info = Db::name('paper')->alias('p')
            ->where('p.whether_delete', '0')
            ->where('p.id', $data['id'])
            ->field('p.user_id')
            ->find();
        $review_where = array();
        if ($cg == 0) {
            $review_where['r.is_show'] = ['eq', '1'];
        }
        if ($info['user_id'] == $this->user_info['id']) {
            $review_where['r.is_show'] = ['in', '0,1'];
        }
        $review_score = Db::name('paper_review_score')->alias('r')
            ->join('user u', 'u.id=r.user_id')
            ->where('r.pa_id', $data['id'])
            ->where('r.much_id', $data['much_id'])
            ->where($review_where)
            ->where('r.audit_status', 1)
            ->field('r.id,r.user_id,r.is_show,r.assess_score,r.assess_content,u.user_head_sculpture,u.user_nick_name')
            ->page($data['page'], 10)
            ->order('r.assess_time desc')
            ->select();
        if (!empty($review_score)) {
            foreach ($review_score as $k => $v) {
                $review_score[$k]['assess_content'] = emoji_decode($v['assess_content']);
                $review_score[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            }
        }
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        return $this->json_rewrite(['info' => $review_score, 'admin' => $cg]);
    }

    /**
     * 获取列表
     */
    public function get_s_list()
    {
        $data = input('param.');
        //获取当前用户是否是超管
        $review_score = Db::name('paper_review_score')->alias('r')
            ->join('user u', 'u.id=r.user_id')
            ->where('r.much_id', $data['much_id'])
            ->where('r.audit_status', 0)
            ->field('r.id,r.user_id,r.is_show,r.assess_score,r.assess_content,u.user_head_sculpture,u.user_nick_name')
            ->page($data['page'], 10)
            ->order('r.assess_time desc')
            ->select();
        if (!empty($review_score)) {
            foreach ($review_score as $k => $v) {
                $review_score[$k]['assess_content'] = emoji_decode($v['assess_content']);
                $review_score[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            }
        }
        return $this->json_rewrite(['info' => $review_score]);
    }

    /**
     * 获取待认证的认证列表
     */
    public function acquire()
    {
        $data = input('param.');
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '没有权限！']);
        }
        $list = Db::name('user_attest')->alias('a')
            ->join('user u', 'u.id=a.user_id')
            ->where('a.much_id', $data['much_id'])
            ->where('a.adopt_status', 0)
            ->page($data['page'], 15)
            ->field('a.id,a.at_id,a.postback_data,u.user_head_sculpture,u.user_nick_name')
            ->select();
        foreach ($list as $k => $v) {
            $attest = Db::name('attest')->where('id', $v['at_id'])->where('much_id', $data['much_id'])->field('at_name,at_icon')->find();
            $list[$k]['attest'] = $attest;
            $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            $list[$k]['postback_data'] = json_decode($v['postback_data'], true);
        }
        return $this->json_rewrite($list);
    }

    /**
     * 审核通过
     */
    public function ok_submit_do()
    {
        $data = input('param.');
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '没有权限！']);
        }

        $acid = $data['acid'];
        $uatInfo = Db::name('user_attest')
            ->alias('uat')
            ->join('attest at', 'uat.at_id = at.id', 'left')
            ->where('uat.id', $acid)
            ->where('uat.much_id&at.much_id', $data['much_id'])
            ->field('at.at_name,at.handsel_day,uat.user_id,uat.ut_inject,uat.adopt_status')
            ->find();
        //  判断审核状态
        if (intval($uatInfo['adopt_status']) !== 0) {
            return $this->json_rewrite(['code' => 0, 'msg' => '系统繁忙，请刷新页面后重试！']);
        }
        $uatInfo['handsel_day'] = intval($uatInfo['handsel_day']);
        $d['adopt_status'] = $data['process'];
        $d['ut_inject'] = trim($data['ut_inject']);
        $d['refuse_time'] = time();
        Db::startTrans();
        try {
            //  审核通过 并且赠送会员时间大于0天
            if ($d['adopt_status'] == 1 && $uatInfo['handsel_day'] > 0) {
                //  用户会员到期时间
                $userVipEndTime = Db::name('user')->where('id', $uatInfo['user_id'])->where('much_id', $data['much_id'])->value('vip_end_time');
                //  获取当前时间戳
                $nowTime = time();
                //  如果用户没开通会员或用户的会员已过期则更新为当前时间
                if ($userVipEndTime <= $nowTime) {
                    //  更新为当前时间
                    $userVipEndTime = $nowTime;
                }
                $time = $userVipEndTime + ($uatInfo['handsel_day'] * 86400);
                //  赠送会员时长
                Db::name('user')->where('id', $uatInfo['user_id'])->where('much_id', $data['much_id'])->update(['vip_end_time' => $time]);
            }
            //  认证审核信息
            Db::name('user_attest')->where('id', $acid)->where('much_id', $data['much_id'])->update($d);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return $this->json_rewrite(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
        }
        if ($d['adopt_status'] >= 1 && $d['adopt_status'] <= 2) {
            $marryingText = "审核未通过";
            if ($d['adopt_status'] == 1) {
                $marryingText = "审核已通过";
            }
            $tmplData = [
                'much_id' => $data['much_id'],
                'at_id' => 'YL0011',
                'user_id' => $uatInfo['user_id'],
                'page' => 'yl_welore/pages/user_smail/index',
                'keyword1' => date('Y年m月d日 H:i:s', time()),
                'keyword2' => "{$uatInfo['at_name']}",
                'keyword3' => $marryingText,
                'keyword4' => "{$uatInfo['ut_inject']}",
            ];
            $tmplService = new TmplService();
            $tmplService->add_template($tmplData);
            Db::name('user_smail')->insert(['user_id' => $uatInfo['user_id'], 'maring' => "您申请的：{{$uatInfo['at_name']}}，{$marryingText}！", 'clue_time' => time(), 'status' => 0, 'much_id' => $data['much_id']]);
        }
        return json(['code' => 1, 'msg' => '操作成功']);
    }

    /**
     * 圈子待审核列表
     */
    public function circle_s_list()
    {
        $data = input('param.');
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '没有权限！']);
        }
        $list = Db::name('territory_petition')->alias('p')
            ->join('user u', 'u.id=p.user_id')
            ->where('p.much_id', $data['much_id'])
            ->where('p.realm_status', 0)
            ->page($data['page'], 20)
            ->field('p.id,p.realm_icon,p.realm_name,p.needle_id,p.solicit_origin,p.is_gnaw_qulord,p.attention,p.realm_synopsis,u.user_head_sculpture,u.user_nick_name')
            ->select();
        foreach ($list as $k => $v) {
            $need = Db::name('needle')->where('much_id', $data['much_id'])->where('id', $v['needle_id'])->value('name');
            $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            $list[$k]['realm_name'] = emoji_decode($v['realm_name']);
            $list[$k]['needle_id'] = $need;
        }
        return $this->json_rewrite($list);
    }

    public function circle_status()
    {
        $item = input('param.');
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $item['much_id'])->where('user_open_id', $item['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '没有权限！']);
        }
        $data['uplid'] = $item['uplid'];
        $data['pical'] = $item['pical'];
        if ($data['pical'] == 1) {
            $topn = Db::name('territory_petition')->where('id', $data['uplid'])->where('much_id', $item['much_id'])->find();
            $setory = Db::name('territory')->where('realm_name', $topn['realm_name'])->where('much_id', $item['much_id'])->find();
            if ($setory) {
                Db::startTrans();
                try {
                    Db::name('territory_petition')->where('id', $data['uplid'])->where('much_id', $item['much_id'])->update(['realm_status' => 4, 'review_lasting' => time()]);
                    Db::name('user_smail')->insert(['user_id' => $topn['user_id'], 'maring' => "很抱歉，圈子{$topn['realm_name']}已存在！", 'clue_time' => time(), 'status' => 0, 'much_id' => $item['much_id']]);
                    Db::commit();
                    return $this->json_rewrite(['code' => 0, 'msg' => '数据重复，该圈子已经存在']);
                } catch (\Exception $e) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 0, 'msg' => 'error ,' . $e->getMessage()]);
                }
            }
            if ($topn['is_gnaw_qulord'] == 1) {
                $user = Db::name('user')->where('uvirtual', 0)->where('id', $topn['user_id'])->where('much_id', $item['much_id'])->find();
                $getLear['bulord'] = "[\"{$user['user_wechat_open_id']}\"]";
            }
            $tory['realm_icon'] = $topn['realm_icon'];
            $tory['realm_name'] = $topn['realm_name'];
            $tory['needle_id'] = $topn['needle_id'];
            $tory['realm_synopsis'] = $topn['realm_synopsis'];
            $tory['attention'] = $topn['attention'];
            $tory['status'] = 0;
            $tory['concern'] = 0;
            $tory['scores'] = 0;
            $tory['rising_time'] = time();
            $tory['much_id'] = $item['much_id'];
            Db::startTrans();
            try {
                $estory = Db::name('territory')->insertGetId($tory);
                if ($topn['is_gnaw_qulord'] == 1) {
                    $getLear['tory_id'] = $estory;
                    $getLear['much_id'] = $item['much_id'];
                    Db::name('territory_learned')->insert($getLear);
                    Db::name('user_trailing')->insert(['user_id' => $user['id'], 'tory_id' => $estory, 'ling_time' => time(), 'much_id' => $item['much_id']]);
                    Db::name('territory')->where('id', $estory)->where('much_id', $item['much_id'])->setInc('concern', 1);
                }
                Db::name('territory_petition')->where('id', $data['uplid'])->where('much_id', $item['much_id'])->update(['realm_status' => $data['pical'], 'review_lasting' => time()]);

                $defaultNavigate = $this->design;

                $msgContent = "恭喜您，{$defaultNavigate['landgrave']}{$topn['realm_name']}申请已经通过审核！";
                Db::name('user_smail')->insert([
                    'user_id' => $topn['user_id'],
                    'maring' => $msgContent,
                    'clue_time' => time(),
                    'status' => 0,
                    'much_id' => $item['much_id']
                ]);
                $tmplService = new TmplService();
                $circleData = [
                    'much_id' => $item['much_id'],
                    'at_id' => 'YL0008',
                    'user_id' => $topn['user_id'],
                    'page' => 'yl_welore/pages/user_smail/index',
                    'keyword1' => $msgContent,
                    'keyword2' => '申请成功',
                    'keyword3' => date('Y年m月d日 H:i:s', time())
                ];
                $tmplService->add_template($circleData);

                //是否申请圈主
                if ($topn['is_gnaw_qulord'] == 1) {
                    $fristName = mb_substr($defaultNavigate['landgrave'], 0, 1, 'utf-8');
                    $appointContent = "您已被系统管理员任命为<{$topn['realm_name']}>{$fristName}主！";
                    Db::name('user_smail')->insert([
                        'user_id' => $topn['user_id'],
                        'maring' => $appointContent,
                        'clue_time' => time() + 1,
                        'status' => 0,
                        'much_id' => $item['much_id']
                    ]);
                    $jobData = [
                        'much_id' => $item['much_id'],
                        'at_id' => 'YL0010',
                        'user_id' => $topn['user_id'],
                        'page' => 'yl_welore/pages/user_smail/index',
                        'keyword1' => $appointContent,
                        'keyword2' => '职位任命',
                    ];
                    $tmplService->add_template($jobData);
                }
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return $this->json_rewrite(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return $this->json_rewrite(['code' => 1, 'msg' => '操作成功']);
            }
        } else {
            Db::startTrans();
            try {
                Db::name('territory_petition')->where('id', $data['uplid'])->where('much_id', $item['much_id'])->update(['realm_status' => $data['pical'], 'review_lasting' => time()]);
                $toryTion = Db::name('territory_petition')->where('id', $data['uplid'])->where('much_id', $item['much_id'])->find();
                Db::name('user_smail')->insert([
                    'user_id' => $toryTion['user_id'],
                    'maring' => "很抱歉，圈子{$toryTion['realm_name']}申请已被拒绝！",
                    'clue_time' => time(),
                    'status' => 0,
                    'much_id' => $item['much_id']
                ]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return $this->json_rewrite(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result !== false) {
                return $this->json_rewrite(['code' => 1, 'msg' => '操作成功']);
            }
        }
    }
}