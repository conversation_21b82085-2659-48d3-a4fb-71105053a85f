<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:88:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/systems/warrior.html";i:1635151619;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755608968;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-copyright"></span> 站点配置
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">站点名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="title" value="<?php echo $knight['title']; ?>" placeholder="请输入站点名称">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">Logo</label>
                        <div class="am-u-sm-9">
                            <img src="<?php echo $knight['sgraph']; ?>" id="shion" onerror="this.src='static/disappear/default.png'" onclick="cuonice();" style="width: 70px;height: 70px;cursor: pointer;"/>
                            <button type="button" style="margin-left:10px;font-size: 12px;" onclick="cuonice();">选择图片</button>
                            <small style="margin-left: 10px;">建议图片尺寸：200*200px</small>
                            <input type="hidden" value="<?php echo $knight['sgraph']; ?>" name="sngimg">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">客服电话</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="phone" value="<?php echo $knight['cust_phone']; ?>" placeholder="请输入客服电话">
                        </div>
                    </div>


                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">版权信息</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="copyright" value="<?php echo $knight['copyright']; ?>" placeholder="请输入版权信息">
                            <?php if($autonomy == 0): ?>
                            <small style="color:red;">仅在小程序中展示</small>
                            <?php endif; ?>
                        </div>
                    </div>


                    <div class="am-form-group">
                        <div class="am-u-sm-6 am-u-sm-push-6">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<script src="./assets/js/base-enhanced.js"></script>
<script>
var baseConfig = {
    urls: {
        proxyBase: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        proxyResource: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        repairMissing: "<?php echo url('index/repairMissing'); ?>",
        purgeCache: "<?php echo url('index/purgeCache'); ?>",
        ordinary: "<?php echo url('ordinary'); ?>",
        receipt: "<?php echo url('receipt'); ?>"
    },
    menuActive: '<?php echo $menuActive; ?>',
    menuList: '<?php echo $menuList; ?>',
    much_role: '<?php echo $much_role; ?>',
    notice: <?php echo $notice; ?>,
    vacant: <?php echo $vacant; ?>
};
BaseEnhanced.init(baseConfig);
window.retakeCache = BaseEnhanced.retakeCache;
window.getOriginalSrc = BaseEnhanced.getOriginalSrc;
</script>
<?php if($much_role=='founder'): ?>
<script>
window.updateFix = function() {
    layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
        btn: ['确定', '取消'],
        title: '提示'
    }, function() {
        // 执行修复操作
        performDatabaseRepair();
    }, function(index) {
        // 取消操作
        layer.close(index);
    });
};

function performDatabaseRepair() {
    var loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });
    $.post("<?php echo url('index/repairMissing'); ?>", function(data) {
        layer.close(loadingIndex);
        if (data !== false) {
            layer.msg('数据库缺失字段修复完成', {
                icon: 1,
                time: 1500
            }, function() {
                location.reload();
            });
        } else {
            layer.msg('修复失败，请稍后重试', {
                icon: 2,
                time: 2000
            }, function() {
                location.reload();
            });
        }
    }).error(function() {
        layer.close(loadingIndex);
        layer.msg('网络错误，修复失败', {
            icon: 2,
            time: 2000
        });
    });
}
</script>
<?php endif; ?>

<script>
    !function () {
        $('.tpl-switch').find('.tpl-switch-btn-view').on('click', function () {
            $(this).prev('.tpl-switch-btn').prop("checked", function () {
                if ($(this).is(':checked')) {
                    return false;
                } else {
                    return true;
                }
            });
        });
    }();


    function cuonice() {
        layer.open({
            type: 2,
            anim:2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["<?php echo url('images/dialogImages'); ?>&gclasid=0", 'no']
        });
    }

    function sutake(eurl) {
        $('#shion').attr('src', eurl);
        $("[name='sngimg']").val(eurl);
        layer.closeAll();
    }
    function excheck(title, sngimg, phone, copyright) {
        if (title == '' || title == 'undefined' || title == null) {
            layer.msg('站点名称不能为空');
            return false;
        }
        if (sngimg == '' || sngimg == 'undefined' || sngimg == null) {
            layer.msg('请上传Logo图标');
            return false;
        }
        if (phone == '' || phone == 'undefined' || phone == null) {
            layer.msg('客服电话不能为空');
            return false;
        }
        if (copyright == '' || copyright == 'undefined' || copyright == null) {
            layer.msg('版权信息不能为空');
            return false;
        }
        return true;
    }

    var slock = false;

    function holdSave() {
        if (!slock) {
            var title = $.trim($('#title').val());
            var sngimg = $.trim($("[name='sngimg']").val());
            var phone = $.trim($('#phone').val());
            var copyright = $.trim($('#copyright').val());
            if (excheck(title, sngimg, phone, copyright)) {
                slock = true;
                $.ajax({
                    type: "post",
                    url: "<?php echo url('warrior'); ?>",
                    data: {
                        'id': '<?php echo $knight['id']; ?>',
                        'title': title,
                        'phone': phone,
                        'sngimg': sngimg,
                        'copyright': copyright
                    },
                    dataType: 'json',
                    success: function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                slock = false;
                            });
                        }
                    }
                });
            }
        }
    }
</script>

</body>
</html>