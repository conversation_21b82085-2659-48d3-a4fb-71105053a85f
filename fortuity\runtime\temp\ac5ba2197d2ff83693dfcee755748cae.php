<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:89:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/unlawful/attract.html";i:1751621856;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755608968;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-bullhorn {margin-right: 5px;color: #23b7e5;}
    .record-btn {background: linear-gradient(135deg, #28a745 0%, #20c997 100%);border: none;color: white;padding: 4px 8px;border-radius: 6px;font-size: 13px;text-decoration: none;cursor: pointer;transition: all 0.3s ease;box-shadow: 0 2px 4px rgba(40,167,69,0.2);display: inline-flex;align-items: center;gap: 6px;}
    .record-btn:hover {background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);box-shadow: 0 4px 8px rgba(40,167,69,0.3);transform: translateY(-1px);color: white;text-decoration: none;}
    .am-form-horizontal {background: #fff;border-radius: 6px;padding: 20px;box-shadow: 0 1px 3px rgba(0,0,0,0.05);}
    .am-form-group {margin-bottom: 20px;padding: 15px 0;border-bottom: 1px solid #f5f5f5;}
    .am-form-group:last-child {border-bottom: none;}
    .am-form-label {font-weight: 500;color: #333;line-height: 1.6;}
    .am-form-group input[type="text"] {height: 36px;padding: 8px 12px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;font-size: 14px;width: 100%;}
    .am-form-group input[type="text"]:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .am-form-group select {height: 36px;padding: 8px 12px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;font-size: 14px;width: 100%;}
    .am-form-group select:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .form-hint {color: #666;font-size: 12px;margin-top: 5px;display: block;line-height: 1.4;}
    .form-hint-warning {color: #dc3545;font-weight: 500;}
    .save-btn {background: linear-gradient(135deg, #23b7e5 0%, #1a9bc0 100%);border: none;color: white;padding: 12px 30px;border-radius: 6px;font-size: 14px;font-weight: 500;cursor: pointer;transition: all 0.3s ease;box-shadow: 0 2px 4px rgba(35,183,229,0.2);}
    .save-btn:hover {background: linear-gradient(135deg, #1a9bc0 0%, #1587a8 100%);box-shadow: 0 4px 8px rgba(35,183,229,0.3);transform: translateY(-1px);}
    .save-btn:active {transform: translateY(0);box-shadow: 0 2px 4px rgba(35,183,229,0.2);}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-bullhorn"></span> 新人营销
        </div>
        <div>
            <a href="<?php echo url('unlawful/attractRecord'); ?>" class="record-btn" target="_blank">
                任务完成记录
            </a>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-offset-1 am-u-sm-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">营销开关</label>
                        <div class="am-u-sm-7 am-u-end">
                            <select id="rewardStatus">
                                <option value="0" <?php if($list['reward_status']==0): ?>selected<?php endif; ?>>关闭</option>
                                <option value="1" <?php if($list['reward_status']==1): ?>selected<?php endif; ?>>开启</option>
                            </select>
                            <small class="form-hint">开启后新注册用户完成下列任务即可获得奖励</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">注册时间</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="text" id="regLessDay" value="<?php echo $list['reg_less_day']; ?>" oninput="grender(this);">
                            <small class="form-hint">新用户注册需在多少天内完成任务才有奖励</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">奖励类型</label>
                        <div class="am-u-sm-7 am-u-end">
                            <select id="rewardType">
                                <option value="0" <?php if($list['reward_type']==0): ?>selected<?php endif; ?>>积分</option>
                                <option value="1" <?php if($list['reward_type']==1): ?>selected<?php endif; ?>>贝壳</option>
                            </select>
                            <small class="form-hint">新人完成任务奖励的内容</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">奖励分数</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="text" id="rewardCode" value="<?php echo $list['reward_code']; ?>" oninput="grender(this,2);">
                            <small class="form-hint">完成新人任务奖励的分数 奖励内容为上方设置的奖励类型</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">最多奖励次数</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="text" id="rewardCount" value="<?php echo $list['reward_count']; ?>" oninput="grender(this);">
                            <small class="form-hint">新人完成任务后最多可获得奖励的次数 0为不限制次数</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子编号</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="text" id="toryIds" value="<?php echo $list['tory_ids']; ?>">
                            <small class="form-hint form-hint-warning">为空则在任何圈子都可获得奖励</small>
                            <small class="form-hint">圈子编号 在指定圈子内发帖或回复可以获得奖励 多个圈子使用英文的逗号分隔 例如 1,2,3</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">帖子编号</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="text" id="paperIds" value="<?php echo $list['paper_ids']; ?>">
                            <small class="form-hint form-hint-warning">为空则在任何帖子都可获得奖励</small>
                            <small class="form-hint">帖子编号 在指定帖子内回复或评论可以获得奖励 多个帖子使用英文的逗号分隔 例如 5,6,7</small>
                        </div>
                    </div>
                    <div class="am-form-group am-margin-top-lg" style="border-bottom: none;">
                        <div class="am-u-sm-9 am-u-sm-push-5" style="margin-left: 45px;">
                            <button type="button" class="save-btn" onclick="holdSave();">
                                <span class="am-icon-save"></span> 保存配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<script src="./assets/js/base-enhanced.js"></script>
<script>
var baseConfig = {
    urls: {
        proxyBase: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        proxyResource: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        repairMissing: "<?php echo url('index/repairMissing'); ?>",
        purgeCache: "<?php echo url('index/purgeCache'); ?>",
        ordinary: "<?php echo url('ordinary'); ?>",
        receipt: "<?php echo url('receipt'); ?>"
    },
    menuActive: '<?php echo $menuActive; ?>',
    menuList: '<?php echo $menuList; ?>',
    much_role: '<?php echo $much_role; ?>',
    notice: <?php echo $notice; ?>,
    vacant: <?php echo $vacant; ?>
};
BaseEnhanced.init(baseConfig);
window.retakeCache = BaseEnhanced.retakeCache;
window.getOriginalSrc = BaseEnhanced.getOriginalSrc;
</script>
<?php if($much_role=='founder'): ?>
<script>
window.updateFix = function() {
    layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
        btn: ['确定', '取消'],
        title: '提示'
    }, function() {
        // 执行修复操作
        performDatabaseRepair();
    }, function(index) {
        // 取消操作
        layer.close(index);
    });
};

function performDatabaseRepair() {
    var loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });
    $.post("<?php echo url('index/repairMissing'); ?>", function(data) {
        layer.close(loadingIndex);
        if (data !== false) {
            layer.msg('数据库缺失字段修复完成', {
                icon: 1,
                time: 1500
            }, function() {
                location.reload();
            });
        } else {
            layer.msg('修复失败，请稍后重试', {
                icon: 2,
                time: 2000
            }, function() {
                location.reload();
            });
        }
    }).error(function() {
        layer.close(loadingIndex);
        layer.msg('网络错误，修复失败', {
            icon: 2,
            time: 2000
        });
    });
}
</script>
<?php endif; ?>

<script>

    var grender = function (obj, limit) {
        if (limit === 2) {
            obj.value = obj.value.replace(/[^\d.]/g, "");
            obj.value = obj.value.replace(/^\./g, "");
            obj.value = obj.value.replace(/\.{2,}/g, ".");
            obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        } else {
            obj.value = Number((obj.value.match(/^\d+(?:\.\d{0})?/)));
        }
    }

    var holdSave = function () {
        var setData = {};
        setData['uplid'] = '<?php echo $list['id']; ?>';
        setData['rewardStatus'] = $.trim($("#rewardStatus").val());
        setData['regLessDay'] = $.trim($("#regLessDay").val());
        setData['rewardType'] = $.trim($("#rewardType").val());
        setData['rewardCode'] = $.trim($("#rewardCode").val());
        setData['rewardCount'] = $.trim($("#rewardCount").val());
        setData['toryIds'] = $.trim($("#toryIds").val());
        setData['paperIds'] = $.trim($("#paperIds").val());
        $.post("<?php echo url('unlawful/attract'); ?>", setData, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000});
            }
        }, 'json');
    }
</script>

</body>
</html>