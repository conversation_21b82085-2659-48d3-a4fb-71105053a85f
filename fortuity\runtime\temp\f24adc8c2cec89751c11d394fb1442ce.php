<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:83:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/user/index.html";i:1681882553;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755516152;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a></li>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>.am-table>thead:first-child>tr:first-child>th,.am-table>tbody>tr>td{text-align: center;}.bunch{border:1px solid;padding:3px 6px;border-radius:5px;font-size:12px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}.span-ranking{background:#477a98;border-radius:3px;padding:6px 10px;font-size:12px;cursor:pointer;color:#f1f4f5;}.escalator a{margin:0 4%;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-users"></span> 用户列表
        </div>
    </div>
    <div class="tpl-block">
        <div id="search-list" style="display: flex;flex-wrap: wrap;width: 100%;min-height: 38px;">
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">UID</label>
                <div class="search-input-inline">
                    <input type="text" name="uid" value="<?php echo $uid; ?>" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">用户昵称</label>
                <div class="search-input-inline">
                    <input type="text" name="userName" value="<?php echo $userName; ?>" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">openid</label>
                <div class="search-input-inline">
                    <input type="text" name="openid" value="<?php echo $openid; ?>" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">手机号</label>
                <div class="search-input-inline">
                    <input type="text" name="phone" value="<?php echo $phone; ?>" class="search-input">
                </div>
            </div>
            <button class="search-wide-pitch search-btn" onclick="turtle();">
                <i class="am-icon-search"></i> 搜 索
            </button>
        </div>
        <div class="am-g">
            <div class="am-u-sm-6 am-u-md-6">
                <div class="am-btn-toolbar" style="margin: 0px 0px 15px 20px;">
                    <div class="am-btn-group am-btn-group-xs resth">
                        <a href="<?php echo url('user/index'); ?>&egon=0" class="cust-btn cust-btn-one <?php if($egon==0): ?>cust-btn-activate<?php endif; ?>">全部</a>
                        <a href="<?php echo url('user/index'); ?>&egon=1" class="cust-btn <?php if($egon==1): ?>cust-btn-activate<?php endif; ?>">状态正常</a>
                        <a href="<?php echo url('user/index'); ?>&egon=2" class="cust-btn <?php if($egon==2): ?>cust-btn-activate<?php endif; ?>">状态封禁</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g" style="margin-top: 10px;">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table table-main am-table-radius">
                        <thead>
                        <tr>
                            <th width="6%">UID</th>
                            <th width="8%">用户头像</th>
                            <th width="12%">用户昵称</th>
                            <th width="10%">用户等级</th>
                            <th width="17%">openid</th>
                            <th width="8%"><?php echo $defaultNavigate['confer']; ?></th>
                            <th width="8%"><?php echo $defaultNavigate['currency']; ?></th>
                            <th width="8%">荣誉点</th>
                            <th width="8%">用户状态</th>
                            <th width="10%">授权时间</th>
                            <th width="6%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): $i = 0; $__LIST__ = $list;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <tr>
                            <td class="am-text-middle"><?php echo $vo['id']; ?></td>
                            <td class="am-text-middle">
                                <?php if($vo['user_head_sculpture'] && $vo['user_head_sculpture'] !='/yl_welore/style/icon/default.png'): ?>
                                <img src="<?php echo $vo['user_head_sculpture']; ?>" style="width: 50px;height: 50px;border-radius: 50%;">
                                <?php else: ?>
                                <img src="<?php echo urlBridging('static/disappear/tourist.png'); ?>" style="width: 50px;height: 50px;border-radius: 50%;">
                                <?php endif; ?>
                            </td>
                            <td class="am-text-middle">
                                <span title="<?php echo emoji_decode($vo['user_nick_name']); ?>">
                                    <?php echo emoji_decode($vo['user_nick_name']); ?>
                                </span>
                            </td>
                            <td class="am-text-middle">
                                <span title="当前经验值：<?php echo $vo['experience']; ?>">
                                Lv.<?php echo $vo['level']; ?>
                                </span>
                            </td>
                            <td class="am-text-middle"><?php echo $vo['user_wechat_open_id']; ?></td>
                            <td class="am-text-middle"><?php echo $vo['fraction']; ?></td>
                            <td class="am-text-middle"><?php echo $vo['conch']; ?></td>
                            <td class="am-text-middle"><?php echo $vo['honor_point']; ?></td>
                            <td class="am-text-middle">
                                <?php if($vo['status'] == 0): ?>
                                <span style="color: red;">禁止</span>
                                <?php else: ?>
                                <span style="color: lightgreen;">正常</span>
                                <?php endif; ?>
                            </td>
                            <td class="am-text-middle"> <?php echo date('Y-m-d H:i:s',$vo['user_reg_time']); ?></td>
                            <td class="am-text-middle">
                                <span class="am-icon-angle-down am-icon-sm" style="cursor: pointer;margin-left: 8px;" onclick="gains(this,'<?php echo $vo['id']; ?>');"></span>
                            </td>
                        </tr>
                        <tr id="kshatri-<?php echo $vo['id']; ?>" class="shild" style="display: none;background: #f3f4f6;text-align: center;">
                            </td>
                            <td class="escalator" colspan="11">
                                <a href="<?php echo url('user/material'); ?>&usid=<?php echo $vo['id']; ?>" class="bunch" target="_blank">个人资料</a>
                                <a href="<?php echo url('manual/dailyTask'); ?>&uid=<?php echo $vo['id']; ?>" class="bunch" target="_blank">任务完成详情</a>
                                <a href="<?php echo url('essay/index'); ?>&egon=0&hazy_name=<?php echo $vo['user_wechat_open_id']; ?>&page=1" class="bunch" target="_blank">发帖记录</a>
                                <a href="<?php echo url('user/wallet'); ?>&usid=<?php echo $vo['id']; ?>" class="bunch" target="_blank">钱包明细</a>
                                <a href="<?php echo url('manual/expGloryRecord'); ?>&uid=<?php echo $vo['id']; ?>" class="bunch" target="_blank">经验荣誉获得记录</a>
                                <?php if($vo['status']==1): ?>
                                <a href="javascript:void(0);" class="bunch" style="color: red;" onclick="uronet('<?php echo $vo['id']; ?>','0');" >封禁</a>
                                <?php else: ?>
                                <a href="javascript:void(0);" class="bunch" style="color: green;" onclick="uronet('<?php echo $vo['id']; ?>','1');" >解封</a>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            <?php echo $list->render(); ?>
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<?php if($much_role=='founder'): ?>
<script>
    var updateFix = function () {
        layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("<?php echo url('index/repairMissing'); ?>", function (data) {
                if (data !== false) {
                    layer.msg('数据库缺失字段修复完成', {time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('修复失败', {time: 1600}, function () {
                        location.reload();
                    });
                }
            });
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }
</script>
<?php endif; ?>
<script>
    new Vue({
        el: '#menu',
        data() {
            return {
                menuActive: '<?php echo $menuActive; ?>',
                menuList: []
            };
        },
        created() {
            const menuList = '<?php echo $menuList; ?>';
            if (menuList !== '') {
                const menuData = JSON.parse(decodeURIComponent(atob(menuList)));
                for (let i = 0; i < menuData.length; i++) {
                    if (menuData[i].pid === 0) {
                        menuData[i].childMenuItems = [];
                        this.menuList.push(menuData[i]);
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        if (menuData[i].pid === this.menuList[j].id) {
                            const childItem = JSON.parse(JSON.stringify(menuData[i]));
                            childItem.grandsonList = [];
                            this.menuList[j].childMenuItems.push(childItem);
                            break;
                        }
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        for (let k = 0; k < this.menuList[j].childMenuItems.length; k++) {
                            if (menuData[i].pid === this.menuList[j].childMenuItems[k].id) {
                                this.menuList[j].childMenuItems[k].grandsonList.push(menuData[i]);
                                break;
                            }
                        }
                    }
                }
            }
        }
    });

    $(function () {
        setInterval(reballot, 15000);
    });

    var reballot = function () {
        $.getJSON("<?php echo url('ordinary'); ?>", function (data) {
            var i = 0;
            if (data.notice > 0 || data.vacant > 0) {
                i++;
            }
            $('#notice-0,#notice-1').text(data.notice);
            $('#vacant-0,#vacant-1').text(data.vacant);
            if ((data.notice + data.vacant) <= data.preCount) {
                i = 0;
            } else {
                $.post("<?php echo url('receipt'); ?>", {'multiply': (data.notice + data.vacant)});
            }
            if (i > 0) {
                var player = $("#backPlayer")[0];
                player.play();
            }
        });
    }

    var retakeCache = function () {
        $.get("<?php echo url('index/purgeCache'); ?>", function () {
            layer.msg('缓存清理完成', {time: 1000}, function () {
                location.reload();
            });
        });
    }
</script>
<script>
// 添加全局变量
window.EDITOR_MODE = false;  // 标记是否在编辑器模式

// AJAX 拦截器 - 自动还原代理 URL 为原始 URL
(function() {
    // 代理 URL 的匹配模式
    var proxyUrlPattern = /[?&]url=([^&]+)/;
    var proxyBaseUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>";

    // URL 还原函数
    function restoreOriginalUrl(url) {
        try {
            // 检查是否是代理 URL
            if (typeof url === 'string' && url.indexOf(proxyBaseUrl) !== -1) {
                var match = url.match(proxyUrlPattern);
                if (match && match[1]) {
                    // 解码并返回原始 URL
                    return decodeURIComponent(match[1]);
                }
            }
            return url;
        } catch (e) {
            // 出错时返回原始值，确保不影响正常功能
            console.warn('URL restore failed:', e);
            return url;
        }
    }

    // 增强版 URL 还原函数 - 支持从 DOM 元素的 data 属性获取原始 URL
    window.getOriginalSrc = function(element) {
        try {
            if (element && element.getAttribute) {
                // 优先从 data-original-src 获取
                var originalSrc = element.getAttribute('data-original-src');
                if (originalSrc) {
                    return originalSrc;
                }
                // 其次尝试从当前 src 还原
                var currentSrc = element.src || element.getAttribute('src');
                return restoreOriginalUrl(currentSrc);
            }
            return element;
        } catch (e) {
            console.warn('getOriginalSrc failed:', e);
            return element;
        }
    };

    // 递归处理数据对象，还原其中的代理 URL
    function restoreDataUrls(data) {
        try {
            if (typeof data === 'string') {
                return restoreOriginalUrl(data);
            } else if (Array.isArray(data)) {
                return data.map(restoreDataUrls);
            } else if (data && typeof data === 'object') {
                var restored = {};
                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        restored[key] = restoreDataUrls(data[key]);
                    }
                }
                return restored;
            }
            return data;
        } catch (e) {
            // 出错时返回原始数据
            console.warn('Data restore failed:', e);
            return data;
        }
    }

    // 保存原始的 jQuery AJAX 方法
    var originalAjax = $.ajax;
    var originalPost = $.post;
    var originalGet = $.get;

    // 重写 $.ajax
    $.ajax = function(options) {
        try {
            if (options && options.data) {
                options.data = restoreDataUrls(options.data);
            }
        } catch (e) {
            console.warn('AJAX data restore failed:', e);
        }
        return originalAjax.apply(this, arguments);
    };

    // 重写 $.post
    $.post = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('POST data restore failed:', e);
        }
        return originalPost.call(this, url, data, success, dataType);
    };

    // 重写 $.get
    $.get = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('GET data restore failed:', e);
        }
        return originalGet.call(this, url, data, success, dataType);
    };
})();

// 修改检测函数
function checkResourceAccess() {
    var isHttps = window.location.protocol === 'https:';
    
    if (isHttps) {
        // 处理图片
        document.querySelectorAll('img[src^="http://"]').forEach(function(img) {
            // 如果是编辑器模式，所有图片都使用代理URL显示
            if(window.EDITOR_MODE) {
                var originalSrc = img.getAttribute('data-src') || img.src;
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
                return;
            }
            
            // 非编辑器模式下排除富文本编辑器内的图片
            if(img.closest('.w-e-text') || img.closest('.w-e-text-container')) {
                return;
            }
            
            var originalSrc = img.getAttribute('data-src') || img.src;
            var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);

            var tmpImg = new Image();
            tmpImg.onload = function() {
                // 原始图片可访问，不做处理
            }
            tmpImg.onerror = function() {
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = proxyUrl;
            }
            tmpImg.src = originalSrc;
        });
        
        // 处理视频
        document.querySelectorAll('video').forEach(function(video) {
            // 处理video的src属性
            if (video.src && video.src.startsWith('http://')) {
                handleResource(video, 'src');
            }
            
            // 处理source标签
            video.querySelectorAll('source[src^="http://"]').forEach(function(source) {
                handleResource(source, 'src');
            });
        });
    }
}

// 统一的资源处理函数
function handleResource(element, srcAttr) {
    var originalSrc = element[srcAttr];
    var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
    
    // 创建临时对象测试资源是否可访问
    var tmpObj = new Image(); // 用Image对象测试连接可用性
    tmpObj.onload = function() {
        // 原始资源可访问，不做处理
    }
    tmpObj.onerror = function() {
        // 原始资源不可访问，切换到代理
        // 存储原始 URL 到 data 属性
        element.setAttribute('data-original-src', originalSrc);
        element[srcAttr] = proxyUrl;

        // 同时更新父级a标签的href
        var parent = element.parentElement;
        if (parent && parent.tagName === 'A') {
            parent.setAttribute('data-original-href', parent.href);
            parent.href = proxyUrl;
        }
    }
    tmpObj.src = originalSrc;
}

// 页面加载完成后执行检查
window.addEventListener('load', checkResourceAccess);

// 动态加载的内容处理
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            checkResourceAccess();
        }
    });
});

// 监听DOM变化
observer.observe(document.body, {
    childList: true,
    subtree: true
});
</script>

<script>

    var gains = function (obj, suid) {
        var brahman = $(obj).attr('class');
        $('.am-icon-angle-up').attr('class', 'am-icon-angle-down am-icon-sm');
        $('.shild').hide();
        if (brahman != 'am-icon-angle-up am-icon-sm') {
            $(obj).attr('class', 'am-icon-angle-up am-icon-sm');
            $('#kshatri-' + suid).show();
        }
    }

    var uronet = function (uid, their) {
        if (their == 0) {
            layer.confirm('您确定要封禁当前用户吗？', {
                btn: ['确定', '取消'], title: '提示'
            }, function (index) {
                layer.close(index);
                forbidCause(uid, their);
            }, function (index) {
                layer.close(index);
            });
        } else {
            layer.confirm('您确定要解封当前用户吗？', {
                btn: ['确定', '取消'], title: '提示'
            }, function () {
                pulate(uid, their);
            }, function (index) {
                layer.close(index);
            });
        }
    }

    var forbidCause = function (uid, their) {
        layer.prompt({title: '请输入封禁理由', formType: 0}, function (cause, index) {
            cause = $.trim(cause);
            if (cause == '') {
                return;
            }
            pulate(uid, their, cause);
        });
    }


    var pulate = function (uid, their, cause) {
        $.post("<?php echo url('user/pulate'); ?>", {nuid: uid, ntheir: their, cause: cause}, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000});
            }
        }, 'json');
    }

    function turtle() {
        var searchInput = $('#search-list .search-input');
        var n = 0;
        for (var i = 0; i < searchInput.length; i++) {
            if (searchInput[i].value.trim() !== '') {
                n++;
            }
        }
        if (n > 0) {
            location.href = `<?php echo url('user/index'); ?>&${searchInput.serialize()}&page=<?php echo $page; ?>`;
        } else {
            location.href = "<?php echo url('user/index'); ?>&page=<?php echo $page; ?>";
        }
    }
</script>

</body>
</html>