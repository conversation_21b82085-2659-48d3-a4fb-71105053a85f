<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\UserService;
use app\common\Mimetic;
use app\common\Remotely;
use app\common\Tools;
use app\urge\middleware\LoginCheck;
use think\Cache;
use think\Controller;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\Response;

// 虽然不是中间件，但作为服务类引入

#数据初始化
class Base extends Controller
{
    protected $M = null;    //  全局变量
    protected $much = null;     //  管理信息
    protected $much_id = null;     //  商户编号
    protected $sign_code = '1.2.19';     //  项目版本号
    protected $accessType;  //  访问类型

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        // _initialize 会在构造函数后由框架自动调用
    }

    /**
     * 初始化与校验逻辑
     */
    protected function _initialize()
    {
        // 1. 调用安全服务类进行校验，并获取用户信息
        try {
            $sessionData = LoginCheck::run();
            $this->M = $sessionData;
            // 从返回的数据中为 accessType 赋值
            $this->accessType = isset($sessionData['access_type']) ? $sessionData['access_type'] : 0;
        } catch (HttpResponseException $e) {
            // 捕获重定向异常并直接抛出，以中断执行
            throw $e;
        }

        // 2. 继续执行业务初始化逻辑
        if (empty($this->M)) {
            // 此处理论上不会执行到，因为上面验证失败会直接抛出异常
            return;
        }

        $miKey = md5('asia' . strtotime(date('Y-m-d H:i')) . 'shanghai');
        Mimetic::___biochemical($miKey, intval($this->M['uniacid']));

        $this->much = cache('user_' . $this->M['user']['username'] . '_' . $this->M['uniacid']);
        if (!$this->much) {
            $much_data['much_name'] = $this->M['user']['username'];
            $much_data['much_uniacid'] = $this->M['uniacid'];
            $much_data['id'] = $this->newMerchant($much_data);
            $this->much = $much_data;
            Cache::set('user_' . $this->M['user']['username'] . '_' . $this->M['uniacid'], $this->much, 180);
        }
        $this->much_id = $this->much['much_uniacid'];

        $much_title = '';
        switch ($this->M['role']) {
            case 'founder':
                $much_title = '站长';
                break;
            case 'vice_founder':
                $much_title = '副站长';
                break;
            case 'operator':
                $much_title = '操作员';
                break;
            case 'manager':
                $much_title = '管理员';
                break;
            case 'owner':
                $much_title = '所有者';
                break;
        }
        $this->assign('much_role', $this->M['role']);
        $this->assign('much_name', $this->much['much_name']);
        $this->assign('much_title', $much_title);

        //  版权信息
        $outrage = $this->outrage();
        if ($outrage) {
            $this->assign('autonomy', 1);
        } else {
            $this->assign('autonomy', 0);
        }
        //  排除显示
        $excludeMenuList = ['纸条管理', '插件管理', '模板市场'];
        //  从缓存中读取菜单栏
        $menuList = cache('backMenuList');
        //  判断缓存是否失效
        if (!$menuList) {
            //  菜单栏
            $menuList = Db::name('motion')->order(['sort' => 'asc', 'pid' => 'asc'])->select();
            //  设置缓存
            Cache::set('backMenuList', $menuList, 43200);
        }
        for ($i = 0; $i < count($menuList); $i++) {
            //  根据路由规则生成
            $assembleUrl = url($menuList[$i]['mot_url']);
            //  生成相对路径
            $menuList[$i]['mot_url'] = "./{$assembleUrl}";
            //  判断是否需要排除的数据
            if (in_array($menuList[$i]['mot_name'], $excludeMenuList) && !$outrage) {
                unset($menuList[$i]);
            }
        }
        //  个性化菜单栏
        Remotely::customMenu($menuList);
        $sceneController = request()->controller();
        $sceneAction = request()->action();
        $motUrl = strtolower("$sceneController/$sceneAction");
        $menuActive = Db::name('motion')->where('mot_url', $motUrl)->value('id');
        $this->assign('motUrl', $motUrl);
        $this->assign('menuActive', $menuActive);
        $this->assign('menuList', base64_encode(rawurlencode(json_encode($menuList, true))));

        //  全局变量
        $this->assign('_W', $this->M);

        //  版权显示
        $this->assign('globalRecluse', $this->defaultAnchoret());

        //  首页提醒
        $notice = Db::name('prompt_msg')->where('type', 0)->where('status', 0)->where('much_id', $this->much_id)->count();
        $this->assign('notice', $notice);
        //  首页消息
        $vacant = Db::name('prompt_msg')->where('type', 1)->where('status', 0)->where('much_id', $this->much_id)->count();
        $this->assign('vacant', $vacant);

        $knight = $this->getKnight();
        $this->assign('knight', $knight);

        //  兼容富文本
        $richTextCompatible = function ($string) {
            $string = emoji_decode($string);
            if (preg_match("/^<p>.*<\/p>$/", $string)) { // 判断是否已被 <p> 标签包裹
                return $string; // 若已被包裹，则直接输出原字符串
            } else {
                return "<p>{$string}</p>"; // 否则，在字符串前后添加 <p> 和 </p> 标签并输出
            }
        };
        $this->assign('richTextCompatible', $richTextCompatible);


        // ============================  自动删除帖子  ============================
        $userService = new UserService();
        $userService->delPaperTime($this->much_id);
    }

    //  默认版权设置
    protected function defaultAnchoret()
    {
        return Mimetic::___defaultAnchoret();
    }


    //  新增一个新的用户信息
    private function newMerchant($much_data)
    {
        //  查找用户是否有该用户
        $this->much = Db::name('much_admin')->where('much_name', $this->M['user']['username'])->where('much_uniacid', $this->M['uniacid'])->find();
        //  没有此用户则新增一个
        if (!$this->much) {
            //  新增一个用户
            $muserId = Db::name('much_admin')->cache('user_' . $this->M['user']['username'] . '_' . $this->M['uniacid'], 60)->insertGetId($much_data);
        } else {
            $muserId = Db::name('much_admin')->where('much_uniacid', $this->M['uniacid'])->find();
        }
        //  初始化新增流量主
        $this->defaultAdvertise($this->M['uniacid']);
        //  初始化新增广场
        //$this->defaultSquare($this->M['uniacid']);
        //  初始化新增帖子设置
        $this->defaultPaperSmingle($this->M['uniacid']);
        //  初始化网盘配置大小
        $this->defaultNetDisc($this->M['uniacid']);
        //  初始化公众号配置
        $this->defaultWxPopular($this->M['uniacid']);
        //  初始化一个消息回执
        $this->defaultPreCount($this->M['uniacid']);
        //  初始化一个帖子转发
        $this->defaultReissue($this->M['uniacid']);
        //  初始化一个礼物税收
        $this->defaultTaxing($this->M['uniacid']);
        //  初始化新增会员价格
        $this->defaultHonorablePrice($this->M['uniacid']);
        //  初始化一个签到积分范围
        $this->defaultPunch($this->M['uniacid']);
        return $muserId;
    }

    //  新增一个默认的流量主
    protected function defaultAdvertise($muchId)
    {
        return Mimetic::___defaultAdvertise($muchId);
    }

    //  新增一个帖子设置
    protected function defaultPaperSmingle($muchId)
    {
        return Mimetic::___defaultPaperSmingle($muchId);
    }

    //  新增一个网盘设置
    protected function defaultNetDisc($muchId)
    {
        return Mimetic::___defaultNetDisc($muchId);
    }

    /*
     * 新增一个公众号配置
     */
    protected function defaultWxPopular($muchId)
    {
        return Mimetic::___defaultWxPopular($muchId);
    }

    //  新增一个默认会员价格
    protected function defaultHonorablePrice($muchId)
    {
        return Mimetic::___defaultHonorablePrice($muchId);
    }

    //  新增一个默认消息回执
    protected function defaultPreCount($muchId)
    {
        return Mimetic::___defaultPreCount($muchId);
    }

    //  新增一个帖子转发
    protected function defaultReissue($muchId)
    {
        return Mimetic::___defaultReissue($muchId);
    }

    //  新增一个礼物税收
    protected function defaultTaxing($muchId)
    {
        return Mimetic::___defaultTaxing($muchId);
    }


    //  新增一个默认积分获得范围
    protected function defaultPunch($muchId)
    {
        return Mimetic::___defaultPunch($muchId);
    }


    //  获取版权信息
    protected function getKnight()
    {
        return Mimetic::___getKnight($this->much_id);
    }

    //  获取自定义导航
    protected function defaultNavigate()
    {
        return Mimetic::___defaultNavigate($this->much_id);
    }

    //  消息提醒
    public function ordinary()
    {
        $notices = cache('notices_' . $this->much_id);
        if (!$notices) {
            //  提醒
            $notices = Db::name('prompt_msg')->where('status', 0)->where('type', 0)->where('much_id', $this->much_id)->cache('notices_' . $this->much_id)->count('*');
        }
        $vacants = cache('vacants_' . $this->much_id);
        if (!$vacants) {
            //  消息
            $vacants = Db::name('prompt_msg')->where('status', 0)->where('type', 1)->where('much_id', $this->much_id)->cache('vacants_' . $this->much_id)->count('*');
        }
        $promptCount = cache('preCount_' . $this->much_id);
        if (!$promptCount) {
            $promptCount = Db::name('prompt_count')->where('much_id', $this->much_id)->cache('preCount_' . $this->much_id)->find();
        }
        return json(['notice' => $notices, 'vacant' => $vacants, 'preCount' => $promptCount['barg']]);
    }

    //  消息回执
    public function receipt()
    {
        if (request()->isPost() && request()->isAjax()) {
            $multiply = request()->post('multiply', '');
            if ($multiply) {
                return Db::name('prompt_count')->where('much_id', $this->much_id)->cache('preCount_' . $this->much_id)->update(['barg' => $multiply]);
            }
        }
    }

    //   默认方法
    protected function defaultQuery()
    {
        //  获取当前的模块名
        $sceneModule = strtolower(request()->module());
        //  获取当前控制器名
        $sceneController = strtolower(request()->controller());
        //  获取当前的操作名
        $sceneAction = strtolower(request()->action());
        //  返回数据
        return "/$sceneModule/$sceneController/$sceneAction.html";
    }

    /*
     * 空数据重定向
     */
    protected function emptyDataRedirect($data = array())
    {
        //  重定向地址
        $redirect = Response::create('', '', 302, ['Location' => url(request()->action()) . '&' . http_build_query($data)]);
        //  强制跳转
        throw new HttpResponseException($redirect);
    }

    public function outrage()
    {
        $miKey = md5('asia' . strtotime(date('Y-m-d H:i')) . 'shanghai');
        return Mimetic::___outrage($miKey, $this->much_id);
    }

    //   安全过滤
    protected function safe_html($data)
    {
        return Tools::safeHtml($data);
    }
}
