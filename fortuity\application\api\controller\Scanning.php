<?php

namespace app\api\controller;

use app\api\service\Alternative;
use think\Db;

class Scanning extends Base
{
    public function index()
    {
        $data = input('param.');
        $list = Db::name('paper')
            ->where('much_id', $data['much_id'])
            ->where('whether_delete', 0)
            ->where('study_status', 1)
            ->where('address_latitude', 'not null')
            ->where('address_longitude', 'not null')
            ->field('id,address_latitude,address_longitude,study_type')
            ->select();
        $arr = array();
        foreach ($list as $k => $v) {
            $arr[$k]['id'] = $k;
            $arr[$k]['ids'] = $v['id'];
            $arr[$k]['study_type'] = $v['study_type'];
            $arr[$k]['latitude'] = $v['address_latitude'];
            $arr[$k]['longitude'] = $v['address_longitude'];
            $arr[$k]['iconPath'] = "/yl_welore/style/icon/mak.png";
            $arr[$k]['width'] = "100rpx";
            $arr[$k]['height'] = "100rpx";
        }
        return $this->json_rewrite($arr);
    }

    public function AddAnalysis()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if ($plug->check_plug('203421c1-ebc5-d393-96a4-424251758adb', $data['much_id'])) {
            $url = $data['url'];
            $str_r = '/(http:\/\/|https:\/\/)((\w|=|\?|\.|\/|&|-)+)/';
            preg_match_all($str_r, $url, $arr);
            $share_url = $arr[0][0];
            $res = Alternative::GetAnalysis($share_url, $data['much_id'], 1);
            return $this->json_rewrite($res);
        } else {
            return $this->json_rewrite(['code' => 0, 'msg' => '未开通插件']);
        }

    }

    public function paper_heat_config()
    {
        $data = input('param.');
        $config = Db::name('paper_heat_config')->where('much_id', $data['much_id'])->find();
        $domain = explode(':', $_SERVER['HTTP_HOST']);
        $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
        $absRess = "https://{$domain[0]}{$absAddress[0]}static/examine";
        if (empty($config)) {
            $config['custom_title'] = '热帖排行';
            $config['custom_sort_condition'] = $absRess + '/put_top.jpg';
            $config['custom_sort_condition'] = '["liulan","dianzan","shoucang","huifu"]';
            $config['statistics_time'] = 0;
        }
        if ($config['statistics_time'] == 0) {
            //全部
            $yesterdayStart = '2000-01-01';
            $yesterdayEnd = '2099-12-31';
        }
        if ($config['statistics_time'] == 1) {
            //天
            $yesterday = date('Y-m-d', strtotime('-1 day'));
            $yesterdayStart = $yesterday . ' 00:00:00';
            $yesterdayEnd = $yesterday . ' 23:59:59';
        }
        if ($config['statistics_time'] == 2) {
            //周
            $endDate = date('Y-m-d');
            $startDate = date('Y-m-d', strtotime('-1 week', strtotime($endDate)));
            $yesterdayStart = $endDate . ' 00:00:00';
            $yesterdayEnd = $startDate . ' 23:59:59';
        }
        if ($config['statistics_time'] == 3) {
            //月
            $currentDate = date('Y-m-d');
            $firstDayOfMonth = date('Y-m-01', strtotime($currentDate));
            $lastDayOfMonth = date('Y-m-t', strtotime($currentDate));
            $yesterdayStart = $firstDayOfMonth . ' 00:00:00';
            $yesterdayEnd = $lastDayOfMonth . ' 23:59:59';
        }
        if ($config['statistics_time'] == 4) {
            //季度
            $currentDate = date('Y-m-d');
            $timestamp = strtotime($currentDate);
            $quarter = ceil(date('n', $timestamp) / 3);

            $startQuarter = date('Y-m-d', strtotime(($quarter - 1) . ' quarter', $timestamp));
            $endQuarter = date('Y-m-d', strtotime(($quarter) . ' quarter', $timestamp));
            $yesterdayStart = $startQuarter . ' 00:00:00';
            $yesterdayEnd = $endQuarter . ' 23:59:59';
        }
        if ($config['statistics_time'] == 5) {
            //年
            $currentYear = date('Y');
            $firstDayOfYear = $currentYear . '-01-01';
            $lastDayOfYear = $currentYear . '-12-31';
            $yesterdayStart = $firstDayOfYear . ' 00:00:00';
            $yesterdayEnd = $lastDayOfYear . ' 23:59:59';
        }

        $custom_sort_condition = json_decode($config['custom_sort_condition'], true);
        $arr_all = array();
        if (is_array($custom_sort_condition)) {
            foreach ($custom_sort_condition as $k => $v) {
                switch ($v) {
                    case 'liulan':
                        $arr_all[$k] = Db::name('paper')->alias('a')
                            ->where('a.much_id', $data['much_id'])
                            ->where('a.whether_delete', 0)
                            ->where('a.study_status', 1)
                            ->where('adapter_time', 'between time', [$yesterdayStart, $yesterdayEnd])
                            ->field('a.id,a.study_type,a.study_heat as finance,a.study_heat,a.adapter_time,a.study_content,a.tory_id,a.study_title')
                            ->limit(10)
                            ->order('a.study_heat desc')
                            ->select();
                        break;
                    case 'dianzan':
                        $arr_all[$k] = Db::name('user_applaud')->alias('a')
                            ->join('paper u', 'u.id=a.paper_id')
                            ->where('a.much_id', $data['much_id'])
                            ->where('a.applaud_type', 0)
                            ->where('u.whether_delete', 0)
                            ->where('u.study_status', 1)
                            ->where('adapter_time', 'between time', [$yesterdayStart, $yesterdayEnd])
                            ->field('u.id,u.study_type,count(*) as finance,u.adapter_time,u.study_content,u.tory_id,u.study_title')
                            ->group('a.paper_id')
                            ->order('finance desc')
                            ->limit(10)
                            ->select();

                        break;
                    case 'shoucang':
                        $arr_all[$k] = Db::name('user_collect')
                            ->alias('a')
                            ->join('paper u', 'u.id=a.paper_id')
                            ->where('a.much_id&u.much_id', $data['much_id'])
                            ->where('u.whether_delete', 0)
                            ->where('u.study_status', 1)
                            ->where('adapter_time', 'between time', [$yesterdayStart, $yesterdayEnd])
                            ->field('u.id,u.study_type,count(*) as finance,u.adapter_time,u.study_content,u.tory_id,u.study_title')
                            ->group('a.paper_id')
                            ->order('finance desc')
                            ->limit(10)
                            ->select();

                        break;
                    case 'huifu':
                        $arr_all[$k] = Db::name('paper_reply')
                            ->alias('a')
                            ->join('paper u', 'a.paper_id=u.id')
                            ->where('a.much_id', $data['much_id'])
                            ->where('u.study_status', 1)
                            ->where('u.whether_delete', 0)
                            ->where('a.whether_delete', 0)
                            ->where('a.reply_status', 1)
                            ->where('adapter_time', 'between time', [$yesterdayStart, $yesterdayEnd])
                            ->field('u.id,u.study_type,count(*) as finance,u.adapter_time,u.study_content,u.tory_id,u.study_title')
                            ->group('a.paper_id')
                            ->order('finance desc')
                            ->limit(10)
                            ->select();
                }
            }
        }
        //dump($arr_all);exit();
        $new_list = $this->array_sum_my($arr_all);

        arsort($new_list);

        $new_list = array_slice($new_list, 0, 10, true);

        $keys = array();
        //查询是否有没有置顶的
        $currentDateTime = time();
        $top_paper = Db::name('paper_heat_put_top')->where('much_id', $data['much_id'])->where('put_top_start_time', '<=', $currentDateTime)
            ->where('put_top_end_time', '>=', $currentDateTime)->select();
//        $key=array();
//        foreach ($top_paper as $k=>$v){
//            $key[$v['paper_id']]=rand(10000,99999);
//        }
        // $new_list=$key+$new_list;

        foreach ($new_list as $k => $v) {
            $info = Db::name('paper')->where('id', $k)->where('much_id', $data['much_id'])->find();
            //查询圈子
            $attr = Db::name('territory')->where('id', $info['tory_id'])->where('much_id', $data['much_id'])->find();
            //查询帖子回复数量
            $reply = Db::name('paper_reply')->where('whether_delete', 0)->where('paper_id', $info['id'])->where('reply_status', 1)->where('much_id', $data['much_id'])->count();
            $keys[$k]['id'] = $k;
            $keys[$k]['study_type'] = $info['study_type'];
            $keys[$k]['hort'] = $v;
            $keys[$k]['reply'] = $reply;
            $keys[$k]['study_content'] = strip_tags($info['study_content']);
            $keys[$k]['study_title'] = strip_tags($info['study_title']);
            $keys[$k]['realm_name'] = $attr['realm_name'];
        }
        $normalizedArray = (is_array($keys) ? array_values($keys) : array());
        //轮播图
        $paper_heat_banner_ads = Db::name('paper_heat_banner_ads')->where('much_id', $data['much_id'])->order('sort')->select();
        return $this->json_rewrite(['code' => 1, 'info' => $config, 'list' => $normalizedArray, 'banner' => $paper_heat_banner_ads]);
    }

    public function array_sum_my($arr)
    {
        $newArr = array();   //随便命名一个新的空数组
        foreach ($arr as $key => &$v) {             //这里的是有个“引用”
            if (!empty($arr[$key])) {   //只计算每天参加过摇一摇的用户
                foreach ($v as $k) {      //两次循环，取得最里面的数组
                    if (isset($newArr[$k['id']])) {
                        $newArr[$k['id']] += $k['finance'];
                    } else {
                        $newArr[$k['id']] = $k['finance'];
                    }
                }
            } else {
                unset($arr[$key]);
            }

        }
        return $newArr;
    }
}