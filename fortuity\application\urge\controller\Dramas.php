<?php

namespace app\urge\controller;

use app\api\service\Alternative;
use app\api\service\TmplService;
use think\Db;
use think\Response;

/**
 * 短剧
 */
class Dramas extends Base
{
    /**
     * 短剧类型
     * @return mixed
     */
    public function micro_series_type()
    {
        $url = $this->defaultQuery();
        $searchName = request()->get('searchName', '');
        $list = Db::name('micro_series_type')
            ->where('name', 'like', "%{$searchName}%")
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->order(['sort' => 'asc', 'id' => 'asc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'searchName' => $searchName]]);
        $this->assign('list', $list);
        $this->assign('searchName', $searchName);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /**
     * 短剧类型排序
     * @return \think\response\Json|void
     */
    public function micro_series_type_sort()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = request()->post('fid');
            $sort = request()->post('sort');
            Db::startTrans();
            try {
                Db::name('micro_series_type')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->update(['sort' => $sort]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            abort(404);
        }
    }

    /**
     * 新增短剧类型
     * @return mixed
     */
    public function new_micro_series_type()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['name'] = trim(input('post.name'));
            $data['sort'] = intval(input('post.sort'));
            $data['status'] = intval(input('post.status'));
            $data['create_time'] = time();
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('micro_series_type')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            return $this->fetch();
        }
    }

    /**
     * 编辑短剧类型
     * @return mixed
     */
    public function edit_micro_series_type()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $data['name'] = trim(input('post.name'));
            $data['sort'] = intval(input('post.sort'));
            $data['status'] = intval(input('post.status'));
            Db::startTrans();
            try {
                Db::name('micro_series_type')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $fid = intval(input('get.fid'));
            $mstInfo = Db::name('micro_series_type')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->find();
            if ($mstInfo) {
                $this->assign('list', $mstInfo);
                return $this->fetch();
            } else {
                abort(404);
            }
        }
    }

    /**
     * 删除短剧类型
     * @return \think\response\Json|void
     */
    public function del_micro_series_type()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            Db::startTrans();
            try {
                Db::name('micro_series_type')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /**
     * 短剧信息列表
     * @return mixed
     */
    public function micro_series_info()
    {
        $url = $this->defaultQuery();
        $searchName = request()->get('searchName', '');
        $list = Db::name('micro_series_info_list')
            ->where('title', 'like', "%{$searchName}%")
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->orderRaw('status <> 0 asc,case when status = 0 then id end asc,case when status <> 0 then id end desc')
            ->paginate(10, false, ['query' => ['s' => $url, 'searchName' => $searchName]])
            ->each(function ($item) {
                $mstList = Db::name('micro_series_type')->whereIn('id', $item['type'])->where('is_del', 0)->where('much_id', $this->much_id)->field('name')->select();
                $item['type_name'] = implode(',', array_column((array)$mstList, 'name'));
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('searchName', $searchName);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /**
     * 新增短剧信息
     * @return mixed
     */
    public function new_micro_series_info()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = [
                'upload_user_id' => intval(input('post.uploadUserId')),
                'title' => trim(input('post.title')),
                'type' => trim(input('post.type')),
                'poster_url' => trim(input('post.posterUrl')),
                'director' => trim(input('post.director')),
                'screenwriter' => trim(input('post.screenwriter')),
                'lead_actors' => trim(input('post.leadActors')),
                'production_country' => trim(input('post.productionCountry')),
                'language' => trim(input('post.language')),
                'release_date' => trim(input('post.releaseDate')),
                'duration_minutes' => intval(input('post.durationMinutes')),
                'alias' => trim(input('post.alias')),
                'plot_summary' => trim(input('post.plotSummary')),
                'total_episodes' => trim(input('post.totalEpisodes')),
                'user_copyright_img' => trim(input('post.userCopyrightImg')),
                'status' => intval(input('post.status')),
                'display_status' => intval(input('post.displayStatus')),
                'create_time' => time(),
                'update_time' => time(),
                'is_del' => 0,
                'much_id' => $this->much_id,
            ];
            Db::startTrans();
            try {
                Db::name('micro_series_info_list')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error, ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $mstList = Db::name('micro_series_type')->where('is_del', 0)->where('much_id', $this->much_id)->field('id,name')->select();
            $this->assign('mstList', base64_encode(rawurlencode(json_encode($mstList, true))));
            return $this->fetch();
        }
    }

    /**
     * 编辑短剧信息
     * @return mixed
     */
    public function edit_micro_series_info()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $data = [
                'upload_user_id' => intval(input('post.uploadUserId')),
                'title' => trim(input('post.title')),
                'type' => trim(input('post.type')),
                'poster_url' => trim(input('post.posterUrl')),
                'director' => trim(input('post.director')),
                'screenwriter' => trim(input('post.screenwriter')),
                'lead_actors' => trim(input('post.leadActors')),
                'production_country' => trim(input('post.productionCountry')),
                'language' => trim(input('post.language')),
                'release_date' => trim(input('post.releaseDate')),
                'duration_minutes' => intval(input('post.durationMinutes')),
                'alias' => trim(input('post.alias')),
                'plot_summary' => trim(input('post.plotSummary')),
                'total_episodes' => trim(input('post.totalEpisodes')),
                'user_copyright_img' => trim(input('post.userCopyrightImg')),
                'status' => intval(input('post.status')),
                'display_status' => intval(input('post.displayStatus')),
                'update_time' => time(),
            ];
            Db::startTrans();
            try {
                Db::name('micro_series_info_list')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error, ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $fid = intval(input('get.fid'));
            $mstInfo = Db::name('micro_series_info_list')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->find();
            if ($mstInfo) {
                $mstList = Db::name('micro_series_type')->where('is_del', 0)->where('much_id', $this->much_id)->field('id,name')->select();
                $this->assign('mstList', base64_encode(rawurlencode(json_encode($mstList, true))));
                
                // JSON_UNESCAPED_UNICODE = 256
                // JSON_HEX_TAG = 2
                // JSON_HEX_APOS = 4
                // JSON_HEX_AMP = 8
                // JSON_HEX_QUOT = 16
                $this->assign('list', json_encode($mstInfo, 286));
                return $this->fetch();
            } else {
                abort(404);
            }
        }
    }

    /**
     * 短剧信息详情
     * @return mixed
     */
    public function micro_series_info_detail()
    {
        $fid = intval(input('get.fid'));
        $msiInfo = Db::name('micro_series_info_list')->where('id', $fid)->where('much_id', $this->much_id)->find();
        if ($msiInfo) {
            $msiInfo['type_name'] = Db::name('micro_series_type')->where('id', $msiInfo['type'])->where('much_id', $this->much_id)->value('name');
            $msiInfo['user'] = Db::name('user')->where('id', $msiInfo['upload_user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
            $this->assign('list', $msiInfo);
            return $this->fetch();
        } else {
            abort(404);
        }
    }

    /**
     * 短剧信息审核
     * @return \think\response\Json|void
     */
    public function trial_micro_series_info()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            $msiInfo = Db::name('micro_series_info_list')->where('id', $fid)->where('much_id', $this->much_id)->find();
            //  判断审核状态
            if (intval($msiInfo['status']) !== 0) {
                return json(['code' => 0, 'msg' => '系统繁忙，请刷新页面后重试！']);
            }
            $data['status'] = intval(input('post.process', 0));
            //  审核拒绝
            if ($data['status'] === 2) {
                $data['audit_reason'] = trim(input('post.inject', ''));
            }
            Db::startTrans();
            try {
                //  更改审核状态
                Db::name('micro_series_info_list')->where('id', $fid)->where('status', 0)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($data['status'] >= 1 && $data['status'] <= 2) {
                $tmplService = new TmplService();
                $templateData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $msiInfo['upload_user_id'],
                    'page' => 'yl_welore/pages/user_smail/index',
                    'keyword4' => date('Y年m月d日 H:i:s', time()),
                ];
                $maringText = '';
                //  二手交易审核通过
                switch ($data['status']) {
                    case 1:
                        $templateData['keyword1'] = "您发布的短剧信息已通过审核";
                        $maringText = "{$templateData['keyword1']}！";
                        break;
                    case 2:
                        $templateData['keyword1'] = "您发布的短剧信息未通过审核";
                        $maringText = "很抱歉，您发布短剧信息未通过审核，拒绝原因：{$data['audit_reason']}";
                        break;
                }
                //  模板消息
                $tmplService->add_template($templateData);
                //  站内信
                Db::name('user_smail')->insert(['user_id' => $msiInfo['upload_user_id'], 'maring' => $maringText, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /**
     * 删除短剧信息
     * @return \think\response\Json|void
     */
    public function del_micro_series_info()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            Db::startTrans();
            try {
                Db::name('micro_series_info_list')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error, ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }


    /**
     * 短剧视频
     * @return mixed
     */
    public function micro_series_content()
    {
        $url = $this->defaultQuery();
        $searchName = request()->get('searchName', '');
        $where = array();
        if ($searchName !== '') {
            $msiInfo = Db::name('micro_series_info_list')->where('title', 'like', "%{$searchName}%")->where('is_del', 0)->where('much_id', $this->much_id)->field('id,title')->find();
            $where['msi_id'] = $msiInfo['id'];
        }
        $list = Db::name('micro_series_content_list')
            ->where($where)
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->orderRaw('status <> 0 asc,case when status = 0 then id end asc,case when status <> 0 then id end desc')
            ->paginate(10, false, ['query' => ['s' => $url, 'searchName' => $searchName]])
            ->each(function ($item) {
                $item['msi_title'] = Db::name('micro_series_info_list')->where('id', $item['msi_id'])->where('is_del', 0)->where('much_id', $this->much_id)->value('title');
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('searchName', $searchName);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /**
     * 短剧类型排序
     * @return \think\response\Json|void
     */
    public function micro_series_content_sort()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = request()->post('fid');
            $sort = request()->post('sort');
            Db::startTrans();
            try {
                Db::name('micro_series_content_list')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->update(['sort' => $sort]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            abort(404);
        }
    }

    /**
     * 新增短剧视频
     * @return mixed|\think\response\Json
     */
    public function new_micro_series_content()
    {
        if (request()->isPost() && request()->isAjax()) {
            // 获取POST提交的数据
            $msiEpisodeUrlType = intval(input('post.msiEpisodeUrlType'));  // 获取剧集URL类型
            $msiEpisodeNumber = trim(input('post.msiEpisodeNumber'));  // 获取剧集编号
            $msiEpisodeUrl = trim(input('post.msiEpisodeUrl'));  // 获取剧集URL
            $mscInfoCount = 1;  // 默认剧集信息数量为1
            $NumberStartingEpisodes = 1;  // 默认起始剧集编号为1
            $mscInfoList = array();  // 初始化剧集信息列表为空数组
            // 如果剧集URL类型为1，表示多个剧集URL
            if ($msiEpisodeUrlType === 1) {
                $msiEpisodeUrlList = trim(input('post.msiEpisodeUrlList'));  // 获取多个剧集URL列表
                $NumberStartingEpisodes = intval(input('post.numberStartingEpisodes'));  // 获取起始剧集编号
                $mscInfoList = explode("\n", $msiEpisodeUrlList);  // 将剧集URL列表按换行符分割成数组
                $mscInfoCount = count($mscInfoList);  // 获取剧集信息数量
            }
            $data = array();  // 初始化数据数组
            // 根据剧集信息数量循环构建数据数组
            for ($i = 0; $i < $mscInfoCount; $i++) {
                // 如果剧集数量大于1，则进行相应的操作
                if ($msiEpisodeUrlType === 1) {
                    $msiEpisodeUrl = trim($mscInfoList[$i]);  // 获取当前剧集URL
                    if ($msiEpisodeUrl === '') {
                        continue;
                    }
                    $msiEpisodeNumber = $NumberStartingEpisodes + $i;
                }
                // 构建当前剧集数据
                $data[] = [
                    'upload_user_id' => trim(input('post.uploadUserId')),  // 上传用户ID
                    'msi_id' => intval(input('post.msiId')),  // 剧集ID
                    'msi_episode_number' => $msiEpisodeNumber,  // 剧集编号
                    'msi_episode_url' => $msiEpisodeUrl,  // 剧集URL
                    'is_allow_only_vip' => intval(input('post.isAllowOnlyVip', 0)),  // 是否仅VIP可见
                    'paid_unlocking_type' => intval(input('post.paidUnlockingType', 0)),  // 付费解锁类型
                    'paid_unlocking_price' => floatval(input('post.paidUnlockingPrice', 0)),  // 付费解锁价格
                    'status' => intval(input('post.status', 0)),  // 状态
                    'display_status' => intval(input('post.displayStatus', 0)),  // 显示状态
                    'sort' => intval(input('post.sort', 0)),  // 排序
                    'create_time' => time(),  // 创建时间
                    'is_del' => 0,  // 是否删除
                    'much_id' => $this->much_id  // 关联ID
                ];
            }

            Db::startTrans();  // 开启事务

            try {
                Db::name('micro_series_content_list')->insertAll($data);  // 批量插入剧集内容数据
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => '保存失败，错误信息：' . $e->getMessage()]);
            }

            return json(['code' => 1, 'msg' => '保存成功']);  // 返回保存成功的JSON响应
        } else {
            $msiId = intval(input('get.msiId'));  // 获取剧集ID
            $msiInfo = Db::name('micro_series_info_list')
                ->where('id', $msiId)
                ->where('is_del', 0)
                ->where('much_id', $this->much_id)
                ->find();  // 查询剧集信息

            if ($msiInfo) {
                $this->assign('msiTitle', $msiInfo['title']);  // 分配剧集标题到模板
                $this->assign('msiId', $msiId);  // 分配剧集ID到模板
                return $this->fetch();  // 返回模板视图
            } else {
                abort(404);  // 若未找到剧集信息，则返回404错误
            }
        }
    }

    /**
     * 编辑短剧视频
     * @return mixed|\think\response\Json
     */
    public function edit_micro_series_content()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $data['upload_user_id'] = trim(input('post.uploadUserId'));
            $data['msi_episode_number'] = trim(input('post.msiEpisodeNumber'));
            $data['msi_episode_url'] = trim(input('post.msiEpisodeUrl'));
            $data['is_allow_only_vip'] = intval(input('post.isAllowOnlyVip', 0));
            $data['paid_unlocking_type'] = intval(input('post.paidUnlockingType', 0));
            $data['paid_unlocking_price'] = floatval(input('post.paidUnlockingPrice', 0));
            $data['status'] = intval(input('post.status', 0));
            $data['display_status'] = intval(input('post.displayStatus', 0));
            $data['sort'] = intval(input('post.sort', 0));

            Db::startTrans();
            try {
                Db::name('micro_series_content_list')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error, ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $fid = intval(input('get.fid'));
            $mscInfo = Db::name('micro_series_content_list')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->find();
            if ($mscInfo) {
                $msiInfo = Db::name('micro_series_info_list')->where('id', $mscInfo['msi_id'])->where('is_del', 0)->where('much_id', $this->much_id)->find();
                if ($msiInfo) {
                    $this->assign('msiTitle', $msiInfo['title']);
                    $this->assign('msiId', $msiInfo['id']);
                    $this->assign('list', $mscInfo);
                    return $this->fetch();
                } else {
                    abort(404);
                }
            } else {
                abort(404);
            }
        }
    }

    /**
     * 短剧视频详情
     * @return mixed
     */
    public function micro_series_content_detail()
    {
        $fid = intval(input('get.fid'));
        $mscInfo = Db::name('micro_series_content_list')->where('id', $fid)->where('much_id', $this->much_id)->find();
        if ($mscInfo) {
            $msiInfo = Db::name('micro_series_info_list')->where('id', $mscInfo['msi_id'])->where('is_del', 0)->where('much_id', $this->much_id)->find();
            $mscInfo['title'] = $msiInfo['title'];
            $mscInfo['user'] = Db::name('user')->where('id', $msiInfo['upload_user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
            $this->assign('list', $mscInfo);
            return $this->fetch();
        } else {
            abort(404);
        }
    }

    /**
     * 短剧视频审核
     * @return \think\response\Json
     */
    public function trial_micro_series_content()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $mscInfo = Db::name('micro_series_content_list')->where('id', $fid)->where('much_id', $this->much_id)->find();
            if (!$mscInfo) {
                return json(['code' => 0, 'msg' => '内容不存在']);
            }
            // 判断审核状态
            if (intval($mscInfo['status']) !== 0) {
                return json(['code' => 0, 'msg' => '系统繁忙，请刷新页面后重试！']);
            }
            $data['status'] = intval(input('post.process', 0));
            // 审核拒绝
            if ($data['status'] === 2) {
                $data['audit_reason'] = trim(input('post.inject', ''));
            }
            Db::startTrans();
            try {
                // 更改审核状态
                Db::name('micro_series_content_list')->where('id', $fid)->where('status', 0)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error, ' . $e->getMessage()]);
            }
            if ($data['status'] >= 1 && $data['status'] <= 2) {
                $tmplService = new TmplService();
                $templateData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $mscInfo['upload_user_id'],
                    'page' => 'yl_welore/pages/user_smail/index',
                    'keyword4' => date('Y年m月d日 H:i:s', time()),
                ];
                $maringText = '';
                //  二手交易审核通过
                switch ($data['status']) {
                    case 1:
                        $templateData['keyword1'] = "您发布的短剧视频已通过审核";
                        $maringText = "{$templateData['keyword1']}！";
                        break;
                    case 2:
                        $templateData['keyword1'] = "您发布的短剧视频未通过审核";
                        $maringText = "很抱歉，您发布短剧视频未通过审核，拒绝原因：{$data['audit_reason']}";
                        break;
                }
                //  模板消息
                $tmplService->add_template($templateData);
                //  站内信
                Db::name('user_smail')->insert(['user_id' => $mscInfo['upload_user_id'], 'maring' => $maringText, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /**
     * 删除短剧视频
     * @return \think\response\Json
     */
    public function del_micro_series_content()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = input('post.fid/a',array());
            Db::startTrans();
            try {
                Db::name('micro_series_content_list')->whereIn('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error, ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /**
     * 短剧视频解锁
     * @return mixed
     */
    public function micro_series_content_unlock()
    {
        $page = request()->get('page', 1);
        $list = Db::name('micro_series_unlock_paid_content_list')
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $this->defaultQuery()]])
            ->each(function ($item) {
                $item['msi_title'] = Db::name('micro_series_info_list')->where('id', $item['msi_id'])->where('much_id', $this->much_id)->value('title');
                $item['msc_episode_number'] = Db::name('micro_series_content_list')->where('id', $item['msc_id'])->where('much_id', $this->much_id)->value('msi_episode_number');
                $item['user'] = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /**
     * 短剧评论
     * @return mixed
     */
    public function micro_series_review()
    {
        $searchName = trim(input('get.searchName', ''));
        $page = request()->get('page', 1);
        $list = Db::name('micro_series_info_review')
            ->where('comment', 'like', "%{$searchName}%")
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->orderRaw('status <> 0 asc,case when status = 0 then id end asc,case when status <> 0 then id end desc')
            ->paginate(10, false, ['query' => ['s' => $this->defaultQuery(), 'searchName' => $searchName]])
            ->each(function ($item) {
                $item['msi_title'] = Db::name('micro_series_info_list')->where('id', $item['msi_id'])->where('much_id', $this->much_id)->value('title');
                $item['msc_episode_number'] = Db::name('micro_series_content_list')->where('id', $item['msc_id'])->where('much_id', $this->much_id)->value('msi_episode_number');
                $item['user'] = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
                return $item;
            });
        $expressionHtml = function ($val) {
            return Alternative::ExpressionHtml($val);
        };
        $this->assign('expressionHtml', $expressionHtml);
        $this->assign('list', $list);
        $this->assign('searchName', $searchName);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /**
     * 短剧评论审核
     * @return \think\response\Json|void
     */
    public function trial_micro_series_review()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $msiInfo = Db::name('micro_series_info_review')->where('id', $fid)->where('much_id', $this->much_id)->find();
            if (!$msiInfo) {
                return json(['code' => 0, 'msg' => '评论不存在']);
            }
            // 判断审核状态
            if (intval($msiInfo['status']) !== 0) {
                return json(['code' => 0, 'msg' => '系统繁忙，请刷新页面后重试！']);
            }
            $data['status'] = intval(input('post.process', 0));
            // 审核拒绝
            if ($data['status'] === 2) {
                $data['audit_reason'] = trim(input('post.inject', ''));
            }
            Db::startTrans();
            try {
                // 更改审核状态
                Db::name('micro_series_info_review')->where('id', $fid)->where('status', 0)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error, ' . $e->getMessage()]);
            }
            if ($data['status'] >= 1 && $data['status'] <= 2) {
                $tmplService = new TmplService();
                $templateData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $msiInfo['user_id'],
                    'page' => 'yl_welore/pages/user_smail/index',
                    'keyword4' => date('Y年m月d日 H:i:s', time()),
                ];
                $maringText = '';
                //  二手交易审核通过
                switch ($data['status']) {
                    case 1:
                        $templateData['keyword1'] = "您发布的短剧评论已通过审核";
                        $maringText = "{$templateData['keyword1']}！";
                        break;
                    case 2:
                        $templateData['keyword1'] = "您发布的短剧评论未通过审核";
                        $maringText = "很抱歉，您发布短剧评论未通过审核，拒绝原因：{$data['audit_reason']}";
                        break;
                }
                //  模板消息
                $tmplService->add_template($templateData);
                //  站内信
                Db::name('user_smail')->insert(['user_id' => $msiInfo['user_id'], 'maring' => $maringText, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /**
     * 删除短剧回复
     * @return \think\response\Json
     */
    public function del_micro_series_review()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            Db::startTrans();
            try {
                Db::name('micro_series_info_review')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error, ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /**
     * 短剧配置
     * @return mixed
     */
    public function micro_series_config()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = input('post.item/a', array());
            Db::startTrans();
            try {
                Db::name('micro_series_config')->where('much_id', $this->much_id)->update([
                    'custom_title' => trim($data['customTitle']),
                    'is_vip_free_look' => intval($data['isVipFreeLook']),
                    'is_allow_user_upload' => intval($data['isAllowUserUpload']),
                    'is_info_auto_review' => intval($data['isInfoAutoReview']),
                    'is_content_auto_review' => intval($data['isContentAutoReview']),
                    'is_comment_auto_review' => intval($data['isCommentAutoReview']),
                    'is_require_user_copyright' => intval($data['isRequireUserCopyright']),
                    'is_allow_user_charge' => intval($data['isAllowUserCharge']),
                    'min_amount_charged' => floatval($data['minAmountCharged']),
                    'max_amount_charged' => floatval($data['maxAmountCharged']),
                    'every_day_free_look_num' => intval($data['everyDayFreeLookNum']),
                    'is_enabled_look_ads_unlock_paid_content' => intval($data['isEnabledLookAdsUnlockPaidContent']),
                    'look_ads_unlock_paid_content_max_num' => intval($data['lookAdsUnlockPaidContentMaxNum']),
                    'charged_profit_rake_ratio' => intval($data['chargedProfitRakeRatio']) * 0.01,
                    'user_agreement' => $this->safe_html($data['userAgreement']),
                    'disclaimer_warranties' => $this->safe_html($data['disclaimerWarranties'])
                ]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $mscInfo = Db::name('micro_series_config')->where('much_id', $this->much_id)->find();
            if (!$mscInfo) {
                $mscInfo = [
                    'custom_title' => '短剧视频',
                    'is_vip_free_look' => 0,
                    'is_allow_user_upload' => 0,
                    'is_info_auto_review' => 0,
                    'is_content_auto_review' => 0,
                    'is_comment_auto_review' => 0,
                    'is_require_user_copyright' => 0,
                    'is_allow_user_charge' => 0,
                    'min_amount_charged' => 0.00,
                    'max_amount_charged' => 0.00,
                    'every_day_free_look_num' => 0,
                    'is_enabled_look_ads_unlock_paid_content' => 0,
                    'look_ads_unlock_paid_content_max_num' => 0,
                    'charged_profit_rake_ratio' => 0.5,
                    'user_agreement' => '',
                    'disclaimer_warranties' => '',
                    'much_id' => $this->much_id
                ];
                Db::startTrans();
                try {
                    $mscInfo['id'] = Db::name('micro_series_config')->insertGetId($mscInfo);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
            }
            $this->assign('list', $mscInfo);
            return $this->fetch();
        }
    }

    /**
     * 用户编号转用户名
     * @return \think\response\Json
     */
    public function user_id_to_user_name()
    {
        $uid = input('post.uid');
        $userInfo = Db::name('user')->where('id', $uid)->where('much_id', $this->much_id)->find();
        if ($userInfo) {
            return json(['code' => 1, 'userName' => emoji_decode($userInfo['user_nick_name'])]);
        } else {
            return json(['code' => 0]);
        }
    }

    /**
     * 导入短剧内容到数据
     * @return mixed
     */
    public function import_micro_series_content_to_data()
    {
        $fid = intval(input('get.fid'));
        // 获取短剧文件
        $file = request()->file('mixedFile');
        if (!$file) {
            return json(['code' => 0, 'msg' => '未上传文件']);
        }
        // 获取文件后缀名
        $allowedExtensions = ['csv'];
        $fileExtension = strtolower(pathinfo($file->getInfo('name'), PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $allowedExtensions)) {
            return json(['code' => 0, 'msg' => '文件类型不被允许']);
        }
        // 开始处理CSV文件
        $handle = fopen($file->getRealPath(), "r");
        if ($handle === false) {
            return json(['code' => 0, 'msg' => '无法打开文件']);
        }
        $data = array();
        $lineNumber = 0;
        while (($row = fgetcsv($handle)) !== false) {
            $lineNumber++;
            // 跳过空行
            if (empty(array_filter($row))) {
                continue;
            }
            if (count($row) < 5) {
                fclose($handle);
                return json(['code' => 0, 'msg' => "第 $lineNumber 行文件格式错误"]);
            }
            $episodeNumber = htmlspecialchars(strip_tags(trim($row[0])));
            $episodeUrl = filter_var(trim($row[1]), FILTER_SANITIZE_URL);
            $isAllowOnlyVip = intval(trim($row[2]));
            $paidUnlockingType = intval(trim($row[3]));
            $paidUnlockingPrice = intval(trim($row[4]));
            if (!filter_var($episodeUrl, FILTER_VALIDATE_URL)) {
                fclose($handle);
                return json(['code' => 0, 'msg' => "第 $lineNumber 行URL格式错误"]);
            }
            $data[] = [
                'msi_id' => $fid,
                'upload_user_id' => 0,
                'msi_episode_number' => $episodeNumber,
                'msi_episode_url' => $episodeUrl,
                'is_allow_only_vip' => $isAllowOnlyVip,
                'paid_unlocking_type' => $paidUnlockingType,
                'paid_unlocking_price' => $paidUnlockingPrice,
                'status' => 1,
                'display_status' => 1,
                'sort' => 0,
                'create_time' => time(),
                'is_del' => 0,
                'much_id' => $this->much_id,
            ];
        }
        fclose($handle);
        // 执行事务
        Db::startTrans();
        try {
            Db::name('micro_series_content_list')->insertAll($data);
            Db::commit();
            return json(['code' => 1, 'msg' => '导入成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'msg' => '导入失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 导出短剧内容到CSV
     * @return mixed
     */
    public function export_micro_series_content_to_csv()
    {
        $fid = intval(input('get.fid'));
        if (!$fid) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        //  获取数据
        $mscInfo = Db::name('micro_series_content_list')
            ->where('id', $fid)
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->field('id,msi_id,upload_user_id,status,audit_reason,display_status,sort,create_time,is_del,much_id', true)
            ->find();
        if (!$mscInfo) {
            return json(['code' => 0, 'msg' => '数据不存在']);
        }
        //  $mscInfo 数组的内容转 $data 的 csv格式
        $data[] = $mscInfo;
        // 定义CSV文件名
        $filename = time() . '.csv';
        // 设置HTTP头
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        // 打开输出缓冲区
        ob_start();
        $fp = fopen('php://output', 'w');
        // 自定义函数用于输出CSV行
        function outputCsvRow($row, $fp)
        {
            $line = array_map(function ($field) {
                // 使用双引号包围每个字段
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row);
            // 写入行
            fwrite($fp, implode(',', $line) . "\n");
        }

        // 输出数据
        foreach ($data as $row) {
            outputCsvRow($row, $fp);
        }
        // 关闭文件指针
        fclose($fp);
        // 获取缓冲区内容
        $csvContent = ob_get_clean();
        // 返回响应
        return Response::create($csvContent, 'csv')
            ->header('Content-Disposition', 'attachment;filename="' . $filename . '"')
            ->header('Cache-Control', 'max-age=0');
    }

}