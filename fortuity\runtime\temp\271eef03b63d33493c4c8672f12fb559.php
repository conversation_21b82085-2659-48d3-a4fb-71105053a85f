<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:86:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/essay/setails.html";i:1745512236;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755516152;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a></li>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>.post-detail-table{width:100%;border-collapse:collapse;table-layout:fixed;word-wrap:break-word;}.post-detail-table td{font-size:14px;padding:12px 20px;border:1px solid #e8e8e8;}.post-detail-table td:first-child{width:160px;text-align:right;color:#333;background:#fafafa;}.post-detail-table td:not(:first-child){color:#666;}.media-content{max-height:400px;overflow:auto;padding:10px;}.media-content img{max-width:97%;margin:8px 0;border-radius:4px;}.media-content video{width:97%;border-radius:4px;}.media-content audio{width:80%;margin:10px 0;}.action-buttons{text-align:right;margin-bottom:20px;}.action-buttons .btn{margin-left:10px;border-radius:4px;transition:all 0.3s;}.vote-progress{height:3rem;margin:10px 0;background:#f5f5f5;border-radius:4px;overflow:hidden;}.vote-progress-bar{height:100%;background:#5eb95e;transition:width 0.3s;}.vote-text{position:absolute;width:100%;line-height:3rem;color:#333;text-align:center;cursor:pointer;}.comment-item{border:1px solid #eee;border-radius:4px;padding:15px;margin-bottom:15px;}.comment-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:10px;}.comment-user{display:flex;align-items:center;}.comment-avatar{width:30px;height:30px;border-radius:50%;margin-right:10px;}.modal-content{background:#fff;border-radius:8px;padding:20px;}.modal-header{border-bottom:1px solid #eee;padding-bottom:15px;margin-bottom:15px;}.page-wrapper{width:100%;min-height:100%;padding:20px;margin-bottom:20px;background-color:#f5f7fa;border-radius:8px;}.post-detail-container{max-width:800px;margin:0 auto 20px auto;padding:20px;background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.08);}.oneself-content img{max-width:100% !important;height:auto !important;}.am-modal.am-modal-no-btn {position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 1110;display: none;overflow: hidden;background-color: rgba(0, 0, 0, 0.4);}.modal-dialog {position: relative;width: 400px;margin: 100px auto;background: #fff;border-radius: 8px;box-shadow: 0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08);}.modal-dialog .am-modal-hd {padding: 16px;border-bottom: 1px solid #eee;position: relative;font-size: 16px;font-weight: 500;color: #333;}.modal-dialog .am-modal-hd .am-close {position: absolute;right: 20px;top: 50%;transform: translateY(-50%);font-size: 18px;color: #999;cursor: pointer;}.modal-dialog .am-modal-bd {padding: 20px;display: flex;flex-direction: column;justify-content: center;min-height: 150px;}.modal-dialog .am-form-group {margin: 10px 0;display: flex;align-items: center;}.modal-dialog .am-form-label {width: 80px;text-align: right;padding-right: 12px;color: #333;font-size: 14px;}.modal-dialog .am-form-field-wrap {flex: 1;display: flex;align-items: center;}.modal-dialog .tpl-form-input {flex: 1;height: 36px;padding: 0 10px;border: 1px solid #ddd;border-radius: 4px;outline: none;}.modal-dialog .am-form-help {margin-left: 8px;color: #999;font-size: 14px;}.modal-dialog .btn-container {text-align: center;margin-top: 24px;}.modal-dialog .am-btn {padding: 8px 24px;background: #1890ff;color: #fff;border: none;border-radius: 4px;transition: all 0.3s;font-size: 14px;}.modal-dialog .am-btn:hover {background: #40a9ff;}.am-modal-dialog {position: relative;background: #fff;border-radius: 6px;width: 400px;margin: 15vh auto 0;}.am-modal-hd {padding: 15px;font-size: 16px;font-weight: 500;text-align: left;border-bottom: 1px solid #eee;}.am-modal-bd {padding: 20px;}.am-form-group {margin-bottom: 1rem;display: flex;align-items: center;margin: 10px 0;}.am-form-label {width: 80px;text-align: right;margin-right: 10px;}.am-form-field-wrap {display: flex;align-items: center;flex: 1;}.tpl-form-input {flex: 1;height: 36px;padding: 0 10px;border: 1px solid #ddd;border-radius: 4px;}.am-form-help {margin-left: 8px;color: #999;white-space: nowrap;}.am-form-group.btn-wrap {display: flex;justify-content: center;margin-top: 20px;width: 100%;}.am-btn.am-btn-primary {min-width: 100px;margin: 0 auto;}</style>
<div class="page-wrapper">
    <div class="post-detail-container">
        <header class="post-header">
            <h2 class="am-icon-file-text"> 帖子详情</h2>
            <div class="action-buttons">
                <a href="<?php echo url('essay/index'); ?>" target="_blank">
                    <button type="button" class="am-btn am-btn-default am-btn-sm">帖子列表</button>
                </a>
                <a href="<?php echo url('essay/editWritings'); ?>&prid=<?php echo $list['id']; ?>" target="_blank">
                    <button type="button" class="am-btn am-btn-sm" style="background: #294c64;color: white;">
                        编辑帖子
                    </button>
                </a>
                <?php if($list['whether_delete']==0): ?>
                <button type="button" class="am-btn am-btn-danger am-btn-sm" onclick="satypical('3');">删除此贴</button>
                <?php endif; if($list['whether_delete']==1): ?>
                <button type="button" class="am-btn am-btn-success am-btn-sm" onclick="restoreper('<?php echo $list['id']; ?>');">恢复此帖</button>
                <?php endif; ?>
            </div>
        </header>

        <section class="post-content">
            <table class="post-detail-table">
                <tr>
                    <td class="am-text-middle" style="width:35%;"><?php echo $defaultNavigate['landgrave']; ?>名称</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2"><?php echo $list['realm_name']; ?></td>
                </tr>
                <tr>
                    <td class="am-text-middle">发帖用户</td>
                    <td class="am-text-middle" colspan="2">
                        <?php if($list['uvirtual'] == 0): ?>
                        <a href="<?php echo url('user/index'); ?>&openid=<?php echo $list['user_wechat_open_id']; ?>&page=1" title="<?php echo emoji_decode($list['user_nick_name']); ?>" target="_blank">
                            <?php echo emoji_decode($list['user_nick_name']); ?>
                        </a>
                        <?php else: ?>
                        <a href="<?php echo url('user/theoretic'); ?>&hazy_name=<?php echo filter_emoji($list['user_nick_name']); ?>&page=1" target="_blank" title="<?php echo emoji_decode($list['user_nick_name']); ?>">
                            <?php echo emoji_decode($list['user_nick_name']); ?>
                        </a>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle"><?php if($list['study_type']==3): ?>活动名称<?php else: ?>发帖标题<?php endif; ?></td>
                    <td class="am-text-middle" colspan="2">
                        <?php if($list['study_title']!=''): ?> <?php echo emoji_decode($list['study_title']); else: ?> 无 <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">帖子类型</td>
                    <td class="am-text-middle" colspan="2">
                        <?php switch($list['study_type']): case "0": ?>图文<?php break; case "1": ?>语音<?php break; case "2": ?>视频<?php break; case "3": ?>活动<?php break; case "4": ?>单选投票<?php break; case "5": ?>多选投票<?php break; case "6": ?>视频号<?php break; endswitch; ?>帖
                    </td>
                </tr>
                <?php if($gambitInfo): ?>
                <tr>
                    <td class="am-text-middle">话题类型</td>
                    <td class="am-text-middle" colspan="2">
                        <a href="<?php echo url('essay/index'); ?>&egon=0&tgid=<?php echo $gambitInfo['id']; ?>" target="_blank">
                            <?php echo $gambitInfo['gambit_name']; ?>
                        </a>
                    </td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td class="am-text-middle">功能类型</td>
                    <td class="am-text-middle" colspan="2">
                        <?php if($list['is_buy'] > 0): ?>付费帖子<?php elseif($list['welfare']): ?>红包帖子<?php else: ?>普通帖子<?php endif; ?>
                    </td>
                </tr>
                <?php if($callPhonePluginKey && $list['call_phone']): ?>
                <tr>
                    <td class="am-text-middle">联系方式</td>
                    <td class="am-text-middle" colspan="2">
                        <a href="tel:<?php echo $list['call_phone']; ?>">
                            <?php echo $list['call_phone']; ?>
                        </a>
                    </td>
                </tr>
                <?php endif; if($list['is_buy']>0): ?>
                <tr>
                    <td class="am-text-middle">付费类型</td>
                    <td class="am-text-middle" colspan="2">
                        <?php if(!$netDiscPluginKey): ?>
                        内容付费
                        <?php else: switch($list['is_buy']): case "1": ?>内容付费<?php break; case "2": ?>文件付费<?php break; case "3": ?>整体付费<?php break; endswitch; endif; ?>
                    </td>
                </tr>
                <?php if($list['is_buy']>1 && $netDiscPluginKey): ?>
                <tr>
                    <td class="am-text-middle">文件详情</td>
                    <td class="am-text-middle" colspan="2">
                        <?php if($list['buyFilesInfo']['is_dir']): ?>
                        <span title="<?php echo $list['file_name']; ?>" style="cursor: pointer;" onclick="viewFiles();">
                            <i class="am-icon-folder-o" style="margin-right: 3px;"></i>
                            <?php echo $list['buyFilesInfo']['file_name']; ?>
                        </span>
                        <?php else: ?>
                        <a href="<?php echo $list['buyFilesInfo']['ncInfo']['file_address']; ?>" target="_blank" title="<?php echo $list['buyFilesInfo']['file_name']; ?>.<?php echo $list['buyFilesInfo']['ncInfo']['file_suffix']; ?>">
                            <i class="am-icon-file-text-o" style="margin-right: 3px;"></i>
                            <?php echo subtext($list['buyFilesInfo']['file_name'],6); ?>.<?php echo $list['buyFilesInfo']['ncInfo']['file_suffix']; ?>
                        </a>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endif; endif; if($list['welfare']): switch($list['welfare']['initial_type']): case "0": ?>
                <tr>
                    <td class="am-text-middle">派发类型</td>
                    <td class="am-text-middle" colspan="2">
                        <?php switch($list['welfare']['red_type']): case "0": ?>普通红包<?php break; case "1": ?>拼手气红包<?php break; endswitch; ?>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">派发金额</td>
                    <td class="am-text-middle" colspan="2"><?php echo $list['welfare']['initial_conch']; ?> ( <?php echo $defaultNavigate['currency']; ?> )</td>
                </tr>
                <tr>
                    <td class="am-text-middle">派发数量</td>
                    <td class="am-text-middle" colspan="2"><?php echo $list['welfare']['initial_quantity']; ?> ( 个 )</td>
                </tr>
                <tr>
                    <td class="am-text-middle">剩余数量</td>
                    <td class="am-text-middle" colspan="2"><?php echo $list['welfare']['surplus_quantity']; ?> ( 个 )</td>
                </tr>
                <tr>
                    <td class="am-text-middle">剩余金额</td>
                    <td class="am-text-middle" colspan="2"><?php echo $list['welfare']['surplus_conch']; ?> ( <?php echo $defaultNavigate['currency']; ?> )</td>
                </tr>
                <?php break; case "1": ?>
                <tr>
                    <td class="am-text-middle">派发类型</td>
                    <td class="am-text-middle" colspan="2">
                        <?php switch($list['welfare']['red_type']): case "0": ?>普通红包<?php break; case "1": ?>拼手气红包<?php break; endswitch; ?>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">派发金额</td>
                    <td class="am-text-middle" colspan="2"><?php echo $list['welfare']['initial_fraction']; ?> ( <?php echo $defaultNavigate['confer']; ?> )</td>
                </tr>
                <tr>
                    <td class="am-text-middle">派发数量</td>
                    <td class="am-text-middle" colspan="2"><?php echo $list['welfare']['initial_quantity']; ?> ( 个 )</td>
                </tr>
                <tr>
                    <td class="am-text-middle">剩余数量</td>
                    <td class="am-text-middle" colspan="2"><?php echo $list['welfare']['surplus_quantity']; ?> ( 个 )</td>
                </tr>
                <tr>
                    <td class="am-text-middle">剩余金额</td>
                    <td class="am-text-middle" colspan="2"><?php echo $list['welfare']['surplus_fraction']; ?> ( <?php echo $defaultNavigate['confer']; ?> )</td>
                </tr>
                <?php break; endswitch; endif; if($list['is_buy']>0): ?>
                    <tr>
                        <td class="am-text-middle">付费查看价格</td>
                        <td class="am-text-middle" colspan="2">
                            <?php echo $list['buy_price']; switch($list['buy_price_type']): case "0": ?> ( <?php echo $defaultNavigate['currency']; ?> )<?php break; case "1": ?> ( <?php echo $defaultNavigate['confer']; ?> )<?php break; endswitch; ?>
                        </td>
                    </tr>
                    <?php if($list['study_status']!=0): ?>
                    <tr>
                        <td class="am-text-middle">付费购买人数</td>
                        <?php if($list['user_buy_count']==0): ?>
                        <td class="am-text-middle" colspan="2">
                            <?php echo intval($list['user_buy_count']); ?> 人
                        </td>
                        <?php else: ?>
                        <td class="am-text-middle" style="width:40%;">
                            <?php echo intval($list['user_buy_count']); ?> 人
                        </td>
                        <td class="am-text-middle" style="width:20%;text-align:center;">
                            <span class="am-icon-search" style="cursor:pointer;" onclick="tance('<?php echo $list['id']; ?>');">
                                查看详情
                            </span>
                        </td>
                        <?php endif; ?>
                    </tr>
                    <tr>
                        <td class="am-text-middle">共获收益<?php switch($list['buy_price_type']): case "0": ?><?php echo $defaultNavigate['currency']; break; case "1": ?><?php echo $defaultNavigate['confer']; break; endswitch; ?> ( 税前 )</td>
                        <td class="am-text-middle" colspan="2">
                            <?php echo number_format($list['fraction_buy_count'],2); switch($list['buy_price_type']): case "0": ?> ( <?php echo $defaultNavigate['currency']; ?> )<?php break; case "1": ?> ( <?php echo $defaultNavigate['confer']; ?> )<?php break; endswitch; ?>
                        </td>
                    </tr>
                    <tr>
                        <td class="am-text-middle">共获收益<?php switch($list['buy_price_type']): case "0": ?><?php echo $defaultNavigate['currency']; break; case "1": ?><?php echo $defaultNavigate['confer']; break; endswitch; ?> ( 税后 )</td>
                        <td class="am-text-middle" colspan="2">
                            <?php echo number_format($list['fraction_buy_tax_count'],2); switch($list['buy_price_type']): case "0": ?> ( <?php echo $defaultNavigate['currency']; ?> )<?php break; case "1": ?> ( <?php echo $defaultNavigate['confer']; ?> )<?php break; endswitch; ?>
                        </td>
                    </tr>
                    <?php endif; endif; if(!(empty($paperWechatChannelVideoInfo['feed_token']) || (($paperWechatChannelVideoInfo['feed_token'] instanceof \think\Collection || $paperWechatChannelVideoInfo['feed_token'] instanceof \think\Paginator ) && $paperWechatChannelVideoInfo['feed_token']->isEmpty()))): ?>
                <tr>
                    <td class="am-text-middle" style="width:35%;">feed-token</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;">
                            <?php echo $paperWechatChannelVideoInfo['feed_token']; ?>
                        </div>
                    </td>
                </tr>
                <?php endif; if(!(empty($list['study_content']) || (($list['study_content'] instanceof \think\Collection || $list['study_content'] instanceof \think\Paginator ) && $list['study_content']->isEmpty()))): ?>
                <tr>
                    <td class="am-text-middle" style="width:35%;"><?php if($list['study_type']==3): ?>活动内容<?php else: ?>发帖内容<?php endif; ?></td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;">
                            <?php echo emoji_decode($list['study_content']); ?>
                        </div>
                    </td>
                </tr>
                <?php endif; if($pvInfo): ?>
                <tr>
                    <td class="am-text-middle" style="width:35%;">投票选项</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="position:relative;width:100%;overflow:hidden;padding-top:10px;">
                            <?php if(is_array($pvInfo) || $pvInfo instanceof \think\Collection || $pvInfo instanceof \think\Paginator): $i = 0; $__LIST__ = $pvInfo;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <div class="am-progress" style="height:3rem;">
                                <div class="am-progress-bar am-progress-bar-success" <?php echo $vo['ratio']; ?>>
                                    <span title="点击查看投票详情" onclick="votingDetails('<?php echo $vo['id']; ?>');" style="position:absolute;width:100%;color:black;line-height:2.8rem;left:0;cursor:pointer;">
                                        <?php echo $vo['ballot_name']; ?> <?php echo $vo['voters']; ?>票
                                    </span>
                                </div>
                            </div>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">投票截止时间</td>
                    <td class="am-text-middle" colspan="2">
                        <?php if($list['vote_deadline']): ?> <?php echo date('Y-m-d H:i:s',$list['vote_deadline']); else: ?> 没有截止时间 <?php endif; ?>
                    </td>
                </tr>
                <?php endif; if($list['study_type']==3): ?>
                <tr>
                    <td class="am-text-middle" style="width:35%;">活动地址</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;">
                            <a href="https://uri.amap.com/marker?position=<?php echo $briskTeamInfo['brisk_address_longitude']; ?>,<?php echo $briskTeamInfo['brisk_address_latitude']; ?>" target="_blank"  style="color:#999;">
                                <?php echo $briskTeamInfo['brisk_address']; ?>
                            </a>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle" style="width:35%;">活动开始时间</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;"><?php echo date('Y-m-d H:i',$briskTeamInfo['start_time']); ?></div>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle" style="width:35%;">活动结束时间</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;"><?php echo date('Y-m-d H:i',$briskTeamInfo['end_time']); ?></div>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle" style="width:35%;">活动人数</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;"><?php if($briskTeamInfo['number_of_people']>0): ?> <?php echo $briskTeamInfo['number_of_people']; ?> 人 <?php else: ?> 不限制人数 <?php endif; ?></div>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle" style="width:35%;">报名人数</td>
                    <td class="am-text-middle" style="width:65%;">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;"><?php echo $briskTeamInfo['userBriskTeamCount']; ?> 人</div>
                    </td>
                    <td class="am-text-middle" style="width:20%;text-align:center;">
                        <span class="am-icon-search" style="cursor:pointer;" onclick="understand('<?php echo $list['id']; ?>');" >查看报名详情</span>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle" style="width:35%;">验证人数</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;"><?php echo $briskTeamInfo['userBriskTeamWriteOffCount']; ?> 人</div>
                    </td>
                </tr>
                <?php endif; if($list['study_type']!=2): if(!(empty($list['image_part']) || (($list['image_part'] instanceof \think\Collection || $list['image_part'] instanceof \think\Paginator ) && $list['image_part']->isEmpty()))): ?>
                <tr>
                    <td class="am-text-middle">发帖图片</td>
                    <td class="am-text-middle" colspan="2">
                        <div style="max-height:400px;overflow:auto;">
                            <?php if(is_array($list['image_part']) || $list['image_part'] instanceof \think\Collection || $list['image_part'] instanceof \think\Paginator): $i = 0; $__LIST__ = $list['image_part'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <a href="<?php echo $vo; ?>" target="_blank">
                                <img src="<?php echo $vo; ?>" style="width: 97%;margin: 3px;"/>
                            </a>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </div>
                    </td>
                </tr>
                <?php endif; endif; if(!(empty($list['study_voice']) || (($list['study_voice'] instanceof \think\Collection || $list['study_voice'] instanceof \think\Paginator ) && $list['study_voice']->isEmpty()))): ?>
                <tr>
                    <td class="am-text-middle">发帖语音</td>
                    <td class="am-text-middle" colspan="2">
                        <audio controls="controls" style="width:80%;">
                            <source src="<?php echo $list['study_voice']; ?>" type="audio/mpeg">
                            您的浏览器不支持音频标签。
                        </audio>
                    </td>
                </tr>
                <?php endif; if($list['study_type']==2||$list['study_video']!=''): ?>
                <tr>
                    <td class="am-text-middle">发帖视频</td>
                    <td class="am-text-middle" colspan="2">
                        <video id="videoLoad" src="<?php echo $list['study_video']; ?>" controls="controls" style="width:97%;height:350px;">
                            您的浏览器不支持视频标签。
                        </video>
                    </td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td class="am-text-middle">浏览次数</td>
                    <td class="am-text-middle" colspan="2">
                        <?php echo $list['study_heat']; ?> 次
                        <span style="font-size:12px;margin-left:5px;cursor:pointer;" data-am-modal="{target: '#shandsel-1', closeViaDimmer: 1, width: 400, height: 185}" title="修改浏览次数" onclick="sendScamperFrequencySpecifyDefaultValue(0);">
                            <span class="am-icon-edit"></span> 修改浏览次数
                        </span>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">点赞人数</td>
                    <td class="am-text-middle" colspan="2">
                        <?php echo $list['study_laud']; ?> 人
                        <span style="font-size:12px;margin-left:5px;cursor:pointer;" data-am-modal="{target: '#shandsel-2', closeViaDimmer: 1, width: 400, height: 185}" title="修改点赞人数" onclick="sendScamperFrequencySpecifyDefaultValue(1);">
                            <span class="am-icon-edit"></span> 修改点赞人数
                        </span>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">回复次数</td>
                    <td class="am-text-middle" colspan="2"><?php echo $list['study_repount']; ?> 次</td>
                </tr>
                <tr>
                    <td class="am-text-middle">发帖位置</td>
                    <td class="am-text-middle" colspan="2">
                        <?php if($list['address_details']): ?><a href="https://uri.amap.com/marker?position=<?php echo $list['address_longitude']; ?>,<?php echo $list['address_latitude']; ?>" target="_blank" style="color:#999;"><?php echo $list['address_details']; ?></a><?php else: ?> 未知 <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">发布时间</td>
                    <td class="am-text-middle" colspan="2"><?php echo date('Y-m-d H:i:s',$list['adapter_time']); ?></td>
                </tr>
                <tr>
                    <td class="am-text-middle">审核状态</td>
                    <td class="am-text-middle" colspan="2">
                        <?php if($list['study_status']==0): ?>
                        <span class="am-text-warning">待审核</span>
                        <?php elseif($list['study_status']==1): ?>
                        <span class="am-text-success">已通过</span>
                        <?php elseif($list['study_status']==2): ?>
                        <span class="am-text-secondary">已打回</span>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php if($list['study_status']==2): ?>
                <tr>
                    <td class="am-text-middle">打回原因</td>
                    <td class="am-text-middle"><?php echo $list['reject_reason']; ?></td>
                </tr>
                <?php endif; if(!(empty($list['prove_time']) || (($list['prove_time'] instanceof \think\Collection || $list['prove_time'] instanceof \think\Paginator ) && $list['prove_time']->isEmpty()))): ?>
                <tr>
                    <td class="am-text-middle">审核时间</td>
                    <td class="am-text-middle" colspan="2"><?php echo date('Y-m-d H:i:s',$list['prove_time']); ?></td>
                </tr>
                <?php endif; if($list['whether_delete']==1): ?>
                <tr>
                    <td class="am-text-middle">删除时间</td>
                    <td class="am-text-middle" colspan="2"><?php echo date('Y-m-d H:i:s',$list['whetd_time']); ?></td>
                </tr>
                <tr>
                    <td class="am-text-middle">删除原因</td>
                    <td class="am-text-middle" colspan="2"><?php echo emoji_decode($list['whether_reason']); ?></td>
                </tr>
                <?php endif; if($correctPluginKey): ?>
                <tr>
                    <td class="am-text-middle">点评信息</td>
                    <td class="am-text-middle" colspan="2">
                        <?php if(is_array($prsList) || $prsList instanceof \think\Collection || $prsList instanceof \think\Paginator): $i = 0; $__LIST__ = $prsList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <div style="border: 1px dashed #ccc;margin-bottom: 5px;padding: 3px 0 0 3px;">
                            <div style="width:100%;display:flex;">
                                <div style="width: 60%">
                                    <a href="<?php echo url('user/index'); ?>&openid=<?php echo $vo['user_openid']; ?>&page=1" title="<?php echo emoji_decode($vo['user_name']); ?>" target="_blank">
                                        <img src="<?php echo $vo['user_head_img']; ?>" style="width:25px;height:25px;border-radius:50%;"/>
                                        <span style="font-size:13px;margin-left:3px;position:relative;top:2px;"><?php echo emoji_decode($vo['user_name']); ?></span>
                                    </a>
                                </div>
                                <div style="width: 40%;text-align: right;margin-right: 5px;">
                                    <small>
                                        <?php if($vo['is_show']): ?>
                                        <span title="所有用户都能看到此点评">公开点评</span>
                                        <?php else: ?>
                                        <span title="仅被点评的用户看到此点评">私密点评</span>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                            <div style="width:90%;padding:2px 0 0 9%;">
                                点评分数：<?php echo $vo['assess_score']; ?>
                            </div>
                            <div style="width:90%;padding:2px 0 0 9%;">
                                <?php echo emoji_decode($vo['assess_content']); ?>
                            </div>
                            <div style="width:100%;font-size:13px;padding-left:9%;margin:10px 0;float:none;">
                                点评时间：<?php echo date('Y-m-d H:i:s',$vo['assess_time']); ?>
                            </div>
                            <div style="width:50%;font-size:13px;padding-left:9%;margin:3px 0 10px 0;float:left;">
                                审核状态：<?php switch($vo['audit_status']): case "0": ?>
                                <span style="color: orange;">待审核</span>
                                <?php break; case "1": ?>
                                <span style="color: green;">已通过</span>
                                <?php break; case "2": ?>
                                <span style="color: red;">已拒绝</span>
                                <?php break; endswitch; ?>
                            </div>
                            <div style="width:50%;float:left;font-size:10px;margin:4px 0 10px 0;padding-right:5px;text-align:right;">
                                <span style="cursor:pointer;" onclick="delCorrect('<?php echo $vo['id']; ?>')">删除</span>
                            </div>
                            <div style="clear:both;"></div>
                        </div>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                        <span style="cursor: pointer;color: #1E9FFF;" title="点击新增点评数据" onclick="newCorrect();">
                            新增一条点评信息
                        </span>
                    </td>
                </tr>
                <?php endif; ?>
            </table>
            <?php if($list['study_status']==0): ?>
            <div class="am-form-group btn-wrap">
                <div class="am-u-sm-12" style="display: flex;justify-content: center;">
                    <button type="button" class="am-btn am-btn-success am-btn-sm  am-round" style="margin-right: 120px;" onclick="satypical('1');">通过
                    </button>
                    <button type="button" class="am-btn am-btn-secondary am-btn-sm  am-round" onclick="satypical('2');">
                        打回
                    </button>
                </div>
            </div>
            <?php endif; ?>
        </section>
    </div>
</div>

<!-- 修改浏览次数模态窗口 -->
<div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel-1">
    <div class="am-modal-dialog">
        <div class="am-modal-hd">
            修改浏览次数
            <a href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
        </div>
        <div class="am-modal-bd">
            <div class="am-form-group">
                <label class="am-form-label">浏览次数</label>
                <div class="am-form-field-wrap">
                    <input type="number" id="newStudyHeat" oninput="digitalCheck(this);" class="tpl-form-input" placeholder="请输入浏览次数">
                    <span class="am-form-help">次</span>
                </div>
            </div>
            <div class="am-form-group btn-wrap">
                <button type="button" class="am-btn am-btn-primary" onclick="sendScamperFrequency(0);">保存</button>
            </div>
        </div>
    </div>
</div>

<div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel-2">
    <div class="am-modal-dialog">
        <div class="am-modal-hd">
            修改点赞人数
            <a href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
        </div>
        <div class="am-modal-bd">
            <div class="am-form-group">
                <label class="am-form-label">点赞人数</label>
                <div class="am-form-field-wrap">
                    <input type="number" id="newStudyLaud" oninput="digitalCheck(this);" class="tpl-form-input" placeholder="请输入点赞人数">
                    <span class="am-form-help">人</span>
                </div>
            </div>
            <div class="am-form-group btn-wrap">
                <button type="button" class="am-btn am-btn-primary" onclick="sendScamperFrequency(1);">保存</button>
            </div>
        </div>
    </div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<?php if($much_role=='founder'): ?>
<script>
    var updateFix = function () {
        layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("<?php echo url('index/repairMissing'); ?>", function (data) {
                if (data !== false) {
                    layer.msg('数据库缺失字段修复完成', {time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('修复失败', {time: 1600}, function () {
                        location.reload();
                    });
                }
            });
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }
</script>
<?php endif; ?>
<script>
    new Vue({
        el: '#menu',
        data() {
            return {
                menuActive: '<?php echo $menuActive; ?>',
                menuList: []
            };
        },
        created() {
            const menuList = '<?php echo $menuList; ?>';
            if (menuList !== '') {
                const menuData = JSON.parse(decodeURIComponent(atob(menuList)));
                for (let i = 0; i < menuData.length; i++) {
                    if (menuData[i].pid === 0) {
                        menuData[i].childMenuItems = [];
                        this.menuList.push(menuData[i]);
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        if (menuData[i].pid === this.menuList[j].id) {
                            const childItem = JSON.parse(JSON.stringify(menuData[i]));
                            childItem.grandsonList = [];
                            this.menuList[j].childMenuItems.push(childItem);
                            break;
                        }
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        for (let k = 0; k < this.menuList[j].childMenuItems.length; k++) {
                            if (menuData[i].pid === this.menuList[j].childMenuItems[k].id) {
                                this.menuList[j].childMenuItems[k].grandsonList.push(menuData[i]);
                                break;
                            }
                        }
                    }
                }
            }
        }
    });

    $(function () {
        setInterval(reballot, 15000);
    });

    var reballot = function () {
        $.getJSON("<?php echo url('ordinary'); ?>", function (data) {
            var i = 0;
            if (data.notice > 0 || data.vacant > 0) {
                i++;
            }
            $('#notice-0,#notice-1').text(data.notice);
            $('#vacant-0,#vacant-1').text(data.vacant);
            if ((data.notice + data.vacant) <= data.preCount) {
                i = 0;
            } else {
                $.post("<?php echo url('receipt'); ?>", {'multiply': (data.notice + data.vacant)});
            }
            if (i > 0) {
                var player = $("#backPlayer")[0];
                player.play();
            }
        });
    }

    var retakeCache = function () {
        $.get("<?php echo url('index/purgeCache'); ?>", function () {
            layer.msg('缓存清理完成', {time: 1000}, function () {
                location.reload();
            });
        });
    }
</script>
<script>
// 添加全局变量
window.EDITOR_MODE = false;  // 标记是否在编辑器模式

// AJAX 拦截器 - 自动还原代理 URL 为原始 URL
(function() {
    // 代理 URL 的匹配模式
    var proxyUrlPattern = /[?&]url=([^&]+)/;
    var proxyBaseUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>";

    // URL 还原函数
    function restoreOriginalUrl(url) {
        try {
            // 检查是否是代理 URL
            if (typeof url === 'string' && url.indexOf(proxyBaseUrl) !== -1) {
                var match = url.match(proxyUrlPattern);
                if (match && match[1]) {
                    // 解码并返回原始 URL
                    return decodeURIComponent(match[1]);
                }
            }
            return url;
        } catch (e) {
            // 出错时返回原始值，确保不影响正常功能
            console.warn('URL restore failed:', e);
            return url;
        }
    }

    // 增强版 URL 还原函数 - 支持从 DOM 元素的 data 属性获取原始 URL
    window.getOriginalSrc = function(element) {
        try {
            if (element && element.getAttribute) {
                // 优先从 data-original-src 获取
                var originalSrc = element.getAttribute('data-original-src');
                if (originalSrc) {
                    return originalSrc;
                }
                // 其次尝试从当前 src 还原
                var currentSrc = element.src || element.getAttribute('src');
                return restoreOriginalUrl(currentSrc);
            }
            return element;
        } catch (e) {
            console.warn('getOriginalSrc failed:', e);
            return element;
        }
    };

    // 递归处理数据对象，还原其中的代理 URL
    function restoreDataUrls(data) {
        try {
            if (typeof data === 'string') {
                return restoreOriginalUrl(data);
            } else if (Array.isArray(data)) {
                return data.map(restoreDataUrls);
            } else if (data && typeof data === 'object') {
                var restored = {};
                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        restored[key] = restoreDataUrls(data[key]);
                    }
                }
                return restored;
            }
            return data;
        } catch (e) {
            // 出错时返回原始数据
            console.warn('Data restore failed:', e);
            return data;
        }
    }

    // 保存原始的 jQuery AJAX 方法
    var originalAjax = $.ajax;
    var originalPost = $.post;
    var originalGet = $.get;

    // 重写 $.ajax
    $.ajax = function(options) {
        try {
            if (options && options.data) {
                options.data = restoreDataUrls(options.data);
            }
        } catch (e) {
            console.warn('AJAX data restore failed:', e);
        }
        return originalAjax.apply(this, arguments);
    };

    // 重写 $.post
    $.post = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('POST data restore failed:', e);
        }
        return originalPost.call(this, url, data, success, dataType);
    };

    // 重写 $.get
    $.get = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('GET data restore failed:', e);
        }
        return originalGet.call(this, url, data, success, dataType);
    };
})();

// 修改检测函数
function checkResourceAccess() {
    var isHttps = window.location.protocol === 'https:';
    
    if (isHttps) {
        // 处理图片
        document.querySelectorAll('img[src^="http://"]').forEach(function(img) {
            // 如果是编辑器模式，所有图片都使用代理URL显示
            if(window.EDITOR_MODE) {
                var originalSrc = img.getAttribute('data-src') || img.src;
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
                return;
            }
            
            // 非编辑器模式下排除富文本编辑器内的图片
            if(img.closest('.w-e-text') || img.closest('.w-e-text-container')) {
                return;
            }
            
            var originalSrc = img.getAttribute('data-src') || img.src;
            var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);

            var tmpImg = new Image();
            tmpImg.onload = function() {
                // 原始图片可访问，不做处理
            }
            tmpImg.onerror = function() {
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = proxyUrl;
            }
            tmpImg.src = originalSrc;
        });
        
        // 处理视频
        document.querySelectorAll('video').forEach(function(video) {
            // 处理video的src属性
            if (video.src && video.src.startsWith('http://')) {
                handleResource(video, 'src');
            }
            
            // 处理source标签
            video.querySelectorAll('source[src^="http://"]').forEach(function(source) {
                handleResource(source, 'src');
            });
        });
    }
}

// 统一的资源处理函数
function handleResource(element, srcAttr) {
    var originalSrc = element[srcAttr];
    var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
    
    // 创建临时对象测试资源是否可访问
    var tmpObj = new Image(); // 用Image对象测试连接可用性
    tmpObj.onload = function() {
        // 原始资源可访问，不做处理
    }
    tmpObj.onerror = function() {
        // 原始资源不可访问，切换到代理
        // 存储原始 URL 到 data 属性
        element.setAttribute('data-original-src', originalSrc);
        element[srcAttr] = proxyUrl;

        // 同时更新父级a标签的href
        var parent = element.parentElement;
        if (parent && parent.tagName === 'A') {
            parent.setAttribute('data-original-href', parent.href);
            parent.href = proxyUrl;
        }
    }
    tmpObj.src = originalSrc;
}

// 页面加载完成后执行检查
window.addEventListener('load', checkResourceAccess);

// 动态加载的内容处理
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            checkResourceAccess();
        }
    });
});

// 监听DOM变化
observer.observe(document.body, {
    childList: true,
    subtree: true
});
</script>

<script>

    var tance = function (reid) {
        var page = 1;
        layer.open({
            type: 2,
            id: 'erpUp',
            title: '付费详情',
            shadeClose: true,
            shade: 0.8,
            area: ['600px', '6%'],
            scrollbar: true,
            content: "<?php echo url('essay/buyPaperUser'); ?>&reid=" + reid,
            success: function () {
                $($('#erpUp iframe')[0].contentWindow).scroll(function () {
                    var iframeScrollHeight = $("#erpUp iframe").get(0).contentWindow.document.body.scrollHeight;
                    var iframeScrollTop = $($('#erpUp iframe')[0].contentWindow).scrollTop();
                    var iframeClientHeight = $($('#erpUp iframe')[0].contentWindow).height();
                    if (iframeScrollHeight - iframeClientHeight == iframeScrollTop) {
                        $.ajaxSettings.async = false;
                        $.post("<?php echo url('essay/buyPaperUser'); ?>", {
                            jetty: 1,
                            reid: reid,
                            page: ++page
                        }, function (data) {
                            if (data.length != 0) {
                                for (var i = 0; i < data.length; i++) {
                                    var html = '<tr>';
                                    html += '<td class="am-table-centered am-text-middle" title="' + data[i].user_nick_name + '">';
                                    html += '    <img src="' + data[i].user_head_sculpture + '" style="width:50px;height:50px;border-radius:50%;">';
                                    html += '    <span style="position:relative;top:5px;font-size:12px;">';
                                    if ($.trim(data[i].user_wechat_open_id) != '') {
                                        html += '    <a class="user-name" href="<?php echo url('user/index'); ?>&egon=0&openid=' + data[i].user_wechat_open_id + '&page=1" target="_blank">' + data[i].user_nick_name + '</a>';
                                    } else {
                                        html += '    <a class="user-name" href="<?php echo url('user/theoretic'); ?>&hazy_name=' + $.trim(data[i].user_nick_name) + '&page=1" target="_blank">' + data[i].user_nick_name + '</a>';
                                    }
                                    html += '</span>';
                                    html += '</td>';
                                    html += '<td class="am-table-centered am-text-middle">';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">' + '购买价格</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">' + data[i].buy_price + '<?php switch($list['buy_price_type']): case "0": ?> ( <?php echo $defaultNavigate['currency']; ?> )<?php break; case "1": ?> ( <?php echo $defaultNavigate['confer']; ?> )<?php break; endswitch; ?></div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">平台税率</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">'+ Math.round(data[i].buy_taxing * 100) + '%</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">结算金额</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">';
                                    html += '    ' + (data[i].buy_price * 1 - data[i].buy_taxing).toFixed(2) + '<?php switch($list['buy_price_type']): case "0": ?> ( <?php echo $defaultNavigate['currency']; ?> )<?php break; case "1": ?> ( <?php echo $defaultNavigate['confer']; ?> )<?php break; endswitch; ?>';
                                    html += '    </div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-right:#dddddd solid 1px;">购买时间</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;">' + data[i].buy_time + '</div>';
                                    html += '</td>';
                                    html += '</tr>';
                                    $($("#erpUp iframe").get(0).contentWindow.document).find('tbody').append(html);
                                }
                            } else {
                                --page;
                            }
                        }, 'json');
                        $.ajaxSettings.async = true;
                    }
                });
            }
        });
    }

    var viewFiles = function () {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['650px', '640px'],
            scrollbar: false,
            closeBtn: true,
            shadeClose: true,
            content: ["<?php echo url('cloud/steal'); ?>&uid=<?php echo $list['buyFilesInfo']['user_id']; ?>&pid=<?php echo $list['buyFilesInfo']['id']; ?>&type=2", 'no'],
        });
    }

    var restoreper = function (prid) {
        layer.confirm("您确定要恢复此帖吗？", {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("<?php echo url('essay/restper'); ?>", {'prid': prid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.href = data.url;
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    var satypical = function (pical) {
        var uata = {};
        uata['uplid'] = '<?php echo $list['id']; ?>';
        uata['pical'] = pical;
        if (pical == 1) {
            layer.confirm("您确定同意通过审核吗？", {
                btn: ['确定', '取消'], title: '提示'
            }, function () {
                equitable(uata);
            }, function (index) {
                layer.close(index);
            });
        } else {
            if (pical == 2) {
                var peason = "请您输入打回原因：";
            } else if (pical == 3) {
                var peason = "请您输入删帖原因：";
            }
            layer.prompt({'title': peason}, function (rea_value, index) {
                if (rea_value == '' || rea_value == 'undefined' || rea_value == null) {
                    return false;
                }
                uata['reason'] = rea_value;
                equitable(uata);
                layer.close(index);
            });
        }
    }

    var sendScamperFrequencySpecifyDefaultValue = function (type) {
        if (type == 0) {
            $('#newStudyHeat').val('<?php echo $list['study_heat']; ?>');
        } else if (type == 1) {
            $('#newStudyLaud').val('<?php echo $list['study_laud']; ?>');
        }
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var sendScamperFrequency = function (type) {
        var sendData = {};
        sendData['paid'] = '<?php echo $list['id']; ?>';
        sendData['oUnit'] = 0;
        if (type == 0) {
            sendData['type'] = '0';
            sendData['newStudyHeat'] = $('#newStudyHeat').val();
        } else if (type == 1) {
            sendData['type'] = '1';
            sendData['newStudyLaud'] = $('#newStudyLaud').val();
        }
        $.post("<?php echo url('essay/updateScamperFrequency'); ?>", sendData, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                    location.reload();
                });
            }
        }, 'json');
    }

    var equitable = function (uata) {
        $.ajax({
            type: "post",
            url: "<?php echo url('essay/setails'); ?>",
            data: uata,
            dataType: 'json',
            traditional: true,
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }
        });
    }
    
    var understand = function (prid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "<?php echo url('essay/enrollment'); ?>&prid=" + prid);
        dynamicUrl.attr('target', '_blank');
        dynamicUrl.get(0).click();
    }

    var loadThirdPartyVideo = function () {
        var videoType = parseInt('<?php echo $list['video_type']; ?>');
        var tencentVideoVid = $.trim('<?php echo $list['third_part_vid']; ?>');
        if (tencentVideoVid === '') {
            return;
        }
        switch (videoType) {
            case 1:
                var urlString = 'https://vv.video.qq.com/getinfo?platform=101001&charge=0&otype=json&defn=shd&vids=' + tencentVideoVid;
                $.ajax({
                    url: urlString,
                    dataType: "jsonp",
                    jsonp: "callback",
                    success: function (data) {
                        try {
                            var fileName = data['vl']['vi'][0]['fn'];
                            var fvkey = data['vl']['vi'][0]['fvkey'];
                            var host = data['vl']['vi'][0]['ul']['ui'][0]['url'];
                            $('#videoLoad').attr({'src': host + fileName + '?vkey=' + fvkey});
                        } catch (e) {
                        }
                    }
                });
                break;
            case 2:

                //<?php if($videoParsePluginKey): ?>

                $.post("<?php echo url('resolve/parse_url'); ?>", {'url': tencentVideoVid, 'isCover': 0}, function (data) {
                    $('#videoLoad').attr({'src': data.video});
                });

                //<?php endif; ?>

                break;
        }
    }

    var votingDetails = function (pvid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "<?php echo url('leading/votingPlenary'); ?>&pvid=" + pvid);
        dynamicUrl.attr('target', '_blank');
        dynamicUrl.get(0).click();
    }


    $(function () {
        loadThirdPartyVideo();
    });
</script>
<?php if($correctPluginKey): ?>
<script>

    var newCorrect = function () {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['600px', '550px'],
            scrollbar: false,
            closeBtn: true,
            shadeClose: true,
            content: ["<?php echo url('people/newCorrect'); ?>&fid=<?php echo $list['id']; ?>", 'no'],
        });
    }

    var delCorrect = function (fid) {
        layer.confirm('您确定要删除这条数据吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("<?php echo url('people/delCorrect'); ?>", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }
</script>
<?php endif; ?>

</body>
</html>