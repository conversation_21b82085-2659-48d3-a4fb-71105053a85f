<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:86:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/systems/annex.html";i:1715784598;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755608968;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-spinner"></span> 远程附件
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group am-u-sm-offset-3" style="margin-bottom:30px;">
                        <label class="am-radio-inline">
                            <input type="radio" value="0" name="quickType" <?php if($list['quicken_type']==0): ?>checked<?php endif; ?>>
                            本地存储
                        </label>
                        <label class="am-radio-inline">
                            <input type="radio" value="1" name="quickType" <?php if($list['quicken_type']==1): ?>checked<?php endif; ?>>
                            阿里云OSS
                        </label>
                        <label class="am-radio-inline">
                            <input type="radio" value="2" name="quickType" <?php if($list['quicken_type']==2): ?>checked<?php endif; ?>>
                            七牛云存储
                        </label>
                        <label class="am-radio-inline">
                            <input type="radio" value="3" name="quickType" <?php if($list['quicken_type']==3): ?>checked<?php endif; ?>>
                            腾讯云COS
                        </label>
                        <label class="am-radio-inline">
                            <input type="radio" value="4" name="quickType" <?php if($list['quicken_type']==4): ?>checked<?php endif; ?>>
                            又拍云存储
                        </label>
                        <label class="am-radio-inline">
                            <input type="radio" value="5" name="quickType" <?php if($list['quicken_type']==5): ?>checked<?php endif; ?>>
                            FTP存储
                        </label>
                    </div>
                    <div id="pattern-1" class="pdraft" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">AccessKey ID</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="oss_access_key_id" placeholder="<?php if($list['oss_follow']['oss_access_key_id']): ?><?php echo ciphertext($list['oss_follow']['oss_access_key_id']); else: ?>请输入阿里云AccessKey ID<?php endif; ?>">
                                <small>Access Key ID是您访问阿里云API的密钥，具有该账户完全的权限，请您妥善保管。</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Access Key Secret</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="oss_access_key_secret" placeholder="<?php if($list['oss_follow']['oss_access_key_secret']): ?><?php echo ciphertext($list['oss_follow']['oss_access_key_secret']); else: ?>请输入阿里云Access Key Secret<?php endif; ?>">
                                <small>Access Key Secret是您访问阿里云API的密钥，具有该账户完全的权限，请您妥善保管。</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="oss_bucket" value="<?php echo $list['oss_follow']['oss_bucket']; ?>" placeholder="请输入阿里云存储空间的名字">
                                <small>请保证Bucket为可公共读取的</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket所在区域</label>
                            <div class="am-u-sm-9">
                                <select id="oss_endpoint">
                                    <option value="0">请选择区域</option>
                                    <?php if(is_array($ossEndpoint) || $ossEndpoint instanceof \think\Collection || $ossEndpoint instanceof \think\Paginator): $i = 0; $__LIST__ = $ossEndpoint;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <option value="<?php echo $vo['value']; ?>" <?php if($list['oss_follow']['oss_endpoint']==$vo['value']): ?>selected<?php endif; ?>><?php echo $vo['key']; ?>
                                    </option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                                <small>请选择Bucket对应的地域节点</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="oss_url" value="<?php echo $list['oss_follow']['oss_url']; ?>" placeholder="请输入阿里云绑定的外链域名或自定义外链域名">
                                <small>阿里云支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                    </div>
                    <div id="pattern-2" class="pdraft" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">AccessKey</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="qiniu_access_key" placeholder="<?php if($list['qiniu_follow']['qiniu_access_key']): ?><?php echo ciphertext($list['qiniu_follow']['qiniu_access_key']); else: ?>请输入七牛云用于签名的公钥<?php endif; ?>">
                                <small>用于签名的公钥</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">SecretKey</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="qiniu_secret_key" placeholder="<?php if($list['qiniu_follow']['qiniu_secret_key']): ?><?php echo ciphertext($list['qiniu_follow']['qiniu_secret_key']); else: ?>请输入七牛云用于签名的私钥<?php endif; ?>">
                                <small>用于签名的私钥</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="qiniu_bucket" value="<?php echo $list['qiniu_follow']['qiniu_bucket']; ?>" placeholder="请输入七牛云存储空间的名字">
                                <small>请保证Bucket为可公共读取的</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="qiniu_url" value="<?php echo $list['qiniu_follow']['qiniu_url']; ?>" placeholder="请输入七牛云绑定的外链域名或自定义外链域名">
                                <small>七牛云支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">水印代码</label>
                            <div class="am-u-sm-9">
                                <textarea id="qiniu_watermark" style="height:150px;resize:none;"><?php echo $list['qiniu_follow']['qiniu_watermark']; ?></textarea>
                            </div>
                        </div>
                    </div>
                    <div id="pattern-3" class="pdraft" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">APPID</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="cos_app_id" value="<?php echo $list['cos_follow']['cos_app_id']; ?>" placeholder="请输入腾讯云APPID">
                                <small>APPID 是您项目的唯一标识号</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">SecretId</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="cos_secret_id" placeholder="<?php if($list['cos_follow']['cos_secret_id']): ?><?php echo ciphertext($list['cos_follow']['cos_secret_id']); else: ?>请输入腾讯云SecretId<?php endif; ?>">
                                <small>SecretID 是您项目的安全秘钥，具有该账户完全的权限，请妥善保管</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">SecretKey</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="cos_secret_key" placeholder="<?php if($list['cos_follow']['cos_secret_key']): ?><?php echo ciphertext($list['cos_follow']['cos_secret_key']); else: ?>请输入腾讯云SecretKey<?php endif; ?>">
                                <small>SecretKey 是您项目的安全秘钥，具有该账户完全的权限，请妥善保管</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="cos_bucket" value="<?php echo $list['cos_follow']['cos_bucket']; ?>" placeholder="请输入腾讯云存储空间的名字">
                                <small>请保证Bucket为可公共读取的</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket所在区域</label>
                            <div class="am-u-sm-9">
                                <select id="cos_region">
                                    <option value="0">请选择区域</option>
                                    <?php if(is_array($ossRegion) || $ossRegion instanceof \think\Collection || $ossRegion instanceof \think\Paginator): $i = 0; $__LIST__ = $ossRegion;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <option value="<?php echo $vo['value']; ?>" <?php if($list['cos_follow']['cos_region']==$vo['value']): ?>selected<?php endif; ?>><?php echo $vo['key']; ?>
                                    </option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                                <small>请选择Bucket对应的区域</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="cos_url" value="<?php echo $list['cos_follow']['cos_url']; ?>" placeholder="请输入腾讯云绑定的外链域名或自定义外链域名">
                                <small>腾讯云支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                    </div>
                    <div id="pattern-4" class="pdraft" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">服务名</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="upyun_service_name" value="<?php echo $list['upyun_follow']['upyun_service_name']; ?>" placeholder="请输入又拍云存储的服务名称">
                                <small>云存储服务名称</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">操作员名</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="upyun_operator_name" placeholder="<?php if($list['upyun_follow']['upyun_operator_name']): ?><?php echo ciphertext($list['upyun_follow']['upyun_operator_name']); else: ?>请输入又拍云的操作员名<?php endif; ?>">
                                <small>账户管理 -> 操作员 [不是登录用户名]</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">操作员密码</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="upyun_operator_password" placeholder="<?php if($list['upyun_follow']['upyun_operator_password']): ?><?php echo ciphertext($list['upyun_follow']['upyun_operator_password']); else: ?>请输入又拍云的操作员密码<?php endif; ?>">
                                <small>账户管理 -> 操作员密码 [不是登录密码]</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="upyun_url" value="<?php echo $list['upyun_follow']['upyun_url']; ?>" placeholder="请输入又拍云绑定的外链域名或自定义外链域名">
                                <small>又拍云支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                    </div>
                    <div id="pattern-5" class="pdraft" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP服务器地址</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="ftp_host" value="<?php echo $list['ftp_follow']['ftp_host']; ?>" placeholder="请输入FTP服务器地址">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP用户名</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="ftp_username" placeholder="<?php if($list['ftp_follow']['ftp_username']): ?><?php echo ciphertext($list['ftp_follow']['ftp_username']); else: ?>请输入FTP用户名<?php endif; ?>">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP密码</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="ftp_password" placeholder="<?php if($list['ftp_follow']['ftp_password']): ?><?php echo ciphertext($list['ftp_follow']['ftp_password']); else: ?>请输入FTP密码<?php endif; ?>">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP被动模式</label>
                            <div class="am-u-sm-9">
                                <select id="ftp_pasv">
                                    <option value="1" <?php if($list['ftp_follow']['ftp_pasv']==1): ?>selected<?php endif; ?>>开启</option>
                                    <option value="0" <?php if($list['ftp_follow']['ftp_pasv']==0): ?>selected<?php endif; ?>>关闭</option>
                                </select>
                                <small>服务器安全组需开启 TCP 39000-40000 FTP被动模端口范围，关闭FTP被动模式可能会造成上传页面卡死或上传失败等情况。</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP端口号</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="ftp_port" value="<?php echo $list['ftp_follow']['ftp_port']; ?>" placeholder="FTP端口号">
                                <small>FTP端口号，默认21</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="ftp_url" value="<?php echo $list['ftp_follow']['ftp_url']; ?>" placeholder="请输入FTP自定义访问域名">
                                <small>FTP自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group am-u-sm-offset-3" style="font-size: 14px;">
                        注意事项：<span style="color:  red;">保存之前请检查您的配置是否正确，保存之后所更改的内容将会立即生效！</span>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-push-6">
                            <button type="button" class="am-btn am-btn-primary" onclick="setLoad();">
                                保存配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<script src="./assets/js/base-enhanced.js"></script>
<script>
var baseConfig = {
    urls: {
        proxyBase: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        proxyResource: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        repairMissing: "<?php echo url('index/repairMissing'); ?>",
        purgeCache: "<?php echo url('index/purgeCache'); ?>",
        ordinary: "<?php echo url('ordinary'); ?>",
        receipt: "<?php echo url('receipt'); ?>"
    },
    menuActive: '<?php echo $menuActive; ?>',
    menuList: '<?php echo $menuList; ?>',
    much_role: '<?php echo $much_role; ?>',
    notice: <?php echo $notice; ?>,
    vacant: <?php echo $vacant; ?>
};
BaseEnhanced.init(baseConfig);
window.retakeCache = BaseEnhanced.retakeCache;
window.getOriginalSrc = BaseEnhanced.getOriginalSrc;
</script>
<?php if($much_role=='founder'): ?>
<script>
window.updateFix = function() {
    layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
        btn: ['确定', '取消'],
        title: '提示'
    }, function() {
        // 执行修复操作
        performDatabaseRepair();
    }, function(index) {
        // 取消操作
        layer.close(index);
    });
};

function performDatabaseRepair() {
    var loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });
    $.post("<?php echo url('index/repairMissing'); ?>", function(data) {
        layer.close(loadingIndex);
        if (data !== false) {
            layer.msg('数据库缺失字段修复完成', {
                icon: 1,
                time: 1500
            }, function() {
                location.reload();
            });
        } else {
            layer.msg('修复失败，请稍后重试', {
                icon: 2,
                time: 2000
            }, function() {
                location.reload();
            });
        }
    }).error(function() {
        layer.close(loadingIndex);
        layer.msg('网络错误，修复失败', {
            icon: 2,
            time: 2000
        });
    });
}
</script>
<?php endif; ?>

<script>
    $(function () {
        var quickType = $("[name='quickType']:checked").val();
        $('#pattern-' + quickType).show();

        $("[name='quickType']").change(function () {
            var qucke = $("[name='quickType']:checked").val();
            if (qucke == 0) {
                $('.pdraft').hide();
            } else {
                $('.pdraft').hide();
                $('#pattern-' + qucke).show();
            }
        });
    });


    var tokep = false;
    function setLoad() {
        if (!tokep) {
            tokep = true;
            var sngeing = $("[name='quickType']:checked").val();
            var fabric_data = {'quicken_type': 0};
            if (sngeing == 1) {
                var accesskeyId = $.trim($("#oss_access_key_id").val());
                var accessKeySecret = $.trim($("#oss_access_key_secret").val());
                var endpoint = $.trim($("#oss_endpoint").val());
                var bucket = $.trim($("#oss_bucket").val());
                var url = $.trim($("#oss_url").val());
                var salting = $.trim('<?php if($list['oss_follow']['oss_access_key_id']): ?>1<?php endif; ?>');
                if (accesskeyId == '' && salting != '1') {
                    layer.msg('请输入阿里云AccessKey ID');
                    tokep = false;
                    return;
                }
                if (accessKeySecret == '' && salting != '1') {
                    layer.msg('请输入阿里云Access Key Secret');
                    tokep = false;
                    return;
                }
                if (bucket == '') {
                    layer.msg('请输入阿里云存储空间的名字');
                    tokep = false;
                    return;
                }
                if (endpoint == '0') {
                    layer.msg('请选择Bucket对应的地域节点');
                    tokep = false;
                    return;
                }
                if (url == '') {
                    layer.msg('请输入阿里云绑定的外链域名或自定义外链域名');
                    tokep = false;
                    return;
                }
                fabric_data = {
                    'quicken_type': 1,
                    'oss_access_key_id': accesskeyId,
                    'oss_access_key_secret': accessKeySecret,
                    'oss_endpoint': endpoint,
                    'oss_bucket': bucket,
                    'oss_url': url
                };
            } else if (sngeing == 2) {
                var accessKey = $.trim($("#qiniu_access_key").val());
                var secretKey = $.trim($("#qiniu_secret_key").val());
                var bucket = $.trim($("#qiniu_bucket").val());
                var url = $.trim($("#qiniu_url").val());
                var watermark = $.trim($("#qiniu_watermark").val());
                var salting = $.trim('<?php if($list['qiniu_follow']['qiniu_access_key']): ?>1<?php endif; ?>');
                if (accessKey == '' && salting != '1') {
                    layer.msg('请输入七牛云用于签名的公钥');
                    tokep = false;
                    return;
                }
                if (secretKey == '' && salting != '1') {
                    layer.msg('请输入七牛云用于签名的私钥');
                    tokep = false;
                    return;
                }
                if (bucket == '') {
                    layer.msg('请输入七牛云存储空间的名字');
                    tokep = false;
                    return;
                }
                if (url == '') {
                    layer.msg('请输入七牛云绑定的外链域名或自定义外链域名');
                    tokep = false;
                    return;
                }
                fabric_data = {
                    'quicken_type': 2,
                    'qiniu_access_key': accessKey,
                    'qiniu_secret_key': secretKey,
                    'qiniu_bucket': bucket,
                    'qiniu_url': url,
                    'qiniu_watermark': watermark,
                };
            } else if (sngeing == 3) {
                var appId = $.trim($("#cos_app_id").val());
                var secretId = $.trim($("#cos_secret_id").val());
                var secretKey = $.trim($("#cos_secret_key").val());
                var bucket = $.trim($("#cos_bucket").val());
                var region = $.trim($("#cos_region").val());
                var url = $.trim($("#cos_url").val());
                var salting = $.trim('<?php if($list['cos_follow']['cos_secret_id']): ?>1<?php endif; ?>');
                if (appId == '') {
                    layer.msg('请输入腾讯云APPID');
                    tokep = false;
                    return;
                }
                if (secretId == '' && salting != '1') {
                    layer.msg('请输入腾讯云SecretId');
                    tokep = false;
                    return;
                }
                if (secretKey == '' && salting != '1') {
                    layer.msg('请输入腾讯云SecretKey');
                    tokep = false;
                    return;
                }
                if (bucket == '') {
                    layer.msg('请输入腾讯云存储空间的名字');
                    tokep = false;
                    return;
                }
                if (region == '0') {
                    layer.msg('请选择Bucket对应的区域');
                    tokep = false;
                    return;
                }
                if (url == '') {
                    layer.msg('请输入腾讯云绑定的外链域名或自定义外链域名');
                    tokep = false;
                    return;
                }
                fabric_data = {
                    'quicken_type': 3,
                    'cos_app_id': appId,
                    'cos_secret_id': secretId,
                    'cos_secret_key': secretKey,
                    'cos_bucket': bucket,
                    'cos_region': region,
                    'cos_url': url
                };
            } else if (sngeing == 4) {
                var serviceName = $.trim($("#upyun_service_name").val());
                var operatorName = $.trim($("#upyun_operator_name").val());
                var operatorPassword = $.trim($("#upyun_operator_password").val());
                var url = $.trim($("#upyun_url").val());
                fabric_data = {
                    'quicken_type': 4,
                    'upyun_service_name': serviceName,
                    'upyun_operator_name': operatorName,
                    'upyun_operator_password': operatorPassword,
                    'upyun_url': url,
                };
            } else if (sngeing == 5) {
                var ftpHost = $.trim($("#ftp_host").val());
                var ftpUsername = $.trim($("#ftp_username").val());
                var ftpPassword = $.trim($("#ftp_password").val());
                var ftpPort = $.trim($("#ftp_port").val());
                var ftpPasv = $.trim($("#ftp_pasv").val());
                var url = $.trim($("#ftp_url").val());
                fabric_data = {
                    'quicken_type': 5,
                    'ftp_host': ftpHost,
                    'ftp_username': ftpUsername,
                    'ftp_password': ftpPassword,
                    'ftp_port': ftpPort,
                    'ftp_pasv': ftpPasv,
                    'ftp_url': url,
                };
            }
            $.post("<?php echo url('systems/annex'); ?>", fabric_data, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        tokep = false;
                    });
                }
            }, 'json');
        }
    }
</script>

</body>
</html>