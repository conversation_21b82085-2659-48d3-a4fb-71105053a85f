<?php return array(
    'root' => array(
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => NULL,
        'name' => 'topthink/think',
        'dev' => true,
    ),
    'versions' => array(
        'aliyuncs/oss-sdk-php' => array(
            'pretty_version' => 'v2.6.0',
            'version' => '2.6.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aliyuncs/oss-sdk-php',
            'aliases' => array(),
            'reference' => '572d0f8e099e8630ae7139ed3fdedb926c7a760f',
            'dev_requirement' => false,
        ),
        'guzzlehttp/command' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/command',
            'aliases' => array(),
            'reference' => '2aaa2521a8f8269d6f5dfc13fe2af12c76921034',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '6.5.8',
            'version' => '6.5.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'reference' => 'a52f0440530b54fa079ce76e8c5d196a42cad981',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle-services' => array(
            'pretty_version' => '1.1.3',
            'version' => '1.1.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle-services',
            'aliases' => array(),
            'reference' => '9e3abf20161cbf662d616cbb995f2811771759f7',
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '1.5.2',
            'version' => '1.5.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'reference' => 'b94b2807d85443f9719887892882d0329d1e2598',
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '1.9.0',
            'version' => '1.9.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'reference' => 'e98e3e6d4f86621a9b75f623996e6bbdeb4b9318',
            'dev_requirement' => false,
        ),
        'myclabs/php-enum' => array(
            'pretty_version' => '1.6.6',
            'version' => '1.6.6.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/php-enum',
            'aliases' => array(),
            'reference' => '32c4202886c51fbe5cc3a7c34ec5c9a4a790345e',
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => 'f6561bf28d520154e4b0ec72be95418abe6d9363',
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'qcloud/cos-sdk-v5' => array(
            'pretty_version' => 'v2.6.1',
            'version' => '2.6.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../qcloud/cos-sdk-v5',
            'aliases' => array(),
            'reference' => 'd367ba8d0305b83364b64055594a0ac22b1cefd8',
            'dev_requirement' => false,
        ),
        'qiniu/php-sdk' => array(
            'pretty_version' => 'v7.7.0',
            'version' => '7.7.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../qiniu/php-sdk',
            'aliases' => array(),
            'reference' => 'dde03fc55de64815412f8ccfe24e1bd21564a6f1',
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.27.0',
            'version' => '1.27.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'reference' => '639084e360537a19f9ee352433b84ce831f3d2da',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.27.0',
            'version' => '1.27.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'reference' => '19bd1e4fcd5b91116f14d8533c57831ed00571b6',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.27.0',
            'version' => '1.27.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php72',
            'aliases' => array(),
            'reference' => '869329b1e9894268a8a61dabb69153029b7a8c97',
            'dev_requirement' => false,
        ),
        'tencentcloud/common' => array(
            'pretty_version' => '3.0.828',
            'version' => '3.0.828.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tencentcloud/common',
            'aliases' => array(),
            'reference' => '161ae1ec90f6e1b086940a75177162a80ce75fc8',
            'dev_requirement' => false,
        ),
        'tencentcloud/ims' => array(
            'pretty_version' => '3.0.828',
            'version' => '3.0.828.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tencentcloud/ims',
            'aliases' => array(),
            'reference' => '84a1075d14ff1e423511f5a2b387dde480f62579',
            'dev_requirement' => false,
        ),
        'tencentcloud/tms' => array(
            'pretty_version' => '3.0.828',
            'version' => '3.0.828.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tencentcloud/tms',
            'aliases' => array(),
            'reference' => '0f2aebb931aa7a40f585156efa408e967c45f2a7',
            'dev_requirement' => false,
        ),
        'topthink/framework' => array(
            'pretty_version' => 'v5.0.25',
            'version' => '5.0.25.0',
            'type' => 'think-framework',
            'install_path' => __DIR__ . '/../../thinkphp',
            'aliases' => array(),
            'reference' => '643c58ed1bd22a2823ce5e95b3b68a5075f9087c',
            'dev_requirement' => false,
        ),
        'topthink/think' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => NULL,
            'dev_requirement' => false,
        ),
        'topthink/think-installer' => array(
            'pretty_version' => 'v1.0.14',
            'version' => '1.0.14.0',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../topthink/think-installer',
            'aliases' => array(),
            'reference' => 'eae1740ac264a55c06134b6685dfb9f837d004d1',
            'dev_requirement' => false,
        ),
        'upyun/sdk' => array(
            'pretty_version' => '3.5.0',
            'version' => '3.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../upyun/sdk',
            'aliases' => array(),
            'reference' => 'c9f824626552c32b987de4ac7f136e0e21cca962',
            'dev_requirement' => false,
        ),
        'wechatpay/wechatpay-guzzle-middleware' => array(
            'pretty_version' => '0.2.2',
            'version' => '0.2.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wechatpay/wechatpay-guzzle-middleware',
            'aliases' => array(),
            'reference' => '6782ac33ed8cf97628609a71cdc5e84a6a40677a',
            'dev_requirement' => false,
        ),
    ),
);
