<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:85:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/essay/ritual.html";i:1756113407;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755608968;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>.am-modal-hd .am-close{top:-5px;right:-3px;}.w-e-menu{font-size:12px;}.w-e-text,.w-e-text-container{height:300px !important;}.tpl-portlet-components{background:#fff;padding:25px;border-radius:8px;box-shadow:0 2px 12px rgba(0,0,0,0.05);}.portlet-title{margin-bottom:20px;border-bottom:1px solid #eee;padding-bottom:12px;}.portlet-title .caption{font-size:18px;color:#333;}.portlet-title .am-icon-cog{color:#3bb4f2;}.am-form-group{position:relative;margin-bottom:20px;padding-bottom:20px;border-bottom:1px solid #f0f0f0;}.am-form-group:last-child{border-bottom:none;margin-bottom:0;padding-bottom:0;}.am-form-label{font-weight:500;color:#333;padding-top:6px;font-size:13px;}.am-form input[type="radio"]{margin-right:5px;}.am-form input[type="number"]{height:36px;border-radius:4px;border:1px solid #ddd;padding:0 10px;width:100%;}.am-form small{margin-top:4px;font-size:12px;color:#666;}.am-form small[style*="color:red"]{color:#ff5252 !important;}.am-form textarea{border:1px solid #ddd;border-radius:4px;padding:10px;width:100%;}.am-btn-primary{background:#3bb4f2;border-color:#3bb4f2;padding:8px 24px;font-size:14px;border-radius:4px;transition:all 0.3s;}.am-btn-primary:hover{background:#2798d8;border-color:#2798d8;}.radio-group{display:flex;gap:15px;margin-bottom:4px;height:36px;align-items:center;}.radio-group label{display:flex;align-items:center;cursor:pointer;font-weight:normal;color:#666;font-size:14px;line-height:36px;height:36px;}.radio-group input[type="radio"]{margin-top:0;margin-bottom:0;vertical-align:middle;margin-right:4px;}#detail{border:1px solid #ddd;border-radius:4px;margin-bottom:6px;}.number-input-group{position:relative;display:flex;align-items:center;margin-bottom:4px;}.number-input-group input{flex:1;}.number-input-group label{margin-left:10px;color:#999;font-weight:normal;font-size:16px;}.tax-description{margin-top:6px;padding:8px;background:#f8f8f8;border-radius:4px;line-height:1.6;}.tax-description span{display:block;margin-top:3px;}.w-e-toolbar{background:#fafafa !important;border-bottom:1px solid #eee !important;}.w-e-text-container{border-color:#eee !important;}@media screen and (max-width:768px){.content-wrapper{padding:10px;}.tpl-portlet-components{padding:15px;}.am-u-sm-3,.am-u-sm-7,.am-u-sm-9{width:100%;}}.form-textarea{height:400px !important;padding:15px;font-size:14px;line-height:1.6;border:1px solid #ddd;border-radius:4px;background:#fff;resize:none;transition:border-color 0.3s;margin-bottom:4px;}.form-textarea:focus{border-color:#3bb4f2;outline:none;box-shadow:0 0 0 2px rgba(59,180,242,0.1);}.am-form-group:has(.form-textarea){margin-bottom:30px;}.am-form-label{padding-top:8px;}.form-submit{display:flex;justify-content:center;padding:20px 0;}.form-submit .am-btn{min-width:120px;}.content-wrapper{width:100%;min-height:100vh;background-color:#f5f7fa;padding:20px;margin-bottom:20px;}.tpl-portlet-components{max-width:900px;width:100%;margin:0 auto;background:#fff;padding:25px;border-radius:8px;box-shadow:0 2px 12px rgba(0,0,0,0.05);}.am-form-horizontal{max-width:900px;margin:0 auto;}.am-u-sm-3{width:22%;}.am-u-sm-7{width:60%;}.am-u-sm-9{width:78%;}.number-input-group input{max-width:120px;}.editor-container,.form-textarea{background:#fafafa;border:1px solid #eee;}</style>
<div class="content-wrapper">
    <div class="tpl-portlet-components">
        <div class="portlet-title">
            <div class="caption font-green bold">
                <span class="am-icon-cog"></span> 帖子设置
            </div>
        </div>
        <div class="tpl-block">
            <div class="am-g tpl-amazeui-form">
                <div class="am-u-sm-12 am-u-md-12">
                    <div class="am-form am-form-horizontal">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">发帖自动审核</label>
                            <div class="am-u-sm-9">
                                <div class="radio-group">
                                    <label>
                                        <input type="radio" name="review" value="1" <?php if($list['auto_review']==1): ?>checked<?php endif; ?>> 开启
                                    </label>
                                    <label>
                                        <input type="radio" name="review" value="0" <?php if($list['auto_review']==0): ?>checked<?php endif; ?>> 关闭
                                    </label>
                                </div>
                                <small style="color:red;">开启后您无需再手动审核 系统将自动通过所有用户发布的帖子</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">发帖次数限制</label>
                            <div class="am-u-sm-9">
                                <input type="number" id="limit" value="<?php echo $list['number_limit']; ?>" oninput="grender(this);">
                                <small>每日发帖次数限制 0 为不限制</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">自动删除帖子</label>
                            <div class="am-u-sm-9">
                                <input type="number" id="paperAutoDeleteDays" value="<?php echo $list['paper_auto_delete_days']; ?>" oninput="grender(this);">
                                <small>自动删除 指定天数 之前发布的帖子 0 为不自动删除</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">回帖自动审核</label>
                            <div class="am-u-sm-9">
                                <div class="radio-group">
                                    <label>
                                        <input type="radio" name="replyReview" value="1" <?php if($list['reply_auto_review']==1): ?>checked<?php endif; ?>> 开启
                                    </label>
                                    <label>
                                        <input type="radio" name="replyReview" value="0" <?php if($list['reply_auto_review']==0): ?>checked<?php endif; ?>> 关闭
                                    </label>
                                </div>
                                <small style="color:red;">开启后您无需再手动审核 系统将自动通过所有用户回复的帖子</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">回帖次数限制</label>
                            <div class="am-u-sm-9">
                                <input type="number" id="replyLimit" value="<?php echo $list['reply_number_limit']; ?>" oninput="grender(this);">
                                <small>每日单个帖子用户最多回复次数 0 为不限制</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">评论自动审核</label>
                            <div class="am-u-sm-9">
                                <div class="radio-group">
                                    <label>
                                        <input type="radio" name="discussReview" value="1" <?php if($list['discuss_auto_review']==1): ?>checked<?php endif; ?>> 开启
                                    </label>
                                    <label>
                                        <input type="radio" name="discussReview" value="0" <?php if($list['discuss_auto_review']==0): ?>checked<?php endif; ?>> 关闭
                                    </label>
                                </div>
                                <small style="color:red;">开启后您无需再手动审核 系统将自动通过所有用户评论的回复</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">评论次数限制</label>
                            <div class="am-u-sm-9">
                                <input type="number" id="discussLimit" value="<?php echo $list['discuss_number_limit']; ?>" oninput="grender(this);">
                                <small>每日单个帖子用户最多评论次数 0 为不限制</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">付费帖子自动审核</label>
                            <div class="am-u-sm-9">
                                <div class="radio-group">
                                    <label>
                                        <input type="radio" name="buyPaperReview" value="1" <?php if($list['buy_paper_auto_review']==1): ?>checked<?php endif; ?>> 开启
                                    </label>
                                    <label>
                                        <input type="radio" name="buyPaperReview" value="0" <?php if($list['buy_paper_auto_review']==0): ?>checked<?php endif; ?>> 关闭
                                    </label>
                                </div>
                                <small style="color:red;">开启后您无需再手动审核 系统将自动通过所有用户发布的付费帖子</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">发布付费帖次数限制</label>
                            <div class="am-u-sm-9">
                                <input type="number" id="buyPaperLimit" value="<?php echo $list['buy_paper_number_limit']; ?>" oninput="grender(this);">
                                <small>发布付费帖次数限制 0 为不限制</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">平台扣除付费帖子税率</label>
                            <div class="am-u-sm-9">
                                <div class="number-input-group">
                                    <input type="number" id="buyPaperTaxing" value="<?php echo $list['buy_paper_taxing']*100; ?>" oninput="grender(this);">
                                    <label>%</label>
                                </div>
                                <div class="tax-description">
                                    <small>
                                        用户获得付费帖子收益计算方式（ 例如当前税率为 50% ）
                                        <!--
                                        获得<?php echo $defaultNavigate['confer']; ?> ( 税前 ) = 礼物价格 ( <?php echo $defaultNavigate['currency']; ?> ) * 10
                                        -->
                                        <span>收益<?php echo $defaultNavigate['confer']; ?> ( 税后 ) = [ 收益<?php echo $defaultNavigate['confer']; ?> - 收益<?php echo $defaultNavigate['confer']; ?> * 50% ( 税率 ) ]</span>
                                        <span style="color: red;">用户最终所获得的收益积分将保留小数点后两位，其余四舍五入</span>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">帖子内容字体大小</label>
                            <div class="am-u-sm-9">
                                <div class="number-input-group">
                                    <input type="number" id="tractateFontSize" value="<?php echo $list['tractate_font_size']; ?>" oninput="grender(this);">
                                    <label>px</label>
                                </div>
                                <small>帖子内容字体大小 默认大小14px</small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">论坛声明显示开关</label>
                            <div class="am-u-sm-9">
                                <div class="radio-group">
                                    <label>
                                        <input type="radio" name="isShowForumDeclaration" value="1" <?php if($list['is_show_forum_declaration']==1): ?>checked<?php endif; ?>> 开启
                                    </label>
                                    <label>
                                        <input type="radio" name="isShowForumDeclaration" value="0" <?php if($list['is_show_forum_declaration']==0): ?>checked<?php endif; ?>> 关闭
                                    </label>
                                </div>
                                <small style="color:red;">开启后将显示论坛声明</small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">论坛声明</label>
                            <div class="am-u-sm-9">
                                <div id="detail" class="editor-container"><?php echo $list['forum_declaration']; ?></div>
                                <small style="color: red;">请填写论坛声明 ( 帖子内展示 )</small>
                                <span id="customizeGallery" style="display:none;" onclick="cuonice();"></span>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="notice" class="am-u-sm-3 am-form-label">发帖须知</label>
                            <div class="am-u-sm-9">
                                <textarea id="notice" class="form-textarea"><?php echo $list['notice']; ?></textarea>
                                <small style="color: red;">请填写发帖须知 ( 用户发帖需要遵守的规则 )</small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <div class="form-submit">
                                <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<script src="./assets/js/base-enhanced.js"></script>
<script>
var baseConfig = {
    urls: {
        proxyBase: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        proxyResource: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        repairMissing: "<?php echo url('index/repairMissing'); ?>",
        purgeCache: "<?php echo url('index/purgeCache'); ?>",
        ordinary: "<?php echo url('ordinary'); ?>",
        receipt: "<?php echo url('receipt'); ?>"
    },
    menuActive: '<?php echo $menuActive; ?>',
    menuList: '<?php echo $menuList; ?>',
    much_role: '<?php echo $much_role; ?>',
    notice: <?php echo $notice; ?>,
    vacant: <?php echo $vacant; ?>
};
BaseEnhanced.init(baseConfig);
window.retakeCache = BaseEnhanced.retakeCache;
window.getOriginalSrc = BaseEnhanced.getOriginalSrc;
</script>
<?php if($much_role=='founder'): ?>
<script>
window.updateFix = function() {
    layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
        btn: ['确定', '取消'],
        title: '提示'
    }, function() {
        // 执行修复操作
        performDatabaseRepair();
    }, function(index) {
        // 取消操作
        layer.close(index);
    });
};

function performDatabaseRepair() {
    var loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });
    $.post("<?php echo url('index/repairMissing'); ?>", function(data) {
        layer.close(loadingIndex);
        if (data !== false) {
            layer.msg('数据库缺失字段修复完成', {
                icon: 1,
                time: 1500
            }, function() {
                location.reload();
            });
        } else {
            layer.msg('修复失败，请稍后重试', {
                icon: 2,
                time: 2000
            }, function() {
                location.reload();
            });
        }
    }).error(function() {
        layer.close(loadingIndex);
        layer.msg('网络错误，修复失败', {
            icon: 2,
            time: 2000
        });
    });
}
</script>
<?php endif; ?>

<script type="text/javascript" src="static/wangEditor/wangEditor.min.js?time=<?php echo time(); ?>"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css?time=<?php echo time(); ?>">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js?time=<?php echo time(); ?>"></script>
<script>

    var E = window.wangEditor;
    var editor = new E('#detail');
    editor.customConfig.uploadImgServer = true;
    editor.create();
    E.fullscreen.init('#detail');

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["<?php echo url('images/dialogImages'); ?>&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
    }


    var grender = function (obj) {
        obj.value = Number(obj.value.match(/^\d+(?:\.\d{0,0})?/));
    }

    var excheck = function (notice,buyPaperTaxing) {
        if (notice === '' || notice === 'undefined' || notice == null) {
            layer.msg('发帖须知不能为空');
            return false;
        }
        if (buyPaperTaxing >= 100) {
            layer.msg('平台扣除付费帖子税率不能大于等于100%');
            return false;
        }
        return true;
    }

    var holdSave = function () {

        var setData = {};

        setData['uplid'] = '<?php echo $list['id']; ?>';

        setData['review'] = $.trim($("[name='review']:checked").val());
        setData['limit'] = $('#limit').val();
        setData['paperAutoDeleteDays'] = $('#paperAutoDeleteDays').val();

        setData['replyReview'] = $.trim($("[name='replyReview']:checked").val());
        setData['replyLimit'] = $('#replyLimit').val();

        setData['discussReview'] = $.trim($("[name='discussReview']:checked").val());
        setData['discussLimit'] = $('#discussLimit').val();

        setData['buyPaperReview'] = $.trim($("[name='buyPaperReview']:checked").val());
        setData['buyPaperLimit'] = $.trim($('#buyPaperLimit').val());

        setData['buyPaperTaxing'] = Number($.trim($('#buyPaperTaxing').val()));

        setData['tractateFontSize'] = Number($.trim($('#tractateFontSize').val()));

        setData['isShowForumDeclaration'] = $.trim($("[name='isShowForumDeclaration']:checked").val());

        setData['forumDeclaration'] = $.trim(this.editor.txt.html());
        setData['notice'] = $.trim($('#notice').val());

        if (excheck(setData['notice'], setData['buyPaperTaxing'])) {
            $.post("<?php echo url('essay/ritual'); ?>", setData, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        }
    }
</script>

</body>
</html>