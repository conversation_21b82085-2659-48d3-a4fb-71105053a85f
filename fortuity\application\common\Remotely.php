<?php

namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

use think\Cache;
use think\Db;

/**
 * 远程服务类
 * Class Remotely
 * @package app\common
 *
 * 负责与云端授权服务器通信，管理模板和插件的授权、分发、迁移和状态检查。
 * 遵循单一职责原则，将复杂的逻辑拆分为多个私有辅助方法。
 */
class Remotely extends Gyration
{
    /**
     * 模板加密密钥
     */
    const KEY_TEMPLATE_CODING = 'WXVMdW9UZW1wbGF0ZUNvZGluZw==';
    /**
     * 站点插件授权数据加密密钥 (用于 unNotchStream)
     */
    const KEY_SLOT_STREAM = 'WXVMdW9TbG90UGx1Z2lu';
    /**
     * 插件授权码加密密钥 (用于authcode)
     */
    const KEY_PLUGIN_AUTHCODE = 'WXVMdW9QbHVnaW4=';

    //================================================================
    // I. 核心业务入口 (Public API)
    //================================================================

    /**
     * [核心业务] 为插件列表数组添加解锁和开关状态
     * @param array $pluginList 插件列表 (引用传递)
     * @param int|string $muchId 多用户ID
     */
    public static function compareNotch(&$pluginList, $muchId)
    {
        // 获取当前环境已解锁的插件信息
        $readNotchResult = self::readNotch();
        // 提取当前授权数据流信息
        $unNotchStream = $readNotchResult['current_stream'];

        // 检查授权是否过期且 IP 是否与当前 IP 匹配
        if (is_array($unNotchStream) && isset($unNotchStream['exp_time'], $unNotchStream['client_ip']) && ($unNotchStream['exp_time'] > time()) && $unNotchStream['client_ip'] === self::getPositionByIp()) {
            // 1. 设置插件的解锁状态 (narrowUnLock)
            // 遍历传入的插件列表
            foreach ($pluginList['data'] as $i => &$item) {
                $item['narrowUnLock'] = in_array($item['narrowMark'], $readNotchResult['current_unlocked']) ? 1 : 0;
            }
            unset($item); // 解除最后一个元素的引用

            // 2. 获取、迁移并清理用户的个人开关设置
            // 根据多用户ID查询用户的个性化插件开关设置
            $tsvInfo = self::findOrCreateUserValve($muchId);
            // 判断是否为需要从旧逻辑迁移的记录
            self::migrateUserValveIfNeeded($tsvInfo, $readNotchResult);
            // 数据清理：移除用户启用列表中已不再被授权的插件
            $petalData = self::sanitizeUserPetalData($tsvInfo, $readNotchResult['current_unlocked']);

            // 3. 设置插件的个人开关状态 (selfSwitch) 和加密标记
            // 遍历插件列表，为每个插件设置最终的开关状态
            foreach ($pluginList['data'] as $key => &$item) {
                // 新逻辑: petal_data 存的是启用的插件, 在列表里则为开启
                $item['selfSwitch'] = in_array($item['narrowMark'], $petalData) ? 1 : 0;
                // 为每个插件生成一个加密标记，可能用于前端或其他验证
                $item['secretMark'] = authcode($item['narrowMark'], 'ENCODE', base64_decode(self::KEY_PLUGIN_AUTHCODE));
            }
            // 解除最后一个元素的引用
            unset($item);
        } else {
            // 授权无效或过期，将所有插件设置为锁定和关闭状态
            foreach ($pluginList['data'] as &$item) {
                // 设置插件为未解锁状态
                $item['narrowUnLock'] = 0;
                // 设置插件为关闭状态
                $item['selfSwitch'] = 0;
                // 生成插件的加密标记
                $item['secretMark'] = authcode($item['narrowMark'], 'ENCODE', base64_decode(self::KEY_PLUGIN_AUTHCODE));
            }
            // 解除最后一个元素的引用
            unset($item);
        }
    }

    /**
     * [核心业务] 判断某个插件对某个用户是否真正可用 (只读)
     * @param string $name 插件名称
     * @param int|string $muchId 多用户ID
     * @return bool 返回插件是否为开启状态
     */
    public static function isEnableProperty($name, $muchId)
    {
        // 如果插件名或用户ID为空或只包含空格
        if (trim($name) === '' || trim($muchId) === '') {
            return false;
        }

        // 获取所有付费功能的列表，用于名称到ID的转换
        $paidMenuList = self::commonList();
        if (!isset($paidMenuList[$name])) {
            return false;
        }
        // 根据插件名称获取对应的插件ID
        $pluginId = $paidMenuList[$name];

        // 获取当前站点的解锁信息
        $readNotchResult = self::readNotch();
        // 1. 检查插件是否在站点层面解锁
        if (!in_array($pluginId, $readNotchResult['current_unlocked'])) {
            return false;
        }

        // 2. 获取用户个人设置
        // 查询指定用户的插件开关设置
        $tsvInfo = Db::name('template_slot_valve')->where('much_id', $muchId)->find();
        // 如果没有用户设置信息，则插件一定没有启用
        if (!$tsvInfo) {
            return false;
        }

        // 3. 如有需要，执行一次性数据迁移
        // 判断是否为需要从旧逻辑迁移的记录
        self::migrateUserValveIfNeeded($tsvInfo, $readNotchResult);

        // 4. 根据用户的启用列表进行最终判断
        // 解密当前用户的插件设置
        $petalData = json_decode(authcode($tsvInfo['petal_data'], 'DECODE', base64_decode(self::KEY_PLUGIN_AUTHCODE)), true) ?: array();

        // 新逻辑: 必须同时满足"已解锁"和"已在启用列表"
        return in_array($pluginId, $petalData);
    }

    /**
     * [核心业务] 检查指定名称的功能是否已经购买并解锁 (站点级别)
     * @param string $name 插件名称
     * @return bool
     */
    public static function isUnLockProperty($name)
    {
        // 获取所有付费功能的列表
        $paidMenuList = self::commonList();
        // 在访问数组前，先检查键是否存在
        if (!isset($paidMenuList[$name])) {
            // 如果插件名称无效，直接返回 false
            return false;
        }
        // 获取当前环境下已解锁的功能信息
        $readNotch = self::readNotch();
        // 从解锁信息中提取出 ID 列表
        $unLockIds = $readNotch['current_unlocked'];
        // 判断指定功能的 ID 是否存在于已解锁的 ID 列表中
        return in_array($paidMenuList[$name], $unLockIds);
    }

    /**
     * [核心业务] 根据解锁状态动态筛选菜单列表
     * @param array $menuList 菜单列表 (引用传递)
     */
    public static function customMenu(&$menuList)
    {
        // 获取所有付费功能的列表
        $paidMenuList = self::commonList();
        // 获取当前环境下已解锁的功能信息
        $readNotch = self::readNotch();
        // 从解锁信息中提取出 ID 列表
        $unLockIds = $readNotch['current_unlocked'];
        // 初始化一个用于存放未解锁功能名称的数组
        $notUnlockedList = array();
        // 遍历所有付费功能
        foreach ($paidMenuList as $key => $item) {
            // 如果当前功能的 ID 不在已解锁列表中
            if (!in_array($item, $unLockIds)) {
                // 将该功能的名称添加到未解锁列表
                $notUnlockedList[] = $key;
            }
        }

        // 计算传入的菜单列表的总数
        $menuListCount = count($menuList);
        // 计算未解锁列表的总数
        $notUnlockedListCount = count($notUnlockedList);
        // 外层循环遍历传入的菜单
        for ($i = 0; $i < $menuListCount; $i++) {
            // 内层循环遍历未解锁的功能名称
            for ($j = 0; $j < $notUnlockedListCount; $j++) {
                // 如果菜单项存在且名称与未解锁项匹配
                if (isset($menuList[$i]) && $menuList[$i]['mot_name'] === $notUnlockedList[$j]) {
                    // 从菜单列表中移除该项
                    unset($menuList[$i]);
                    // 跳出内层循环，继续处理下一个传入的菜单项
                    break;
                }
            }
        }
        // 重新索引数组，避免因 unset 导致的数组索引不连续
        // PHP 8.4兼容性修复：确保 $menuList 是数组
        $menuList = is_array($menuList) ? (is_array($menuList) ? array_values($menuList) : array()) : array();
    }

    /**
     * [核心业务] 检查启用某个插件时是否存在冲突
     * @param string $narrowMark 要启用的插件的标志
     * @param int|string $muchId 多用户ID
     * @return array 返回冲突结果 ['conflict' => boolean, 'message' => string]
     */
    public static function checkConflicts($narrowMark, $muchId)
    {
        // 根据插件标识获取要启用的插件的名称
        $pluginNameToEnable = self::getPluginNameByMark($narrowMark);
        // 如果找不到对应的插件名称
        if (!$pluginNameToEnable) {
            // 返回无冲突，因为它不是一个已知的、有冲突规则的插件
            return ['conflict' => false, 'message' => ''];
        }

        // 获取所有定义好的冲突插件组
        $conflictGroups = self::getConflictGroups();
        // 遍历每个冲突组
        foreach ($conflictGroups as $group) {
            // 判断要启用的插件是否存在于当前冲突组中
            if (in_array($pluginNameToEnable, $group)) {
                // 发现插件在冲突组中，检查组内其他插件是否已被启用
                // 遍历冲突组中的每一个插件
                foreach ($group as $conflictingPluginName) {
                    // 排除插件自身
                    if ($pluginNameToEnable !== $conflictingPluginName) {
                        // 调用 isEnableProperty 方法检查该冲突插件是否已为启用状态
                        if (self::isEnableProperty($conflictingPluginName, $muchId)) {
                            // 如果发现一个已启用的冲突插件
                            return [
                                // 标记存在冲突
                                'conflict' => true,
                                // 返回具体的提示信息
                                'message' => "开启失败，需要关闭【{$conflictingPluginName}】插件后才可以开启【{$pluginNameToEnable}】"
                            ];
                        }
                    }
                }
                // 插件只可能在一个冲突组里，所以找到后就无需再检查其他组
                break;
            }
        }
        // 遍历完所有相关冲突插件都未发现启用项，则返回无冲突
        return ['conflict' => false, 'message' => ''];
    }

    //================================================================
    // II. 云端通信 (Public API)
    //================================================================

    /**
     * 从云端获取插件市场列表
     * @param $shape
     * @return array|mixed
     */
    public static function notchList($shape)
    {
        try {
            // 定义插件市场接口 URL
            $markURL = 'https://geek.inotnpc.com/index.php?s=/unveil/aptness/genius.shtml';
            // 调用 Suspense 类的 fluorescent 方法获取授权信息
            $contrary = Suspense::fluorescent();
            // 设置 egon 参数，可能为分类或标识
            $postData['egon'] = $shape['egon'];
            // 对搜索名称进行 base64 编码，以防特殊字符问题
            $postData['searchName'] = base64_encode($shape['searchName']);
            // 设置分页参数：当前页码
            $postData['page'] = $shape['page'];
            // 设置分页参数：每页显示条数
            $postData['listRows'] = $shape['listRows'];
            // 将获取到的随机码赋值给 $postData 数组
            $postData['randCode'] = $contrary['rand_code'];
            // 调用 Suspense 类的 getRandomCode 方法获取核心随机码
            $postData['nucleus'] = Suspense::getRandomCode();
            // 设置质量参数为0
            $postData['quality'] = 0;
            // 发送 POST 请求获取插件列表
            $result = Gyration::_requestPost($markURL, $postData);
        } catch (\Exception $e) {
            // 如果发生异常，返回一个空字符串，而不是数组，以兼容 authcode
            $result = '';
        }
        return $result;
    }

    /**
     * 从云端获取模板市场数据
     * @param $surge
     * @return array|mixed
     */
    public static function channel($surge)
    {
        try {
            // 定义模板市场接口 URL
            $markURL = 'https://geek.inotnpc.com/index.php?s=/unveil/aptness/talent.shtml';
            // 调用 Suspense 类的 fluorescent 方法获取授权信息
            $contrar = Suspense::fluorescent();
            // 将获取到的随机码赋值给 $surge 数组
            $surge['randCode'] = $contrar['rand_code'];
            // 调用 Suspense 类的 getRandomCode 方法获取核心随机码
            $surge['nucleus'] = Suspense::getRandomCode();
            // 设置质量参数为0
            $surge['quality'] = 0;
            // 发送 POST 请求并解码返回的 JSON 结果
            $result = json_decode(self::_requestPost($markURL, $surge), true);
        } catch (\Exception $e) {
            // 如果发生异常，返回一个表示模板列表为空的默认结果
            $result = ['listTemplet' => null];
        }
        return $result;
    }

    /**
     * 在云端选择指定模板
     * @param $surge
     * @return array|mixed
     */
    public static function straight($surge)
    {
        try {
            // 定义选择模板的接口 URL
            $markURL = 'https://geek.inotnpc.com/index.php?s=/unveil/aptness/expert.shtml';
            // 调用 Suspense 类的 fluorescent 方法获取授权信息
            $contrar = Suspense::fluorescent();
            // 将获取到的随机码赋值给 $surge 数组
            $surge['randCode'] = $contrar['rand_code'];
            // 调用 Suspense 类的 getRandomCode 方法获取核心随机码
            $surge['nucleus'] = Suspense::getRandomCode();
            // 设置质量参数为0
            $surge['quality'] = 0;
            // 发送 POST 请求并解码返回的 JSON 结果
            $result = json_decode(self::_requestPost($markURL, $surge), true);
        } catch (\Exception $e) {
            // 如果发生异常，返回一个默认的加密模板标识（'0'代表失败或默认）
            $result = ['narrowMark' => self::templetEncode('0')];
        }
        return $result;
    }

    /**
     * 从云端获取原始的加密解锁授权数据
     * @return array|mixed
     */
    public static function notchStream()
    {
        try {
            // 定义获取解锁信息的接口 URL
            $markURL = 'https://geek.inotnpc.com/index.php?s=/unveil/aptness/slotData.shtml';
            // 调用 Suspense 类的 fluorescent 方法获取授权信息
            $contrar = Suspense::fluorescent();
            // 将获取到的随机码赋值给 $postData 数组
            $postData['randCode'] = $contrar['rand_code'];
            // 调用 Suspense 类的 getRandomCode 方法获取核心随机码
            $postData['nucleus'] = Suspense::getRandomCode();
            // 设置质量参数为0
            $postData['quality'] = 0;
            // 发送 POST 请求获取加密的解锁信息
            $result = Gyration::_requestPost($markURL, $postData);
        } catch (\Exception $e) {
            // 如果发生异常，返回一个空数组
            $result = array();
        }
        return $result;
    }

    //================================================================
    // III. 内部核心数据逻辑 (Core Internal Logic)
    //================================================================

    /**
     * [核心数据源] 读取、缓存和更新站点的插件解锁信息
     * @return array 包含当前和历史解锁信息的数组
     */
    public static function readNotch()
    {
        // 使用 IP 地址和一个固定字符串生成一个唯一的 MD5 标识，用于缓存和数据库查询
        $prometheus = md5("YuLuoClothes-" . self::getPositionByIp());
        // 根据 IP 标识从缓存中读取信息
        $tsInfo = cache($prometheus);

        // 如果缓存不存在或已失效
        if (!$tsInfo) {
            // 从数据库中根据 IP 标识查询插件信息
            $tsInfo = Db::name('template_slot')->where('prometheus', $prometheus)->find();
            // 如果数据库中也不存在该记录
            if (!$tsInfo) {
                // 从云端获取新的解锁信息，并准备插入数据库
                $tsInfo = ['apollo' => self::notchStream(), 'prometheus' => $prometheus];
                // 启动数据库事务
                Db::startTrans();
                try {
                    // 将新信息插入数据库并获取自增 ID
                    $tsInfo['id'] = Db::name('template_slot')->insertGetId($tsInfo);
                    // 提交事务
                    Db::commit();
                    // 缓存有效期2天
                    cache($prometheus, $tsInfo, 172800);
                } catch (\Exception $e) {
                    // 回滚事务，撤销插入操作
                    Db::rollback();
                    // 抛出异常，让调用方知道操作失败
                    throw new \Exception('授权数据初始化失败，请稍后重试' . $e->getMessage(), 500);
                }
            } else {
                // 缓存有效期2天
                cache($prometheus, $tsInfo, 172800);
            }
        }
        // 解密从缓存或数据库中获取的原始数据
        $unNotchStream = self::unNotchStream($tsInfo['apollo']);

        // 健壮性检查：如果解密失败或数据为空，则视作过期
        if (!is_array($unNotchStream)) {
            $unNotchStream = ['exp_time' => 0, 'client_ip' => '', 'un_lock_ids' => []];
        }

        // 如果解密后的数据显示授权已过期
        if (!isset($unNotchStream['exp_time']) || $unNotchStream['exp_time'] <= time()) {
            // 这个方法会更新数据库和缓存，但为了确保当前上下文的数据绝对同步，我们需要重新读取
            self::_handleExpiredSlotData($tsInfo, $prometheus);

            // 关键修复：重新从数据库加载最新的信息，确保内存状态与持久化状态一致
            $tsInfo = Db::name('template_slot')->where('prometheus', $prometheus)->find();

            // 再次解密，以获取刚刚被更新的、最新的授权数据
            $unNotchStream = self::unNotchStream($tsInfo['apollo']);

            // 再次进行健壮性检查，以防万一
            if (!is_array($unNotchStream)) {
                $unNotchStream = ['exp_time' => 0, 'client_ip' => '', 'un_lock_ids' => []];
            }
        }

        // 解码历史数据，准备返回值
        $historical_unlocked = null;
        if (isset($tsInfo['apollo_history'])) {
            $historicalUnNotchStream = self::unNotchStream($tsInfo['apollo_history']);
            // 健壮性检查
            if (is_array($historicalUnNotchStream)) {
                $historical_unlocked = isset($historicalUnNotchStream['un_lock_ids']) ? $historicalUnNotchStream['un_lock_ids'] : array();
            }
        }
        return [
            'current_unlocked' => isset($unNotchStream['un_lock_ids']) ? $unNotchStream['un_lock_ids'] : array(),
            'current_stream' => $unNotchStream,
            'historical_unlocked' => $historical_unlocked
        ];
    }

    /**
     * 解密从云端获取的完整解锁授权数据
     * @param $data
     * @return mixed
     */
    public static function unNotchStream($data)
    {
        // 使用 authcode 函数和特定密钥进行解密，并转为关联数组
        return json_decode(authcode($data, 'DECODE', base64_decode(self::KEY_SLOT_STREAM)), true);
    }

    //================================================================
    // IV. 私有辅助方法 (Private Helpers)
    //================================================================

    /**
     * [辅助方法] 处理已过期的站点授权数据 (写操作)
     * @param array $tsInfo 当前的站点信息数组 (引用传递)
     * @param string $prometheus 缓存和DB查询的键
     * @return array 最新的、解密后的授权数据
     */
    private static function _handleExpiredSlotData(&$tsInfo, $prometheus)
    {
        // 再次从云端获取最新的加密数据
        $newApolloData = self::notchStream();

        // 只有当云端数据与当前数据不同，且云端数据不为空时，才执行更新
        if ($newApolloData && $newApolloData !== $tsInfo['apollo']) {
            // 识别无效数据标记：检查是否存在 'is_poison' 标志
            $currentApolloDecoded = self::unNotchStream($tsInfo['apollo']);
            // 判断当前数据是否被标记为无效数据
            $isPoisonPill = (is_array($currentApolloDecoded) && isset($currentApolloDecoded['is_poison']) && $currentApolloDecoded['is_poison'] === true);

            // 防御性编程：如果当前数据被标记为无效，则真正的"旧数据"是历史记录；否则，才是当前数据。
            // 确定要保存到历史记录中的数据
            $oldApolloData = $isPoisonPill && isset($tsInfo['apollo_history']) ? $tsInfo['apollo_history'] : $tsInfo['apollo'];

            // 启动数据库事务
            Db::startTrans();
            try {
                // 原子性更新：将当前apollo数据存入历史，并用新数据覆盖当前apollo
                $updateResult = Db::name('template_slot')->where('prometheus', $prometheus)->update([
                    'apollo' => $newApolloData,
                    'apollo_history' => $oldApolloData
                ]);

                // 检查更新是否成功
                if ($updateResult === false) {
                    throw new \Exception('数据库更新失败', 500);
                }

                // 提交事务
                Db::commit();
                // 事务成功后，更新内存中的变量
                $tsInfo['apollo_history'] = $oldApolloData;
                $tsInfo['apollo'] = $newApolloData;
                // 缓存有效期2天
                Cache::tag('marketConflict')->set($prometheus, $tsInfo, 172800);
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 抛出异常，让调用方知道操作失败
                throw new \Exception('授权数据更新失败，请稍后重试' . $e->getMessage(), 500);
            }
        }
        // 此方法的返回值不再是关键，因为调用方会重新读取。
        // 但为了保持健壮性，仍然返回一个结构一致的数组。
        // 解密最新的授权数据
        $newStream = self::unNotchStream($tsInfo['apollo']);
        // 确保始终返回一个数组
        return is_array($newStream) ? $newStream : ['exp_time' => 0, 'client_ip' => '', 'un_lock_ids' => []];
    }

    /**
     * [辅助方法] 查找或创建用户的插件开关设置记录 (写操作)
     * @param int|string $muchId
     * @return array
     */
    public static function findOrCreateUserValve($muchId)
    {
        // 查询指定用户的插件开关设置记录
        $tsvInfo = Db::name('template_slot_valve')->where('much_id', $muchId)->find();
        // 如果是新用户，没有找到对应的设置记录
        if (!$tsvInfo) {
            // 根据用户反馈，新用户默认为空数组，即所有插件默认关闭
            $enabledPlugins = array();
            // 加密这个空的插件设置数据
            $petalDataEncoded = authcode(json_encode((is_array($enabledPlugins) ? array_values($enabledPlugins) : array())), 'ENCODE', base64_decode(self::KEY_PLUGIN_AUTHCODE));
            // 准备插入数据库的数据，is_reversal=1 表示使用新逻辑（启用列表）
            $tsvInfo = ['petal_data' => $petalDataEncoded, 'is_reversal' => 1, 'much_id' => $muchId];
            // 启动事务
            Db::startTrans();
            try {
                // 为新用户插入插件设置记录
                Db::name('template_slot_valve')->insert($tsvInfo);
                // 提交事务
                Db::commit();
                // 重新查询一次，以获取完整的记录信息（包括自增ID）
                $tsvInfo = Db::name('template_slot_valve')->where('much_id', $muchId)->find();
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
            }
        }
        return $tsvInfo;
    }

    /**
     * [辅助方法] 如果需要，则执行用户设置从旧模式到新模式的迁移 (写操作)
     * @param array $tsvInfo 用户设置记录 (引用传递)
     * @param array $readNotchResult 来自readNotch的返回结果
     */
    public static function migrateUserValveIfNeeded(&$tsvInfo, $readNotchResult)
    {
        if ($tsvInfo && isset($tsvInfo['is_reversal']) && $tsvInfo['is_reversal'] == 0) {
            // 解密旧的禁用插件列表
            $disabledPlugins = json_decode(authcode($tsvInfo['petal_data'], 'DECODE', base64_decode(self::KEY_PLUGIN_AUTHCODE)), true) ?: array();

            // 优先使用历史解锁列表进行迁移计算，如果历史列表不存在，则使用当前列表
            // 确定用于迁移计算的基准解锁插件列表
            $migrationBaseIds = $readNotchResult['historical_unlocked'] !== null
                ? $readNotchResult['historical_unlocked']
                : $readNotchResult['current_unlocked'];
            // 通过"用作迁移参考的解锁插件"和"已禁用插件"的差集，计算出启用的插件列表
            $enabledPlugins = array_diff($migrationBaseIds, $disabledPlugins);

            // 加密新的启用插件列表
            $petalDataEncoded = authcode(json_encode((is_array($enabledPlugins) ? array_values($enabledPlugins) : array())), 'ENCODE', base64_decode(self::KEY_PLUGIN_AUTHCODE));

            // 更新数据库记录
            Db::startTrans();
            try {
                Db::name('template_slot_valve')->where('id', $tsvInfo['id'])->update([
                    // 将标志位更新为 1，表示已迁移到新逻辑
                    'is_reversal' => 1,
                    // 保存新的启用列表数据
                    'petal_data' => $petalDataEncoded
                ]);
                Db::commit();
                // 关键修复：不再手动修改内存变量，而是从数据库重新加载以确保绝对同步
                $tsvInfo = Db::name('template_slot_valve')->where('id', $tsvInfo['id'])->find();
            } catch (\Exception $e) {
                Db::rollback();
                // 事务失败时，向上抛出框架异常，中断后续执行
                throw new \Exception($e->getMessage(), 500);
            }
        }
    }

    /**
     * [辅助方法] 清理用户启用列表中已失效的插件ID (写操作)
     * @param array $tsvInfo 用户设置记录
     * @param array $currentUnlockedIds 当前有效的解锁ID列表
     * @return array 清理后的启用列表
     */
    public static function sanitizeUserPetalData(&$tsvInfo, $currentUnlockedIds)
    {
        // 解密当前用户的插件设置（此时已确保是启用列表）
        $petalData = json_decode(authcode($tsvInfo['petal_data'], 'DECODE', base64_decode(self::KEY_PLUGIN_AUTHCODE)), true) ?: array();
        // 计算用户启用列表与当前解锁列表的交集，移除已失效的插件
        $cleanedEnabledPlugins = array_intersect($petalData, $currentUnlockedIds);

        if ((is_array($cleanedEnabledPlugins) ? count($cleanedEnabledPlugins) : 0) < count($petalData)) {
            // 重新加密清理后的启用插件列表
            $newPetalDataEncoded = authcode(json_encode((is_array($cleanedEnabledPlugins) ? array_values($cleanedEnabledPlugins) : array())), 'ENCODE', base64_decode(self::KEY_PLUGIN_AUTHCODE));
            Db::startTrans();
            try {
                Db::name('template_slot_valve')->where('id', $tsvInfo['id'])->update(['petal_data' => $newPetalDataEncoded]);
                Db::commit();
                // 更新内存变量
                $tsvInfo['petal_data'] = $newPetalDataEncoded;
                return $cleanedEnabledPlugins;
            } catch (\Exception $e) {
                Db::rollback();
                // 事务失败，向上抛出框架异常，中断后续执行
                throw new \Exception($e->getMessage(), 500);
            }
        }
        return $petalData;
    }

    //================================================================
    // V. 静态列表与定义 (Private Definitions)
    //================================================================

    /**
     * 提供一个固定的付费功能（插件）及其唯一标识的列表
     * @return array
     */
    private static function commonList()
    {
        return [
            '一键拨号' => base64_decode('YzJiMGU1ZWEtOTBlNi0xNmFmLTcwODYtNWUwOTU5NTRjZjA1'),
            '卡密列表' => base64_decode('MGU1ZDFiODctYzc0My03YjQwLTk2ZDAtYzViNmI0M2I0OGM0'),
            '网盘列表' => base64_decode('MmQwODY1MWMtMTViOS0xOTI0LWM4M2UtNmM2NTY4MGE4MmJl'),
            '公众通知' => base64_decode('MDMzY2Y4ZDEtNjMyYy04MDZiLTU3NWQtYjc3ZWM3ZjdiZGZk'),
            '内容点评' => base64_decode('NmE1MTYwNWItNzBlOC02NDJhLWE1NWYtY2I5YzA2NGNkZWU5'),
            '失物招领' => base64_decode('YzdhNjgxMWUtMjUzNC1hNTAzLTdjY2UtNzU1OTk1NTJmZTRj'),
            '同城信息' => base64_decode('N2NjZGU5ZTYtZGQ2Ny05MTVkLTMwYTUtYjFmNjNmYmRkOTI2'),
            '视频解析' => base64_decode('MjAzNDIxYzEtZWJjNS1kMzkzLTk2YTQtNDI0MjUxNzU4YWRi'),
            '二手交易' => base64_decode('NjdhMTUxMjktNmY2Ny1iOWQ5LWM0MjgtZDA3YjBhNTIxMjRi'),
            '求职招聘' => base64_decode('MTBmYzQ0NzYtYzhjMS1jY2QyLTFiNDUtZjVmODU2YTRkODk4'),
            '幸运抽奖' => base64_decode('ZDljYmIwZTAtYTRhZS05YWRhLTA1N2ItY2YwOGRmZDkxZWY3'),
            '通用存储' => base64_decode('YzkzMTE1NjMtZDI5ZS0zODcwLWEwZWYtMzZmMWY0OGQzZDE5'),
            '123云盘' => base64_decode('M2NhODJlMTgtNTY0ZC1hMTBjLTVjNWMtNjBmNzg3OTAzMThl'),
            '附近圈子' => base64_decode('YTUyOGFiNjItOTdiZi1lOGEyLWI0NWMtMWQ4ODAyOTBlOTFm'),
        ];
    }

    /**
     * 根据给定的唯一标识（Mark）查找并返回对应的插件名称
     * @param $mark
     * @return false|int|string
     */
    private static function getPluginNameByMark($mark)
    {
        // 获取所有插件的名称和ID映射列表
        $list = self::commonList();
        return array_search($mark, $list);
    }

    /**
     * 定义互斥的插件组
     * @return array[]
     */
    private static function getConflictGroups()
    {
        return [
            ['通用存储', '123云盘'],
        ];
    }

    //================================================================
    // VI. 通用工具 (Public Utilities)
    //================================================================

    /**
     * 加密单个模板标识
     * @param $value
     * @return string
     */
    public static function templetEncode($value)
    {
        return authcode($value, 'ENCODE', base64_decode(self::KEY_TEMPLATE_CODING), 0);
    }

    /**
     * 加密一个包含多个模板标识的数组
     * @param $data
     * @return mixed
     */
    public static function templetEncodeAll($data)
    {
        $data['home'] = authcode($data['home'], 'ENCODE', base64_decode(self::KEY_TEMPLATE_CODING), 0);
        $data['plaza'] = authcode($data['plaza'], 'ENCODE', base64_decode(self::KEY_TEMPLATE_CODING), 0);
        $data['goods'] = authcode($data['goods'], 'ENCODE', base64_decode(self::KEY_TEMPLATE_CODING), 0);
        $data['user'] = authcode($data['user'], 'ENCODE', base64_decode(self::KEY_TEMPLATE_CODING), 0);
        return $data;
    }

    /**
     * 解密单个模板标识
     * @param $value
     * @return string
     */
    public static function templetDecode($value)
    {
        return authcode($value, 'DECODE', base64_decode(self::KEY_TEMPLATE_CODING), 0);
    }

    /**
     * 解密一个包含多个模板标识的数组
     * @param $data
     * @return mixed
     */
    public static function templetDecodeAll($data)
    {
        $data['home'] = authcode($data['home'], 'DECODE', base64_decode(self::KEY_TEMPLATE_CODING), 0);
        $data['plaza'] = authcode($data['plaza'], 'DECODE', base64_decode(self::KEY_TEMPLATE_CODING), 0);
        $data['goods'] = authcode($data['goods'], 'DECODE', base64_decode(self::KEY_TEMPLATE_CODING), 0);
        $data['user'] = authcode($data['user'], 'DECODE', base64_decode(self::KEY_TEMPLATE_CODING), 0);
        return $data;
    }

    /**
     * 获取服务器的公网 IP 地址
     * @return string
     */
    public static function getPositionByIp()
    {
        // 尝试从缓存中读取 IP（缓存时间改为30分钟，提高灵活性）
        $getPositionByInfo = cache('getPositionByInfo');
        if (!$getPositionByInfo) {
            // 创建一个流上下文，用于配置网络请求行为
            $context = stream_context_create([
                // HTTP 相关设置
                'http' => [
                    // 设置请求超时时间为5秒
                    'timeout' => 5,
                    // 设置用户代理
                    'user_agent' => 'Mozilla/5.0 (compatible; AuthSystem/1.0)',
                    // 设置请求方法
                    'method' => 'GET'
                ],
                // SSL 相关设置
                'ssl' => [
                    // 忽略对等证书验证（仅用于IP获取服务）
                    'verify_peer' => false,
                    // 忽略对等证书名称检查
                    'verify_peer_name' => false
                ],
                // Socket 相关设置
                'socket' => [
                    // 绑定到所有网络接口，主要用于指定出站网络接口，这里设为0:0表示不特别指定
                    'bindto' => '0:0'
                ]
            ]);
            // 初始化客户端IP变量
            $clientIp = null;
            // 1. 尝试主接口
            $primaryUrl = 'https://gamematrix.qq.com/sdk/v2/get_gateway';
            try {
                $ipContent = @file_get_contents($primaryUrl, false, $context);
                if ($ipContent) {
                    $ipData = json_decode($ipContent, true);
                    if (!empty($ipData) && isset($ipData['ret']) && intval($ipData['ret']) === 0 && !empty($ipData['client_ip'])) {
                        // 验证IP格式
                        if (self::_validateIpAddress($ipData['client_ip'])) {
                            $clientIp = $ipData['client_ip'];
                        }
                    }
                }
            } catch (\Exception $e) {
                // 记录主接口失败日志
                throw new \Exception('获取接口失败: ' . $e->getMessage(), 500);
            }
            // 2. 如果主接口失败，则依次尝试备用接口
            if (!$clientIp) {
                $fallbackUrls = [
                    'https://r.inews.qq.com/api/ip2city?otype=json',
                    'https://my.ip.cn/json/'
                ];
                foreach ($fallbackUrls as $url) {
                    try {
                        $resContent = @file_get_contents($url, false, $context);
                        if ($resContent) {
                            $resData = json_decode($resContent, true);
                            if (!empty($resData) && isset($resData['data']['ip'])) {
                                // 验证IP格式
                                if (self::_validateIpAddress($resData['data']['ip'])) {
                                    $clientIp = $resData['data']['ip'];
                                    // 成功获取到IP，立即跳出循环
                                    break;
                                }
                            }
                        }
                    } catch (\Exception $e) {
                        // 记录备用接口失败日志
                        throw new \Exception('备用获取接口失败 [' . $url . ']: ' . $e->getMessage(), 500);
                    }
                }
            }
            // 3. 如果所有接口都失败，返回本地回环地址
            if (!$clientIp) {
                return '127.0.0.1';
            }
            // 将获取到的 IP 地址缓存起来，有效期30分钟
            Cache::tag('marketConflict')->set('getPositionByInfo', base64_encode($clientIp), 1800);
            // 返回获取到的 IP 地址
            return $clientIp;
        } else {
            // 从缓存中取出数据，解码并返回 IP 地址
            $cachedIp = base64_decode($getPositionByInfo);
            // 验证缓存中的IP格式是否有效
            if (self::_validateIpAddress($cachedIp)) {
                return $cachedIp;
            } else {
                // 缓存中的IP无效，清除缓存并重新获取
                Cache::rm('getPositionByInfo');
                return self::getPositionByIp();
            }
        }
    }

    /**
     * 验证IP地址格式是否有效
     * @param string $ip IP地址
     * @return bool
     */
    private static function _validateIpAddress($ip)
    {
        // 检查是否为有效的IPv4或IPv6地址
        if (!filter_var($ip, FILTER_VALIDATE_IP)) {
            return false;
        }
        // 排除私有IP地址和保留IP地址
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            return true;
        }
        // 对于授权系统，允许私有IP地址（用于内网环境）
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_IPV6);
    }
}
