<?php

namespace app\api\service;

use app\common\Assembly;
use app\common\Suspense;
use app\urge\controller\Upload;
use think\Db;

class Alternative
{
    /**
     * 小表情
     */
    public static function GetEmoji()
    {
        //查询有几个表情
        $absLocalRes = ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'expression';
        $chunk_result = array();
        if (is_dir($absLocalRes)) {
            // array_merge 重建数组索引 array_diff 过滤.和.. scandir 列出文件和目录
            $file = scandir($absLocalRes);
            array_multisort($file, SORT_ASC, SORT_NUMERIC);
            $arr = array_values(array_diff($file, ['.', '..']));
            $chunk_result = array_chunk($arr, 36);
        }
        return $chunk_result;
    }

    /**
     * 小表情
     */
    public static function ExpressionHtml($str)
    {
        $matches = array();
        $preg = '|\[#:(.*?)\]|';
        preg_match_all($preg, $str, $matches);
        if (count($matches[1]) > 0) {
            $domain = explode(':', $_SERVER['HTTP_HOST']);
            $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
            for ($i = 0; $i < count($matches[1]); $i++) {
                //判断文件是否存在
                $absLocalRes0 = ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'expression' . DS . $matches[1][$i] . '.gif';
                if (file_exists($absLocalRes0)) {
                    $absRess = "https://{$domain[0]}{$absAddress[0]}static/expression/" . $matches[1][$i] . '.gif';
                }
                $absLocalRes1 = ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'expression' . DS . $matches[1][$i] . '.jpg';
                if (file_exists($absLocalRes1)) {
                    $absRess = "https://{$domain[0]}{$absAddress[0]}static/expression/" . $matches[1][$i] . '.jpg';
                }
                $absLocalRes2 = ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'expression' . DS . $matches[1][$i] . '.png';
                if (file_exists($absLocalRes2)) {
                    $absRess = "https://{$domain[0]}{$absAddress[0]}static/expression/" . $matches[1][$i] . '.png';
                }
                $videoHtml = "<img src='{$absRess}' style='margin:0 3px;height: 20px;width: 20px;vertical-align: sub;'/>";
                $str = preg_replace($preg, $videoHtml, $str, 1);
            }
        }
        return $str;
    }

    /**
     * 获取商品sku
     */
    public static function GetShopSku($id, $much_id)
    {
        $info = Db::name('shop_vested')->where('sp_id', $id)->where('much_id', $much_id)->find();
        $arr = array();
        if ($info) {
            $arr['list'] = json_decode($info['sa_list'], true);
            $arr['sa_name'] = $info['sa_name'];
            $arr['id'] = $info['id'];
        }
        return $arr;
    }

    public static function GetAnalysis($url, $muchId, $is_dol = 0)
    {
        //  获取url中的域名
        $host = parse_url($url, PHP_URL_HOST);

        //  优先匹配适配域名
        $vacInfo = Db::name('video_analysis_config')->where('adaptation_domain', $host)->where('interface_type', 1)->where('much_id', $muchId)->find();

        //  判断适配域名是否有指定解析地址
        if (!$vacInfo) {
            //  查找默认解析接口
            $vacInfo = Db::name('video_analysis_config')->where('interface_type', 0)->where('is_default', 1)->where('much_id', $muchId)->find();
            //  未找到默认解析接口
            if (!$vacInfo) {
                //  返回错误，未设置解析接口
                return ['code' => 0, 'msg' => 'error , 未设置默认的解析接口'];
            }
        }
        //  判断是get请求还是post请求  0.get 1.post
        $reqMethod = intval($vacInfo['req_method']);
        //  转换请求参数 json转数组
        $confusionParams = json_decode($vacInfo['req_params'], true);
        //  请求参数
        $params = [
            $confusionParams['urlName'] => $url,
        ];
        foreach ($confusionParams['otherParams'] as $item) {
            $params[$item['key']] = $item['value'];
        }
        //  定义返回结果
        $resultData = null;
        //  根据请求类型进行判断
        switch ($reqMethod) {
            case 0:
                //  获取Url
                $parseUrl = $vacInfo['parse_url'];
                //  判断是否包含?号传参
                if (strpos($parseUrl, '?') !== false) {
                    $parseUrl .= '&';
                } else {
                    $parseUrl .= '?';
                }
                //  数组转URL编码的查询字符串
                $data = http_build_query($params, '', '&');
                //  进行get请求
                $resultData = Suspense::_requestGet("{$parseUrl}{$data}");
                break;
            case 1:
                if (intval($vacInfo['req_type']) === 0) {
                    $data = $params;
                } else {
                    $data = json_encode($params, 320);
                }
                //  进行post请求
                $resultData = Suspense::_requestPost($vacInfo['parse_url'], $data);
                break;
        }
        //  请求结果json转数组
        $result = json_decode($resultData, true);
        //  判断json解析是否出错
        if (json_last_error() !== JSON_ERROR_NONE) {
            //  返回错误，未设置解析接口
            return ['code' => 0, 'msg' => 'error , 接口返回值错误'];
        }
        //  请求结果参数
        $resParams = json_decode($vacInfo['res_params'], true);
        //  解析结果
        $parse = function ($resParams, $result) {
            if (is_array($resParams)) {
                foreach ($resParams as $val) {
                    //  判断变量是否为数组
                    if (is_array($result)) {
                        $result = $result[$val];
                    } else {
                        $result = '';
                        break;
                    }
                }
            }
            return $result;
        };
        //  获取标题
        $title = trim($parse($resParams['title'], $result));
        //  获取封面图
        $cover = trim($parse($resParams['cover'], $result));
        //  判断封面图是否为空
        if ($cover === '') {
            $cover = $vacInfo['default_video_cover'];
        } else {
            if ($is_dol == 1) {
                $cover = self::download_file($cover, $muchId);
            }
        }
        //  获取视频
        $video = $parse($resParams['video'], $result);
        if (empty($video)) {
            return ['code' => 0, 'msg' => 'error，解析失败，请稍后重试！'];
        }
        return ['code' => 1, 'title' => $title, 'cover' => $cover, 'video' => $video];
    }

    /**
     * 下载图片到本地
     */
    public static function download_file($url, $muchId)
    {
        $agreement = date('Ymd');
        $file = @file_get_contents($url);
        $type = self::getUrlFileExt($url);// 图片后缀
        $path = ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'uploads' . DS . $agreement . DS;
        if (file_exists($path) === false) {
            @mkdir($path, 0777, true);
        }
        $path .= $fileName = md5(Assembly::uuid()) . '.' . strtolower($type);
        @file_put_contents($path, $file);
        $upload = new Upload($muchId);
        $result = $upload->remoteImageUploadToThirdPartyStorage($path, $fileName);
        if ($result['code'] > 0) {
            return $result['url'];
        }
        return '';
    }

    public static function getUrlFileExt($url)
    {
        //去除url中的参数
        $tempArray = explode("?", $url);
        //将url按斜杠/分割成数组
        $tempArray = explode("/", $tempArray[0]);
        //取数组的最后一个值，即文件名
        $tempFileName = $tempArray[count($tempArray) - 1];
        //将文件名按.分割成数组
        $fileNameArray = explode(".", $tempFileName);
        //返回文件名的最后一个值，即文件后缀
        return $fileNameArray[count($fileNameArray) - 1];
    }

}