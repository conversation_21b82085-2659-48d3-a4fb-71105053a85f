<?php


namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\Alternative;
use app\api\service\TmplService;
use app\common\Playful;
use think\Cache;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\Url;
use think\View;

#小秘密
class Stealth extends Base
{
    //小秘密列表
    public function softly()
    {
        $url = $this->defaultQuery();
        $hazy_name = emoji_encode(request()->get('hazy_name', ''));
        $list = Db::name('sprout')
            ->alias('sp')
            ->join('user us', 'sp.user_id = us.id')
            ->where('sp.content|us.user_nick_name', 'like', "%{$hazy_name}%")
            ->where('sp.is_del', 0)
            ->where('sp.much_id&us.much_id', $this->much_id)
            ->orderRaw('sp.status <> 0 asc,case when sp.status = 0 then sp.id end asc,case when sp.status <> 0 then sp.id end desc')
            ->field('sp.*,us.user_nick_name,us.user_wechat_open_id')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]])
            ->each(function ($item, $key) {
                $item['user_nick_name'] = emoji_decode($item['user_nick_name']);
                return $item;
            });
        $expressionHtml = function ($val) {
            return Alternative::ExpressionHtml($val);
        };
        $this->assign('expressionHtml', $expressionHtml);
        $this->assign('list', $list);
        $this->assign('hazy_name', emoji_decode($hazy_name));
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //小秘密详情
    public function limpid()
    {
        //小秘密编号
        $uplid = intval(request()->get('uplid', 0));
        //小秘密内容
        $sproutInfo = Db::name('sprout')->where('id', $uplid)->where('is_del', 0)->where('much_id', $this->much_id)->find();
        //是否存在
        if ($sproutInfo) {
            $userInfo = Db::name('user')->where('id', $sproutInfo['user_id'])->where('much_id', $this->much_id)->find();
            $sproutInfo['user_nick_name'] = emoji_decode($userInfo['user_nick_name']);
            $sproutInfo['user_wechat_open_id'] = $userInfo['user_wechat_open_id'];

            if (intval($sproutInfo['at_user_id']) !== 0) {
                $atUserInfo = Db::name('user')->where('id', $sproutInfo['at_user_id'])->where('much_id', $this->much_id)->find();
                $sproutInfo['at_user_nick_name'] = emoji_decode($atUserInfo['user_nick_name']);
                $sproutInfo['at_user_wechat_open_id'] = $atUserInfo['user_wechat_open_id'];
            }

            $expressionHtml = function ($val) {
                return Alternative::ExpressionHtml($val);
            };
            $this->assign('expressionHtml', $expressionHtml);

            $this->assign('list', $sproutInfo);
            return $this->fetch();
        } else {
            //不存在重定向
            $url = Url::build('stealth/softly');
            return "<script>alert('很抱歉，您当前所查看的小秘密不存在！');location.href='{$url}';</script>";
        }
    }

    /*
     * 修改小秘密部分数据
     */
    public function updateLimpidFrequency()
    {
        if (request()->isPost() && request()->isAjax()) {
            $paid = request()->post('paid');
            $type = intval(request()->post('type'));
            $updateData = array();
            switch ($type) {
                case 0:
                    $updateData['praise_number'] = intval(input('post.newPraiseNumber', 0));
                    break;
                case 1:
                    $updateData['send_time'] = strtotime(date(input('post.newSendTime')));
                    break;
            }
            Db::startTrans();
            try {
                Db::name('sprout')->where('id', $paid)->where('much_id', $this->much_id)->update($updateData);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $this->error('参数错误', 'essay/index');
        }
    }

    //小秘密审核状态
    public function teaching()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            //小秘密内容
            $spInfo = Db::name('sprout')->where('id', $data['sid'])->where('is_del', 0)->where('much_id', $this->much_id)->find();
            //是否存在
            if (!$spInfo) {
                return json(['code' => 0, 'msg' => 'error , invalid data !']);
            } else {
                //是否已审核
                if (intval($spInfo['status']) !== 0) {
                    return json(['code' => 1]);
                }
            }
            //部分内容
            $slight = emoji_encode(subtext(emoji_decode(strip_tags($spInfo['content'])), 10));
            //审核通过
            if ($data['code'] == 1) {
                $combination = "您发布的小秘密已通过审核！";
                $combinationPrompt = "您发布的 \"{$slight}\" (小秘密) 已通过审核！";
                //审核拒绝
            } elseif ($data['code'] == 2) {
                $combination = "您发布的小秘密未通过审核！";
                $combinationPrompt = "您发布的 \"{$slight}\"（小秘密）未通过审核，未通过原因：{$data['caption']}！";
                //数据错误
            } else {
                return json(['code' => 0, 'msg' => 'error']);
            }
            //更改的数据
            $spData = ['check_time' => time(), 'status' => $data['code']];
            if (intval($data['code']) === 2) {
                //拒绝原因
                $spData['reject_reason'] = $data['caption'];
            }
            Db::startTrans();
            try {
                Db::name('sprout')->where('id', $data['sid'])->where('much_id', $this->much_id)->update($spData);
                Db::name('user_smail')->insert(['user_id' => $spInfo['user_id'], 'maring' => $combinationPrompt, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                $tmplData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $spInfo['user_id'],
                    'page' => 'yl_welore/pages/packageD/whisper/index',
                    'keyword1' => $combination,
                    'keyword2' => date('Y年m月d日 H:i:s', time())
                ];
                $tmplService = new TmplService();
                $tmplService->add_template($tmplData);
                if (intval($spInfo['at_user_id']) !== 0) {
                    $tmplData['user_id'] = $spInfo['at_user_id'];
                    $tmplData['keyword1'] = 'Ta给你发了一条小秘密';
                    $tmplService->add_template($tmplData);
                }
                return json(['code' => 1, 'msg' => '操作成功']);
            }
        } else {
            $this->error('参数错误', 'stealth/softly');
        }
    }

    //删除小秘密
    public function delSoftly()
    {
        if (request()->isPost() && request()->isAjax()) {
            $sid = request()->post('sid');
            Db::startTrans();
            try {
                //删除小秘密
                Db::name('sprout')->where('id', $sid)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '操作成功']);
            }
        }
    }


    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, array(), 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    //小秘密回复
    public function janitor()
    {
        $url = $this->defaultQuery();
        $hazy_name = emoji_encode(request()->get('hazy_name', ''));
        $list = Db::name('sprout_reply')
            ->alias('sr')
            ->join('sprout sp', 'sr.sp_id = sp.id')
            ->join('user us', 'sr.re_user_id = us.id')
            ->where('sr.re_content|us.user_nick_name', 'like', "%{$hazy_name}%")
            ->where('sr.is_del', 0)
            ->where('sr.much_id&sp.much_id', $this->much_id)
            ->orderRaw('sr.status <> 0 asc,case when sr.status = 0 then sr.id end asc,case when sr.status <> 0 then sr.id end desc')
            ->field('sr.*,sp.content,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]])
            ->each(function ($item, $key) {
                $item['user_nick_name'] = emoji_decode($item['user_nick_name']);
                return $item;
            });
        $expressionHtml = function ($val) {
            return Alternative::ExpressionHtml($val);
        };
        $this->assign('expressionHtml', $expressionHtml);
        $this->assign('list', $list);
        $this->assign('hazy_name', emoji_decode($hazy_name));
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //小秘密回复详情
    public function opaque()
    {
        //小秘密编号
        $uplid = intval(request()->get('uplid', 0));
        //小秘密内容
        $sproutReplyInfo = Db::name('sprout_reply')->where('id', $uplid)->where('is_del', 0)->where('much_id', $this->much_id)->find();
        //是否存在
        if ($sproutReplyInfo) {
            $expressionHtml = function ($val) {
                return Alternative::ExpressionHtml($val);
            };
            $this->assign('expressionHtml', $expressionHtml);
            $spInfo = Db::name('sprout')->where('id', $sproutReplyInfo['sp_id'])->where('much_id', $this->much_id)->find();
            $this->assign('spInfo', $spInfo);
            $userInfo = Db::name('user')->where('id', $sproutReplyInfo['re_user_id'])->where('much_id', $this->much_id)->find();
            $sproutReplyInfo['user_nick_name'] = emoji_decode($userInfo['user_nick_name']);
            $sproutReplyInfo['user_wechat_open_id'] = $userInfo['user_wechat_open_id'];
            $this->assign('list', $sproutReplyInfo);
            return $this->fetch();
        } else {
            //不存在重定向
            $url = Url::build('stealth/softly');
            return "<script>alert('很抱歉，您当前所查看的小秘密回复不存在！');location.href='{$url}';</script>";
        }
    }

    //小秘密审核状态
    public function politics()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            //小秘密回复
            $sprInfo = Db::name('sprout_reply')->where('id', $data['sid'])->where('is_del', 0)->where('much_id', $this->much_id)->find();
            //是否存在
            if (!$sprInfo) {
                return json(['code' => 0, 'msg' => 'error , invalid data !']);
            } else {
                //是否已审核
                if (intval($sprInfo['status']) !== 0) {
                    return json(['code' => 1]);
                }
            }
            //部分内容
            $slight = emoji_encode(subtext(emoji_decode(strip_tags($sprInfo['re_content'])), 10));
            //审核通过
            if ($data['code'] == 1) {
                $combination = "您回复的小秘密已通过审核！";
                $combinationPrompt = "您回复的 \"{$slight}\" (小秘密) 已通过审核！";
                //审核拒绝
            } elseif ($data['code'] == 2) {
                $combination = "您回复的小秘密未通过审核！";
                $combinationPrompt = "您回复的 \"{$slight}\"（小秘密）未通过审，未通过原因：{$data['caption']}！";
                //数据错误
            } else {
                return json(['code' => 0, 'msg' => 'error']);
            }
            //更改的数据
            $spData = ['check_time' => time(), 'status' => $data['code']];
            if (intval($data['code']) === 2) {
                //拒绝原因
                $spData['reject_reason'] = $data['caption'];
            }
            Db::startTrans();
            try {
                Db::name('sprout_reply')->where('id', $data['sid'])->where('much_id', $this->much_id)->update($spData);
                Db::name('user_smail')->insert(['user_id' => $sprInfo['re_user_id'], 'maring' => $combinationPrompt, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                $tmplData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $sprInfo['user_id'],
                    'page' => 'yl_welore/pages/packageD/whisper/index',
                    'keyword1' => $combination,
                    'keyword2' => date('Y年m月d日 H:i:s', time())
                ];
                $tmplService = new TmplService();
                $tmplService->add_template($tmplData);
                return json(['code' => 1, 'msg' => '操作成功']);
            }
        } else {
            $this->error('参数错误', 'stealth/softly');
        }
    }

    //删除小秘密回复
    public function delJanitor()
    {
        if (request()->isPost() && request()->isAjax()) {
            $sid = request()->post('sid');
            Db::startTrans();
            try {
                //删除小秘密
                Db::name('sprout_reply')->where('id', $sid)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '操作成功']);
            }
        }
    }

    //  小秘密设置
    public function janitor_setting()
    {
        if (request()->isPost() && request()->isAjax()) {
            $uplid = request()->post('uplid');

            $data['custom_hiss_title'] = request()->post('title');

            $data['auto_hiss'] = request()->post('hiss');
            $data['hiss_limit'] = request()->post('hissLimit');

            $data['reply_auto_hiss'] = request()->post('replyHiss');
            $data['reply_hiss_limit'] = request()->post('replyHissLimit');

            Db::startTrans();
            try {
                Db::name('paper_smingle')->where('id', $uplid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $tuaList = Db::name('paper_smingle')->where('much_id', $this->much_id)->find();
            $this->assign('list', $tuaList);
            return $this->fetch();
        }
    }

    /*
     * 回复楼中楼
     */
    public function replyBuilding()
    {
        $data['reply_id'] = intval(input('post.paperId'));
        $paperReplyInfo = Db::name('paper_reply')->where('id', $data['reply_id'])->where('much_id', $this->much_id)->find();
        if ($paperReplyInfo) {
            $data['user_id'] = intval(input('post.userId'));
            $data['reply_user_id'] = intval(input('post.uid', 0));
            $data['duplex_content'] = emoji_encode(input('post.content'));
            $duplexTime = trim(input('post.duplexTime'));
            if ($duplexTime) {
                $duplexTimeFormat = intval(floatval($duplexTime) / 1000);
            } else {
                $duplexTimeFormat = time();
            }
            //  回复时间
            $data['duplex_time'] = $duplexTimeFormat;
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('paper_reply_duplex')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            Cache::clear("globalIndexCache_{$this->much_id}");
            return json(['code' => 1, 'msg' => '回复成功，请在小程序里查看回复内容！']);
        } else {
            return json(['code' => 0, 'msg' => '帖子回复ID填写有误']);
        }
    }
}