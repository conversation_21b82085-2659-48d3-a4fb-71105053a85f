<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:86:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/user/material.html";i:1677477275;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755516152;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a></li>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<link rel="stylesheet" href="assets/css/colorful-font.css?v=<?php echo time(); ?>">
<style>.am-table > tbody > tr > td{font-size:14px;margin-top:8px;}.am-table > tbody > tr > td:nth-child(1){text-align:right;color:#000000;padding-right:20px;}.am-table > tbody > tr > td:nth-child(2){padding-left:20px;color:#999;}a:hover{color:black!important;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-user"></span> 用户资料
        </div>
        <div class="tpl-portlet-input tpl-fz-ml right">
            <a href="<?php echo url('user/editMaterial'); ?>&uid=<?php echo $userInfo['id']; ?>" style="font-size:12px;color:black;font-weight: bold;border: 1px solid;padding:3px 6px;">
                <span class="am-icon-edit"></span>编辑用户资料
            </a>
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-form am-form-horizontal" style="width:700px;margin:0 auto;padding:25px 3% 10px 3%;overflow:hidden;border:solid 1px #cccccc;box-shadow:0px 0px 10px 0px rgba(0, 0, 0, 1);">
                    <table class="am-table am-table-bordered">
                        <tr>
                            <td class="am-text-middle" style="width:35%;">用户头像</td>
                            <td class="am-text-middle" style="width:65%;">
                                <?php if($userAF !=''): ?>
                                <div style="background:url('<?php echo $userAF['adorn_icon']; ?>') no-repeat top center / cover;height:75px;width:75px;display:flex;justify-content:center;align-items:center;border-radius:50%;">
                                    <?php if($userInfo['user_head_sculpture'] && $userInfo['user_head_sculpture'] !='/yl_welore/style/icon/default.png'): ?>
                                    <img src="<?php echo $userInfo['user_head_sculpture']; ?>" style="height: 50px;width: 50px;border-radius: 50%;">
                                    <?php else: ?>
                                    <img src="<?php echo urlBridging('static/disappear/tourist.png'); ?>" style="height: 50px;width: 50px;border-radius: 50%;">
                                    <?php endif; ?>
                                </div>
                                <?php else: if($userInfo['user_head_sculpture'] && $userInfo['user_head_sculpture'] !='/yl_welore/style/icon/default.png'): ?>
                                <img src="<?php echo $userInfo['user_head_sculpture']; ?>" style="height: 50px;width: 50px;border-radius: 50%;">
                                <?php else: ?>
                                <img src="<?php echo urlBridging('static/disappear/tourist.png'); ?>" style="height: 50px;width: 50px;border-radius: 50%;">
                                <?php endif; endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">用户昵称</td>
                            <td class="am-text-middle" style="display:flex;">
                                <div class="<?php echo $userNameStyle; ?>">
                                    <span title="<?php echo emoji_decode($userInfo['user_nick_name']); ?>">
                                    <?php echo emoji_decode($userInfo['user_nick_name']); ?>
                                    </span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">用户等级</td>
                            <td class="am-text-middle" style="width:65%;">
                                <?php $tempLevelcount = 0; if(is_array($userLevel) || $userLevel instanceof \think\Collection || $userLevel instanceof \think\Paginator): $i = 0; $__LIST__ = $userLevel;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;if($vo['level_hierarchy']==$userInfo['level']): ?>
                                <span title="Lv.<?php echo $vo['level_hierarchy']; ?> - <?php echo $vo['level_name']; ?>" style="margin-left: 5px;">
                                <img src="<?php echo $vo['level_icon']; ?>" style="width: auto;height: 18px;margin-top: -5px;">
                                </span>
                                <?php else: $tempLevelcount++; endif; endforeach; endif; else: echo "" ;endif; if($tempLevelcount==count($userLevel)): $levelbuoy = count($userLevel) - 1;?>
                                <span title="Lv.<?php echo $userLevel[$levelbuoy]['level_hierarchy']; ?> - <?php echo $userLevel[$levelbuoy]['level_name']; ?>" style="margin-left: 5px;">
                                <img src="<?php echo $userLevel[$levelbuoy]['level_icon']; ?>" style="width: auto;height: 18px;margin-top: -5px;">
                                </span>
                                <?php endif; ?>
                                <span style="margin-left:5px;" title="当前经验值：<?php echo $userInfo['experience']; ?>，下一阶段升级所需经验值：<?php echo $nextExperience; ?>">
                                ( <?php echo $userInfo['experience']; ?> / <?php echo $nextExperience; ?> )
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">用户认证</td>
                            <td class="am-text-middle" style="width:65%;">
                                <?php if($uatInfo == 1): ?>
                                <a href="<?php echo url('depend/acquire'); ?>&hazy_name=<?php echo $userInfo['user_wechat_open_id']; ?>&page=1" target="_blank" title="用户已通过认证">
                                    已认证
                                </a>
                                <?php else: ?>
                                未认证
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">用户性别</td>
                            <td class="am-text-middle" style="width:65%;">
                                <?php if($userInfo['gender']==0): ?>未知<?php elseif($userInfo['gender']==1): ?>男<?php elseif($userInfo['gender']==2): ?>女<?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">佩戴勋章</td>
                            <td class="am-text-middle" style="width:65%;">
                                <?php if($userMedal): ?>
                                <span title="<?php echo $userMedal['merit_name']; ?> - <?php echo $userMedal['merit_annotate']; ?>">
                                    <img src="<?php echo $userMedal['merit_icon']; ?>" style="width:auto;height:22px;">
                                </span>
                                <?php else: ?>
                                <span title="当前未佩戴勋章" style="color:#8da9b9;">
                                    当前未佩戴勋章
                                </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">手机号</td>
                            <td class="am-text-middle" style="width:65%;">
                                <?php if($userInfo['user_phone']): ?><?php echo $userInfo['user_phone']; else: ?>未绑定<?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">openid</td>
                            <td class="am-text-middle" style="width:65%;"><?php echo $userInfo['user_wechat_open_id']; ?></td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">剩余<?php echo $defaultNavigate['currency']; ?></td>
                            <td class="am-text-middle" style="width:65%;"><?php echo $userInfo['conch']; ?></td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">剩余<?php echo $defaultNavigate['confer']; ?></td>
                            <td class="am-text-middle" style="width:65%;"><?php echo $userInfo['fraction']; ?></td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">荣誉点</td>
                            <td class="am-text-middle" style="width:65%;"><?php echo $userInfo['honor_point']; ?></td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">邀请码</td>
                            <td class="am-text-middle" style="width:65%;"><?php echo $userInfo['code']; ?></td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">状态</td>
                            <td class="am-text-middle" style="width:65%;">
                                <?php if($userInfo['status']==0): ?>
                                <span style="color:red;">禁止</span>
                                <?php else: ?>
                                <span style="color:green;">正常</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php if($userInfo['status']==0): ?>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">封禁原因</td>
                            <td class="am-text-middle" style="width:65%;"><?php echo $userInfo['forbid_prompt']; ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">会员到期时间</td>
                            <td class="am-text-middle" style="width:65%;">
                                <?php if($userInfo['vip_end_time']>time()): ?>
                                <?php echo date('Y-m-d',$userInfo['vip_end_time']); elseif($userInfo['vip_end_time']!='0'): ?>
                                此用户会员已过期
                                <?php else: ?>
                                此用户没有开通过会员
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">访问IP地址</td>
                            <td class="am-text-middle" style="width:65%;"><?php echo $userInfo['user_access_ip']; ?></td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">注册时间</td>
                            <td class="am-text-middle" style="width:65%;"><?php echo date('Y-m-d H:i:s',$userInfo['user_reg_time']); ?></td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">最后访问时间</td>
                            <td class="am-text-middle" style="width:65%;">
                                <?php if($userInfo['user_last_time']): ?><?php echo date('Y-m-d H:i:s',$userInfo['user_last_time']); else: ?>已经很长时间没有来过了<?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">个性签名</td>
                            <td class="am-text-middle" style="width:65%;">
                                <?php if($userInfo['autograph']): ?><?php echo emoji_decode($userInfo['autograph']); else: ?>无<?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<?php if($much_role=='founder'): ?>
<script>
    var updateFix = function () {
        layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("<?php echo url('index/repairMissing'); ?>", function (data) {
                if (data !== false) {
                    layer.msg('数据库缺失字段修复完成', {time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('修复失败', {time: 1600}, function () {
                        location.reload();
                    });
                }
            });
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }
</script>
<?php endif; ?>
<script>
    new Vue({
        el: '#menu',
        data() {
            return {
                menuActive: '<?php echo $menuActive; ?>',
                menuList: []
            };
        },
        created() {
            const menuList = '<?php echo $menuList; ?>';
            if (menuList !== '') {
                const menuData = JSON.parse(decodeURIComponent(atob(menuList)));
                for (let i = 0; i < menuData.length; i++) {
                    if (menuData[i].pid === 0) {
                        menuData[i].childMenuItems = [];
                        this.menuList.push(menuData[i]);
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        if (menuData[i].pid === this.menuList[j].id) {
                            const childItem = JSON.parse(JSON.stringify(menuData[i]));
                            childItem.grandsonList = [];
                            this.menuList[j].childMenuItems.push(childItem);
                            break;
                        }
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        for (let k = 0; k < this.menuList[j].childMenuItems.length; k++) {
                            if (menuData[i].pid === this.menuList[j].childMenuItems[k].id) {
                                this.menuList[j].childMenuItems[k].grandsonList.push(menuData[i]);
                                break;
                            }
                        }
                    }
                }
            }
        }
    });

    $(function () {
        setInterval(reballot, 15000);
    });

    var reballot = function () {
        $.getJSON("<?php echo url('ordinary'); ?>", function (data) {
            var i = 0;
            if (data.notice > 0 || data.vacant > 0) {
                i++;
            }
            $('#notice-0,#notice-1').text(data.notice);
            $('#vacant-0,#vacant-1').text(data.vacant);
            if ((data.notice + data.vacant) <= data.preCount) {
                i = 0;
            } else {
                $.post("<?php echo url('receipt'); ?>", {'multiply': (data.notice + data.vacant)});
            }
            if (i > 0) {
                var player = $("#backPlayer")[0];
                player.play();
            }
        });
    }

    var retakeCache = function () {
        $.get("<?php echo url('index/purgeCache'); ?>", function () {
            layer.msg('缓存清理完成', {time: 1000}, function () {
                location.reload();
            });
        });
    }
</script>
<script>
// 添加全局变量
window.EDITOR_MODE = false;  // 标记是否在编辑器模式

// AJAX 拦截器 - 自动还原代理 URL 为原始 URL
(function() {
    // 代理 URL 的匹配模式
    var proxyUrlPattern = /[?&]url=([^&]+)/;
    var proxyBaseUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>";

    // URL 还原函数
    function restoreOriginalUrl(url) {
        try {
            // 检查是否是代理 URL
            if (typeof url === 'string' && url.indexOf(proxyBaseUrl) !== -1) {
                var match = url.match(proxyUrlPattern);
                if (match && match[1]) {
                    // 解码并返回原始 URL
                    return decodeURIComponent(match[1]);
                }
            }
            return url;
        } catch (e) {
            // 出错时返回原始值，确保不影响正常功能
            console.warn('URL restore failed:', e);
            return url;
        }
    }

    // 增强版 URL 还原函数 - 支持从 DOM 元素的 data 属性获取原始 URL
    window.getOriginalSrc = function(element) {
        try {
            if (element && element.getAttribute) {
                // 优先从 data-original-src 获取
                var originalSrc = element.getAttribute('data-original-src');
                if (originalSrc) {
                    return originalSrc;
                }
                // 其次尝试从当前 src 还原
                var currentSrc = element.src || element.getAttribute('src');
                return restoreOriginalUrl(currentSrc);
            }
            return element;
        } catch (e) {
            console.warn('getOriginalSrc failed:', e);
            return element;
        }
    };

    // 递归处理数据对象，还原其中的代理 URL
    function restoreDataUrls(data) {
        try {
            if (typeof data === 'string') {
                return restoreOriginalUrl(data);
            } else if (Array.isArray(data)) {
                return data.map(restoreDataUrls);
            } else if (data && typeof data === 'object') {
                var restored = {};
                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        restored[key] = restoreDataUrls(data[key]);
                    }
                }
                return restored;
            }
            return data;
        } catch (e) {
            // 出错时返回原始数据
            console.warn('Data restore failed:', e);
            return data;
        }
    }

    // 保存原始的 jQuery AJAX 方法
    var originalAjax = $.ajax;
    var originalPost = $.post;
    var originalGet = $.get;

    // 重写 $.ajax
    $.ajax = function(options) {
        try {
            if (options && options.data) {
                options.data = restoreDataUrls(options.data);
            }
        } catch (e) {
            console.warn('AJAX data restore failed:', e);
        }
        return originalAjax.apply(this, arguments);
    };

    // 重写 $.post
    $.post = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('POST data restore failed:', e);
        }
        return originalPost.call(this, url, data, success, dataType);
    };

    // 重写 $.get
    $.get = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('GET data restore failed:', e);
        }
        return originalGet.call(this, url, data, success, dataType);
    };
})();

// 修改检测函数
function checkResourceAccess() {
    var isHttps = window.location.protocol === 'https:';
    
    if (isHttps) {
        // 处理图片
        document.querySelectorAll('img[src^="http://"]').forEach(function(img) {
            // 如果是编辑器模式，所有图片都使用代理URL显示
            if(window.EDITOR_MODE) {
                var originalSrc = img.getAttribute('data-src') || img.src;
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
                return;
            }
            
            // 非编辑器模式下排除富文本编辑器内的图片
            if(img.closest('.w-e-text') || img.closest('.w-e-text-container')) {
                return;
            }
            
            var originalSrc = img.getAttribute('data-src') || img.src;
            var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);

            var tmpImg = new Image();
            tmpImg.onload = function() {
                // 原始图片可访问，不做处理
            }
            tmpImg.onerror = function() {
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = proxyUrl;
            }
            tmpImg.src = originalSrc;
        });
        
        // 处理视频
        document.querySelectorAll('video').forEach(function(video) {
            // 处理video的src属性
            if (video.src && video.src.startsWith('http://')) {
                handleResource(video, 'src');
            }
            
            // 处理source标签
            video.querySelectorAll('source[src^="http://"]').forEach(function(source) {
                handleResource(source, 'src');
            });
        });
    }
}

// 统一的资源处理函数
function handleResource(element, srcAttr) {
    var originalSrc = element[srcAttr];
    var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
    
    // 创建临时对象测试资源是否可访问
    var tmpObj = new Image(); // 用Image对象测试连接可用性
    tmpObj.onload = function() {
        // 原始资源可访问，不做处理
    }
    tmpObj.onerror = function() {
        // 原始资源不可访问，切换到代理
        // 存储原始 URL 到 data 属性
        element.setAttribute('data-original-src', originalSrc);
        element[srcAttr] = proxyUrl;

        // 同时更新父级a标签的href
        var parent = element.parentElement;
        if (parent && parent.tagName === 'A') {
            parent.setAttribute('data-original-href', parent.href);
            parent.href = proxyUrl;
        }
    }
    tmpObj.src = originalSrc;
}

// 页面加载完成后执行检查
window.addEventListener('load', checkResourceAccess);

// 动态加载的内容处理
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            checkResourceAccess();
        }
    });
});

// 监听DOM变化
observer.observe(document.body, {
    childList: true,
    subtree: true
});
</script>

</body>
</html>