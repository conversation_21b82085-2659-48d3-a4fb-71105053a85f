<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\TmplService;
use app\common\Playful;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

/*
 * 用户认证
 */

class Depend extends Base
{
    /*
     * 认证表单
     */
    public function provision()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $list = Db::name('attest')
            ->where('at_name', 'like', "%{$hazy_name}%")
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->order(['scores' => 'asc', 'id' => 'asc'])
            ->field('custom_form,introduction', true)
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 表单排序
     */
    public function provisionSort()
    {
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $scores = request()->post('dalue');
            Db::startTrans();
            try {
                Db::name('attest')->where('id', $syid)->where('much_id', $this->much_id)->update(['scores' => $scores]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '排序成功']);
        }
    }

    /*
     * 新增表单
     */
    public function muProvision()
    {
        if (request()->isPost() && request()->isAjax()) {
            $acid = intval(input('post.acid', 0));
            $atInfo = Db::name('attest')->where('id', $acid)->where('much_id', $this->much_id)->count();
            $decent = input('post.decent/a');
            $form = input('post.form/a');
            $data['at_name'] = trim($form['atName']);
            $data['at_icon'] = trim($form['atIcon']);
            $data['handsel_day'] = intval($form['handselDay']);
            $data['custom_form'] = json_encode([$decent, $form['customForm']], 320);
            $data['introduction'] = $this->safe_html($form['introduction']);
            $data['status'] = intval($form['status']);
            $data['scores'] = intval($form['scores']);
            if ($atInfo === 0) {
                $data['create_time'] = time();
                $data['is_del'] = 0;
                $data['much_id'] = $this->much_id;
            }
            Db::startTrans();
            try {
                if ($atInfo === 0) {
                    Db::name('attest')->insert($data);
                } else {
                    Db::name('attest')->where('id', $acid)->where('much_id', $this->much_id)->update($data);
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $acid = intval(input('get.acid', 0));
            $list = Db::name('attest')->where('id', $acid)->where('much_id', $this->much_id)->find();
            if ($list) {
                $list['custom_form'] = base64_encode(rawurlencode($list['custom_form']));
                $this->assign('list', $list);
                $this->assign('retrofit', 1);
            } else {
                $this->assign('retrofit', 0);
            }
            return $this->fetch();
        }
    }

    /*
     * 删除表单
     */
    public function delProvision()
    {
        if (request()->isPost() && request()->isAjax()) {
            $ecid = request()->post('ecid', 0);
            Db::startTrans();
            try {
                Db::name('attest')->where('id', $ecid)->where('much_id', $this->much_id)->update(['status' => 0, 'is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '删除成功']);
        }
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, array(), 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    /*
     * 认证列表
     */
    public function acquire()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $hazy_egon = intval(request()->get('egon', 0));
        $where = array();
        switch ($hazy_egon) {
            case 1:
                $where['uat.adopt_status'] = 0;
                break;
            case 2:
                $where['uat.adopt_status'] = 1;
                break;
            case 3:
                $where['uat.adopt_status'] = 2;
                break;
            default:
                $where = array();
        }
        $list = Db::name('user_attest')
            ->alias('uat')
            ->join('attest at', 'uat.at_id = at.id', 'left')
            ->join('user us', 'uat.user_id = us.id', 'left')
            ->where('at.at_name|us.user_nick_name|us.user_wechat_open_id', 'like', "%{$hazy_name}%")
            ->where($where)
            ->where('uat.much_id&at.much_id&us.much_id', $this->much_id)
            ->orderRaw('uat.adopt_status <> 0 asc,case when uat.adopt_status = 0 then uat.id end asc,case when  uat.adopt_status <> 0 then uat.id end desc')
            ->field('uat.id,uat.at_id,uat.user_id,uat.adopt_status,uat.refer_time,at.at_name,at.handsel_day,us.user_head_sculpture,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 认证详情
     */
    public function acquireInfo()
    {
        $acid = intval(input('get.acid', 0));
        $uatInfo = Db::name('user_attest')->where('id', $acid)->where('much_id', $this->much_id)->find();
        $uatInfo['postback_data'] = json_decode($uatInfo['postback_data'], true);
        $this->assign('list', $uatInfo);
        return $this->fetch();
    }

    /*
     * 认证审核
     */
    public function trialAcquire()
    {
        if (request()->isPost() && request()->isAjax()) {
            $acid = intval(input('post.acid', 0));
            $uatInfo = Db::name('user_attest')->alias('uat')->join('attest at', 'uat.at_id = at.id', 'left')->where('uat.id', $acid)->where('uat.much_id&at.much_id', $this->much_id)->field('at.at_name,at.handsel_day,uat.user_id,uat.ut_inject,uat.adopt_status')->find();
            //  判断审核状态
            if (intval($uatInfo['adopt_status']) !== 0) {
                return json(['code' => 0, 'msg' => '系统繁忙，请刷新页面后重试！']);
            }
            $uatInfo['handsel_day'] = intval($uatInfo['handsel_day']);
            $data['adopt_status'] = intval(input('post.process', 0));
            $data['ut_inject'] = trim(input('post.inject', ''));
            $data['refuse_time'] = time();
            Db::startTrans();
            try {
                //  审核通过 并且赠送会员时间大于0天
                if ($data['adopt_status'] === 1 && $uatInfo['handsel_day'] > 0) {
                    //  用户会员到期时间
                    $userVipEndTime = Db::name('user')->where('id', $uatInfo['user_id'])->where('much_id', $this->much_id)->value('vip_end_time');
                    //  获取当前时间戳
                    $nowTime = time();
                    //  如果用户没开通会员或用户的会员已过期则更新为当前时间
                    if ($userVipEndTime <= $nowTime) {
                        //  更新为当前时间
                        $userVipEndTime = $nowTime;
                    }
                    //  赠送会员时长
                    Db::name('user')->where('id', $uatInfo['user_id'])->where('much_id', $this->much_id)->update(['vip_end_time' => ($userVipEndTime + ($uatInfo['handsel_day'] * 86400))]);
                }
                //  认证审核信息
                Db::name('user_attest')->where('id', $acid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($data['adopt_status'] >= 1 && $data['adopt_status'] <= 2) {
                $marryingText = "审核未通过";
                if ($data['adopt_status'] === 1) {
                    $marryingText = "审核已通过";
                }
                $tmplData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0011',
                    'user_id' => $uatInfo['user_id'],
                    'page' => 'yl_welore/pages/user_smail/index',
                    'keyword1' => date('Y年m月d日 H:i:s', time()),
                    'keyword2' => "{$uatInfo['at_name']}",
                    'keyword3' => $marryingText,
                    'keyword4' => "{$uatInfo['ut_inject']}",
                ];
                $tmplService = new TmplService();
                $tmplService->add_template($tmplData);
                Db::name('user_smail')->insert(['user_id' => $uatInfo['user_id'], 'maring' => "您申请的：{{$uatInfo['at_name']}}，{$marryingText}！", 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 删除认证信息
     */
    public function acquireDel()
    {
        if (request()->isPost() && request()->isAjax()) {
            $ecid = request()->post('ecid');
            Db::startTrans();
            try {
                Db::name('user_attest')->where('id', $ecid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '删除成功']);
        } else {
            $this->error('参数错误', 'depend/acquire');
        }
    }
}