<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:96:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/quickly/floating_module.html";i:1756110566;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755608968;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>
    .tpl-portlet-components {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        padding: 24px;
        margin: 0;
    }

    /* 标题区域 */
    .portlet-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 16px;
        margin-bottom: 24px;
        border-bottom: 1px solid #eee;
    }

    .caption {
        font-size: 18px;
        color: #333;
        font-weight: 500;
        display: flex;
        align-items: center;
    }

    .caption .am-icon-cog {
        margin-right: 8px;
        color: #666;
    }

    /* 表单样式 */
    .am-form-horizontal .am-form-group {
        margin-bottom: 24px;
    }

    .am-form-horizontal .am-form-label {
        text-align: right;
        font-weight: 500;
        padding-top: 15px;
        font-size: 14px;
        color: #333;
        line-height: 16px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    .am-form-horizontal .am-form-group .am-u-sm-9 {
        padding-left: 20px;
    }

    .am-form-horizontal input[type='text'],
    .am-form-horizontal input[type='number'],
    .am-form-horizontal input[type='url'] {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 8px 12px;
        height: 40px;
        width: 100%;
        font-size: 14px;
        transition: border-color 0.3s;
        box-sizing: border-box;
    }

    .am-form-horizontal input:focus {
        border-color: #1890ff;
        outline: none;
        box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
    }

    /* 单选框样式 */
    .radio-group {
        display: flex;
        gap: 12px;
        align-items: center;
        padding-top: 8px;
    }

    .radio-item {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 10px 16px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        background: #fafafa;
        transition: all 0.3s ease;
        min-width: 60px;
        justify-content: center;
        height: 36px;
        box-sizing: border-box;
    }

    .radio-item:hover {
        border-color: #1890ff;
        background: #f0f8ff;
    }

    .radio-item input[type="radio"] {
        margin: 0 6px 0 0;
        padding: 0;
        width: 16px;
        height: 16px;
        accent-color: #1890ff;
        flex-shrink: 0;
        position: relative;
        top: 0;
    }

    .radio-item input[type="radio"]:checked {
        accent-color: #1890ff;
    }

    .radio-item span {
        cursor: pointer;
        font-size: 14px;
        color: #333;
        margin: 0;
        font-weight: 500;
        line-height: 16px;
        height: 16px;
        display: flex;
        align-items: center;
    }

    /* 选中状态的单选框项 */
    .radio-item:has(input:checked) {
        border-color: #1890ff;
        background: #e6f7ff;
        box-shadow: 0 2px 4px rgba(24,144,255,0.1);
    }

    .radio-item:has(input:checked) span {
        color: #1890ff;
    }

    /* 帮助文本 */
    .help-text {
        color: #666;
        font-size: 13px;
        margin-top: 6px;
        padding: 6px 0;
        line-height: 1.4;
    }

    /* 按钮样式 */
    .am-btn {
        padding: 8px 24px;
        font-size: 14px;
        border-radius: 4px;
        transition: all 0.3s;
        margin-right: 12px;
    }

    .am-btn-primary {
        background: #1890ff;
        color: white;
        border: 1px solid #1890ff;
        min-width: 100px;
    }

    .am-btn-primary:hover {
        background: #40a9ff;
        border-color: #40a9ff;
    }

    /* 错误信息 */
    .error-message {
        color: #ff4d4f;
        font-size: 13px;
        margin-top: 8px;
        padding: 8px 12px;
        background-color: #fff2f0;
        border: 1px solid #ffccc7;
        border-radius: 4px;
        display: flex;
        align-items: center;
    }

    .error-message::before {
        content: "⚠";
        margin-right: 6px;
        font-weight: bold;
    }

    .has-error input {
        border-color: #ff4d4f;
        box-shadow: 0 0 0 2px rgba(255,77,79,0.2);
    }

    /* 禁用状态 */
    .am-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* 图片预览 */
    .icon-preview {
        margin-top: 12px;
        width: 120px;
        height: 120px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        background: #fafafa;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        box-sizing: border-box;
    }

    .icon-preview img {
        max-width: 100px;
        max-height: 100px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        display: block;
    }

    .icon-preview .no-image {
        color: #999;
        font-size: 13px;
        text-align: center;
        line-height: 1.4;
    }

    /* 选择器样式 */
    .am-form-horizontal select {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 8px 12px;
        height: 40px;
        width: 100%;
        font-size: 14px;
        transition: border-color 0.3s;
        box-sizing: border-box;
        background: #fff;
    }

    .am-form-horizontal select:focus {
        border-color: #1890ff;
        outline: none;
        box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
    }

    /* 条件显示区域 */
    .conditional-field {
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .conditional-field.hidden {
        max-height: 0;
        margin: 0;
        padding: 0;
        opacity: 0;
    }
</style>

<div id="app" class="tpl-portlet-components" v-cloak>
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-code"></span> 首页悬浮图标配置
        </div>
    </div>
    
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-8 am-u-md-offset-2">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            是否开启
                        </label>
                        <div class="am-u-sm-9">
                            <div class="radio-group">
                                <label class="radio-item" for="enable_yes">
                                    <input type="radio" id="enable_yes" v-model="config.is_enable" :value="1">
                                    <span>是</span>
                                </label>
                                <label class="radio-item" for="enable_no">
                                    <input type="radio" id="enable_no" v-model="config.is_enable" :value="0">
                                    <span>否</span>
                                </label>
                            </div>
                            <div class="help-text">设置是否在首页显示悬浮图标</div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            图标地址
                        </label>
                        <div class="am-u-sm-9" :class="{'has-error': errors.icon_url}">
                            <input
                                type="url"
                                v-model="config.icon_url"
                                placeholder="请输入图标图片地址">
                            <div class="help-text">悬浮图标的图片地址，推荐使用200x200px的正方形图片</div>
                            <div v-if="errors.icon_url" class="error-message">{{errors.icon_url}}</div>
                            <div style="display: flex; align-items: center; gap: 15px; margin-top: 12px;">
                                <div class="icon-preview" @click="openImageDialog" style="cursor: pointer;">
                                    <img v-if="config.icon_url" :src="config.icon_url" alt="图标预览" @error="handleImageError">
                                    <div v-else class="no-image">点击选择图片</div>
                                </div>
                                <button type="button" @click="openImageDialog" style="font-size: 12px; padding: 4px 12px; border: 1px solid #d9d9d9; background: #fff; border-radius: 4px; cursor: pointer;">选择图片</button>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            图标位置
                        </label>
                        <div class="am-u-sm-9">
                            <div class="radio-group">
                                <label class="radio-item" for="position_left">
                                    <input type="radio" id="position_left" v-model="config.position_fixed" :value="0">
                                    <span>左侧</span>
                                </label>
                                <label class="radio-item" for="position_right">
                                    <input type="radio" id="position_right" v-model="config.position_fixed" :value="1">
                                    <span>右侧</span>
                                </label>
                            </div>
                            <div class="help-text">设置悬浮图标在屏幕左侧还是右侧显示</div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            底部偏移
                        </label>
                        <div class="am-u-sm-9" :class="{'has-error': errors.bottom_offset}">
                            <input 
                                type="number" 
                                v-model="config.bottom_offset" 
                                placeholder="请输入距离底部的偏移距离"
                                min="0"
                                max="9999">
                            <div class="help-text">设置悬浮图标距离屏幕底部的距离，单位为像素(px)，范围0-9999</div>
                            <div v-if="errors.bottom_offset" class="error-message">{{errors.bottom_offset}}</div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            跳转类型
                        </label>
                        <div class="am-u-sm-9">
                            <select v-model="config.target_type">
                                <option value="0">客服会话</option>
                                <option value="1">内部链接</option>
                                <option value="2">外部链接</option>
                                <option value="3">小程序跳转</option>
                            </select>
                            <div class="help-text">设置点击悬浮图标后的跳转行为</div>
                        </div>
                    </div>

                    <div class="am-form-group conditional-field" :class="{'hidden': config.target_type != 1 && config.target_type != 2}">
                        <label class="am-u-sm-3 am-form-label">
                            跳转链接
                        </label>
                        <div class="am-u-sm-9" :class="{'has-error': errors.target_path}">
                            <input
                                type="url"
                                v-model="config.target_path"
                                placeholder="请输入跳转链接地址">
                            <div class="help-text">
                                <span v-if="config.target_type == 1">内部链接：填写小程序内部页面路径，如：/pages/index/index</span>
                                <span v-if="config.target_type == 2">外部链接：填写完整的网址，如：https://www.example.com</span>
                            </div>
                            <div v-if="errors.target_path" class="error-message">{{errors.target_path}}</div>
                        </div>
                    </div>

                    <div class="am-form-group conditional-field" :class="{'hidden': config.target_type != 3}">
                        <label class="am-u-sm-3 am-form-label">
                            小程序AppID
                        </label>
                        <div class="am-u-sm-9" :class="{'has-error': errors.target_appid}">
                            <input
                                type="text"
                                v-model="config.target_appid"
                                placeholder="请输入小程序AppID">
                            <div class="help-text">要跳转的小程序的AppID</div>
                            <div v-if="errors.target_appid" class="error-message">{{errors.target_appid}}</div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <div class="am-u-sm-8 am-u-sm-offset-4">
                            <button
                                type="button"
                                class="am-btn am-btn-primary"
                                @click="saveConfig"
                                :disabled="isLoading">
                                {{isLoading ? '保存中...' : '保存配置'}}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<script src="./assets/js/base-enhanced.js"></script>
<script>
var baseConfig = {
    urls: {
        proxyBase: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        proxyResource: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        repairMissing: "<?php echo url('index/repairMissing'); ?>",
        purgeCache: "<?php echo url('index/purgeCache'); ?>",
        ordinary: "<?php echo url('ordinary'); ?>",
        receipt: "<?php echo url('receipt'); ?>"
    },
    menuActive: '<?php echo $menuActive; ?>',
    menuList: '<?php echo $menuList; ?>',
    much_role: '<?php echo $much_role; ?>',
    notice: <?php echo $notice; ?>,
    vacant: <?php echo $vacant; ?>
};
BaseEnhanced.init(baseConfig);
window.retakeCache = BaseEnhanced.retakeCache;
window.getOriginalSrc = BaseEnhanced.getOriginalSrc;
</script>
<?php if($much_role=='founder'): ?>
<script>
window.updateFix = function() {
    layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
        btn: ['确定', '取消'],
        title: '提示'
    }, function() {
        // 执行修复操作
        performDatabaseRepair();
    }, function(index) {
        // 取消操作
        layer.close(index);
    });
};

function performDatabaseRepair() {
    var loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });
    $.post("<?php echo url('index/repairMissing'); ?>", function(data) {
        layer.close(loadingIndex);
        if (data !== false) {
            layer.msg('数据库缺失字段修复完成', {
                icon: 1,
                time: 1500
            }, function() {
                location.reload();
            });
        } else {
            layer.msg('修复失败，请稍后重试', {
                icon: 2,
                time: 2000
            }, function() {
                location.reload();
            });
        }
    }).error(function() {
        layer.close(loadingIndex);
        layer.msg('网络错误，修复失败', {
            icon: 2,
            time: 2000
        });
    });
}
</script>
<?php endif; ?>

<script>
    var vm = new Vue({
        el: '#app',
        data() {
            return {
                config: {
                    is_enable: parseInt('<?php echo (isset($config['is_enable']) && ($config['is_enable'] !== '')?$config['is_enable']:0); ?>'),
                    icon_url: '<?php echo htmlentities((isset($config['icon_url']) && ($config['icon_url'] !== '')?$config['icon_url']:"")); ?>',
                    position_fixed: parseInt('<?php echo (isset($config['position_fixed']) && ($config['position_fixed'] !== '')?$config['position_fixed']:1); ?>'),
                    bottom_offset: parseInt('<?php echo (isset($config['bottom_offset']) && ($config['bottom_offset'] !== '')?$config['bottom_offset']:200); ?>'),
                    target_type: parseInt('<?php echo (isset($config['target_type']) && ($config['target_type'] !== '')?$config['target_type']:0); ?>'),
                    target_path: '<?php echo htmlentities((isset($config['target_path']) && ($config['target_path'] !== '')?$config['target_path']:"")); ?>',
                    target_appid: '<?php echo htmlentities((isset($config['target_appid']) && ($config['target_appid'] !== '')?$config['target_appid']:"")); ?>'
                },
                isLoading: false,
                errors: {
                    icon_url: '',
                    bottom_offset: '',
                    target_path: '',
                    target_appid: ''
                }
            }
        },
        methods: {
            // 清除错误信息
            clearErrors: function() {
                this.errors = {
                    icon_url: '',
                    bottom_offset: '',
                    target_path: '',
                    target_appid: ''
                };
            },

            // 图片加载错误处理
            handleImageError: function(event) {
                event.target.style.display = 'none';
            },

            // 打开图片选择对话框
            openImageDialog: function() {
                layer.open({
                    type: 2,
                    anim: 2,
                    scrollbar: true,
                    area: ['900px', '600px'],
                    title: false,
                    closeBtn: 0,
                    shadeClose: true,
                    content: ["<?php echo url('images/dialogImages'); ?>&gclasid=0", 'no']
                });
            },

            // 表单验证
            validateForm: function() {
                this.clearErrors();
                let isValid = true;

                // 验证底部偏移距离
                const offset = parseInt(this.config.bottom_offset);
                if (isNaN(offset) || offset < 0) {
                    this.errors.bottom_offset = '底部偏移距离不能小于0';
                    isValid = false;
                } else if (offset > 9999) {
                    this.errors.bottom_offset = '底部偏移距离不能超过9999px';
                    isValid = false;
                }

                // 验证跳转链接（内部链接或外部链接时）
                if (this.config.target_type == 1 || this.config.target_type == 2) {
                    if (!this.config.target_path || this.config.target_path.trim() === '') {
                        this.errors.target_path = '跳转链接不能为空';
                        isValid = false;
                    } else if (this.config.target_type == 2) {
                        // 外部链接需要验证URL格式（支持http和https）
                        const urlPattern = /^https?:\/\/[^\s]+$/;
                        if (!urlPattern.test(this.config.target_path)) {
                            this.errors.target_path = '外部链接格式不正确，请输入完整的http://或https://地址';
                            isValid = false;
                        }
                    }
                }

                return isValid;
            },

            // 保存配置
            saveConfig: function () {
                if (!this.validateForm()) {
                    return;
                }

                if (this.isLoading) {
                    return;
                }

                this.isLoading = true;

                $.post("<?php echo url('quickly/floating_module'); ?>", {
                    is_enable: this.config.is_enable,
                    icon_url: this.config.icon_url,
                    position_fixed: this.config.position_fixed,
                    bottom_offset: this.config.bottom_offset,
                    target_type: this.config.target_type,
                    target_path: this.config.target_path,
                    target_appid: this.config.target_appid
                }, (data) => {
                    this.isLoading = false;
                    if (data.code > 0) {
                        layer.msg(data.msg, { icon: 1, time: 1500 }, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, { icon: 5, time: 2500 });
                    }
                }, 'json').fail(() => {
                    this.isLoading = false;
                    layer.msg('网络错误，请稍后重试', {icon: 5, time: 2500});
                });
            }
        }
    });

    // 全局函数，用于图片选择回调
    function sutake(eurl) {
        vm.config.icon_url = eurl;
        layer.closeAll();
    }
</script>

</body>
</html>