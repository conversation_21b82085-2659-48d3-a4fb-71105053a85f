<?php

namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

use think\Db;

class FluxibleInfo
{
    /*
     * 向接口提交post数据
     */
    public static function SendDetectPost($data, $muchId)
    {

        //签到：type=0,认证：type=1，会员：type=2，
        // 本地文件路径
        $filePath = EXTEND_PATH . 'Surprising' . DS . 'technical_' . $muchId . '.txt';
        // 判断本地文件是否存在
        if (file_exists($filePath)) {
            if ($data['type'] == 0) {
                $data = self::GetUserPunch($data, $muchId);
            }
            self::SetExternalSql($data, $muchId);
            //  读取文件中的url
            $url = @file_get_contents($filePath);
            //  发送post请求
            return Gyration::_requestPost(trim($url), $data);
        }
        return array();
    }

    public static function SetExternalSql($data, $muchId)
    {
        $filePath = EXTEND_PATH . 'Surprising' . DS . 'ExternalData_' . $muchId . '.php';
        if (!file_exists($filePath)) {
            return 0;
        }
        $db = require_once $filePath;
        if ($db['switch'] == 1) {
            //链接远程数据库
            $ext = Db::connect($db['db']);
            if ($data['type'] == 1) {
                $extList = $ext->table('share_user')->select();
                $user_attest = Db::name('user_attest')->where('id', $data['attest_id'])->where('user_id', $data['user_id'])->where('much_id', $muchId)->find();
                $attest = Db::name('attest')->where('id', $user_attest['at_id'])->where('much_id', $muchId)->find();
                $user_info = Db::name('user')->where('id', $data['user_id'])->where('much_id', $muchId)->find();
                //认证数据处于未审核状态
                if ($user_attest['adopt_status'] == 0) {
                    $json = json_decode($user_attest['postback_data'], true);
                    if (is_array($extList)) {
                    foreach ($extList as $k => $v) {
                        //相等更新提交认证
                        if ((strcasecmp($v['deviceCode'], $json[0]['value']) == 0) && (strcasecmp($v['serverPort'], $json[1]['value']) == 0)) {
                            $up = Db::name('user_attest')->where('id', $data['attest_id'])->where('user_id', $data['user_id'])->where('much_id', $muchId)->update(['adopt_status' => 1, 'refuse_time' => time()]);
                            //如果认证成功赠送会员
                            if ($up !== false) {
                                if ($attest['handsel_day'] > 0) {
                                    $time = 86400 * $attest['handsel_day'];
                                    //免费版
                                    $extData['name'] = $user_info['user_nick_name'];
                                    $extData['phone'] = $user_info['user_phone'];
                                    if ($v['platform'] == 'Base') {
                                        $new_time = bcadd(time(), $time);
                                        $extData['platform'] = 'Pro';
                                    } else {
                                        $new_time = strtotime($v['endTime']);
                                        $new_time = bcadd($new_time, $time);
                                    }
                                    $extData['endTime'] = date('Y-m-d H:i:s', $new_time);
                                    $extData['company'] = $data['user_id'] . '|' . $muchId;
                                    //更新远程数据库
                                    $ext->table('share_user')->where('id', $v['id'])->update($extData);
                                    //更新小程序用户会员
                                    $nowTime = time();
                                    $userVipEndTime = $user_info['vip_end_time'];
                                    //  如果用户没开通会员或用户的会员已过期则更新为当前时间
                                    if ($user_info['vip_end_time'] <= $nowTime) {
                                        //  更新为当前时间
                                        $userVipEndTime = $nowTime;
                                    }
                                    $UpTime = bcadd($userVipEndTime, $time);
                                    //  赠送会员时长
                                    Db::name('user')->where('id', $data['user_id'])->where('much_id', $muchId)->update(['vip_end_time' => $UpTime]);
                                }
                            }
                        }
                    }
                    }
                }
            }
            if ($data['type'] == 0 && $db['sign'] == 1) {
                //签到送会员
                $company = $data['user_id'] . '|' . $muchId;
                $extInfo = $ext->table('share_user')->where('company', $company)->find();
                if (!empty($extInfo)) {
                    if ($extInfo['platform'] == 'Pro') {
                        $time = strtotime($extInfo['endTime']);
                        $time = bcadd($time, 86400);
                        $time = date('Y-m-d H:i:s', $time);
                    } else {
                        $time = bcadd(time(), 86400);
                        $time = date('Y-m-d H:i:s', $time);
                    }
                    $extData['platform'] = 'Pro';
                    $extData['endTime'] = $time;
                    $ext->table('share_user')->where('id', $extInfo['id'])->update($extData);
                }
            }
            //同步数据
            if ($data['type'] == 3) {
                $company = $data['user_id'] . '|' . $muchId;
                $extInfo = $ext->table('share_user')->where('company', $company)->find();
                $user_info = Db::name('user')->where('id', $data['user_id'])->where('much_id', $muchId)->find();
                if (!empty($extInfo)) {
                    if ($user_info['vip_end_time'] > time()) {
                        $time = date('Y-m-d H:i:s', $user_info['vip_end_time']);
                        $extData['platform'] = 'Pro';
                    } else {
                        $time = date('Y-m-d H:i:s', '4102415999');
                        $extData['platform'] = 'Base';
                    }
                    $extData['endTime'] = $time;
                    if ($extInfo['platform'] == 'Pro') {
                        if ($user_info['vip_end_time'] > strtotime($extInfo['endTime'])) {
                            $ext->table('share_user')->where('id', $extInfo['id'])->update($extData);
                        }
                    } else {
                        if ($user_info['vip_end_time'] > time()) {
                            $ext->table('share_user')->where('id', $extInfo['id'])->update($extData);
                        }
                    }

                }
            }
        }

    }

    /**
     * 查询用户总签到次数
     */
    public static function GetUserPunch($data, $muchId)
    {
        $count = Db::name('user_punch')->where('user_id', $data['user_id'])->where('much_id', $muchId)->count();
        $data['punch_count'] = $count;
        return $data;
    }
}