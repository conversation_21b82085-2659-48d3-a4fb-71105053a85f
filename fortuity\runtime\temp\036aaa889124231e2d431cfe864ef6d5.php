<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:91:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/marketing/fabulous.html";i:1751623548;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755608968;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-diamond {margin-right: 5px;color: #23b7e5;}
    .am-form-horizontal {background: #fff;border-radius: 6px;padding: 20px;box-shadow: 0 1px 3px rgba(0,0,0,0.05);}
    .am-form-group {margin-bottom: 20px;padding: 15px 0;border-bottom: 1px solid #f5f5f5;}
    .am-form-group:last-child {border-bottom: none;}
    .am-form-label {font-weight: 500;color: #333;line-height: 1.6;}
    .am-form-group input[type="text"], .am-form-group input[type="number"] {height: 36px;padding: 8px 12px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;font-size: 14px;}
    .am-form-group input[type="text"]:focus, .am-form-group input[type="number"]:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .am-form-group small {color: #666;font-size: 12px;margin-top: 5px;display: block;line-height: 1.4;}
    .radio-group {display: flex;gap: 20px;align-items: center;}
    .radio-item {display: flex;align-items: center;gap: 6px;cursor: pointer;padding: 8px 12px;border-radius: 4px;transition: all 0.3s;}
    .radio-item:hover {background-color: #f8f9fa;}
    .radio-item input[type="radio"] {margin: 0;width: 16px;height: 16px;}
    .warning-text {color: #dc3545;font-weight: 500;}
    .info-text {font-weight: 600;color: #333;}
    .currency-label {color: #666;font-weight: normal;font-size: 14px;margin-left: 8px;}
    .custom-price-container {border: 2px solid #e8e8e8;border-radius: 8px;padding: 50px 20px 20px 20px;margin: 20px 0;position: relative;background: #f8f9fa;}
    .add-btn {position: absolute;top: 10px;right: 15px;background: linear-gradient(135deg, #28a745 0%, #20c997 100%);border: none;color: white;padding: 8px 12px;border-radius: 6px;font-size: 12px;cursor: pointer;transition: all 0.3s ease;box-shadow: 0 2px 4px rgba(40,167,69,0.2);}
    .add-btn:hover {background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);box-shadow: 0 4px 8px rgba(40,167,69,0.3);transform: translateY(-1px);}
    .price-item {background: #fff;border-radius: 6px;padding: 15px;margin-bottom: 15px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);position: relative;}
    .price-item:last-child {margin-bottom: 0;}
    .delete-btn {position: absolute;top: 10px;right: 15px;color: #dc3545;font-size: 20px;cursor: pointer;transition: all 0.3s;}
    .delete-btn:hover {color: #c82333;transform: scale(1.1);}
    .price-form-row {display: flex;align-items: center;gap: 20px;flex-wrap: wrap;}
    .price-form-item {display: flex;align-items: center;gap: 8px;flex: 1;min-width: 200px;}
    .price-form-item label {font-size: 12px;white-space: nowrap;min-width: 80px;}
    .price-form-item input {flex: 1;min-width: 100px;}
    .save-btn {background: linear-gradient(135deg, #23b7e5 0%, #1a9bc0 100%);border: none;color: white;padding: 12px 30px;border-radius: 6px;font-size: 14px;font-weight: 500;cursor: pointer;transition: all 0.3s ease;box-shadow: 0 2px 4px rgba(35,183,229,0.2);}
    .save-btn:hover {background: linear-gradient(135deg, #1a9bc0 0%, #1587a8 100%);box-shadow: 0 4px 8px rgba(35,183,229,0.3);transform: translateY(-1px);}
    .save-btn:active {transform: translateY(0);box-shadow: 0 2px 4px rgba(35,183,229,0.2);}
</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-diamond"></span> 会员设置
        </div>
    </div>
    <div class="tpl-block" style="margin: 50px 0 50px 0;">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-9 am-u-sm-push-1">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">价格类型</label>
                        <div class="am-u-sm-9">
                            <div class="radio-group">
                                <label class="radio-item">
                                    <input type="radio" v-model="list.chop_type" value="0">
                                    <span>系统生成</span>
                                </label>
                                <label class="radio-item">
                                    <input type="radio" v-model="list.chop_type" value="1">
                                    <span>自定义设置</span>
                                </label>
                            </div>
                            <small class="warning-text">注意事项 : 选择价格类型后会重置已设置的数据</small>
                            <small class="info-text">如需在已保存的数据上修改，刷新页面即可重新获取数据</small>
                        </div>
                    </div>

                    <template v-if="list.chop_type == 0">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">会员价格</label>
                            <div class="am-u-sm-5 am-u-end">
                                <input type="number" v-model="list.hono_price" placeholder="请输入会员价格" @input="list.hono_price = Number(list.hono_price.match(/^\d*(\.?\d{0,2})/g)[0] || 0)">
                            </div>
                            <span class="currency-label">单价 / 月 ( {{touch.currency}} )</span>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">是否开启首次折扣</label>
                            <div class="am-u-sm-9">
                                <div class="radio-group">
                                    <label class="radio-item">
                                        <input type="radio" v-model="list.first_discount" value="1">
                                        <span>开启</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" v-model="list.first_discount" value="0">
                                        <span>关闭</span>
                                    </label>
                                </div>
                                <small>开启后用户首次开通会员将享受折扣优惠</small>
                            </div>
                        </div>

                        <div class="am-form-group" v-if="list.first_discount == 1">
                            <label class="am-u-sm-3 am-form-label">首次折扣比例</label>
                            <div class="am-u-sm-5">
                                <input type="text" v-model="list.discount_scale" placeholder="请输入首次折扣比例" @input="list.discount_scale = list.discount_scale.toString().replace(/\D/g,'')">
                                <small>
                                    用户开通会员享受折扣率 例如 85% <br>
                                    [ 实付价格 = 会员价格 * 85% ( <span class="warning-text">所得结果将保留小数点后一位，其余四舍五入</span>) ]
                                </small>
                            </div>
                            <span class="currency-label">%</span>
                        </div>
                    </template>
                    <template v-if="list.chop_type == 1">
                        <div class="custom-price-container">
                            <button class="add-btn" @click="addDelimit();" title="添加一条自定义数据">
                                <span class="am-icon-plus"></span> 添加套餐
                            </button>
                            <div class="price-item" v-for="(item,index) in list.define_price" :key="index">
                                <div class="price-form-row">
                                    <div class="price-form-item">
                                        <label>开通天数</label>
                                        <input type="text" v-model="item.time" placeholder="天数" @input="item.time = item.time.toString().replace(/\D/g,'')">
                                    </div>
                                    <div class="price-form-item">
                                        <label>价格 ( {{touch.currency}} )</label>
                                        <input type="number" v-model="item.price" placeholder="价格" @input="item.price = Number(item.price.match(/^\d*(\.?\d{0,2})/g)[0] || 0)">
                                    </div>
                                    <div class="price-form-item">
                                        <label>排序</label>
                                        <input type="text" v-model="item.sort" placeholder="排序" @input="item.sort = item.sort.toString().replace(/\D/g,'')">
                                    </div>
                                </div>
                                <span class="delete-btn" v-if="index != 0" @click="removeDelimit(index);" title="删除这条数据">×</span>
                            </div>
                        </div>
                    </template>
                    <div class="am-form-group" style="display: flex;justify-content: center;border-bottom: none;">
                        <button type="button" class="save-btn" @click="holdSave();">
                            <span class="am-icon-save"></span> 保存配置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<script src="./assets/js/base-enhanced.js"></script>
<script>
var baseConfig = {
    urls: {
        proxyBase: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        proxyResource: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        repairMissing: "<?php echo url('index/repairMissing'); ?>",
        purgeCache: "<?php echo url('index/purgeCache'); ?>",
        ordinary: "<?php echo url('ordinary'); ?>",
        receipt: "<?php echo url('receipt'); ?>"
    },
    menuActive: '<?php echo $menuActive; ?>',
    menuList: '<?php echo $menuList; ?>',
    much_role: '<?php echo $much_role; ?>',
    notice: <?php echo $notice; ?>,
    vacant: <?php echo $vacant; ?>
};
BaseEnhanced.init(baseConfig);
window.retakeCache = BaseEnhanced.retakeCache;
window.getOriginalSrc = BaseEnhanced.getOriginalSrc;
</script>
<?php if($much_role=='founder'): ?>
<script>
window.updateFix = function() {
    layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
        btn: ['确定', '取消'],
        title: '提示'
    }, function() {
        // 执行修复操作
        performDatabaseRepair();
    }, function(index) {
        // 取消操作
        layer.close(index);
    });
};

function performDatabaseRepair() {
    var loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });
    $.post("<?php echo url('index/repairMissing'); ?>", function(data) {
        layer.close(loadingIndex);
        if (data !== false) {
            layer.msg('数据库缺失字段修复完成', {
                icon: 1,
                time: 1500
            }, function() {
                location.reload();
            });
        } else {
            layer.msg('修复失败，请稍后重试', {
                icon: 2,
                time: 2000
            }, function() {
                location.reload();
            });
        }
    }).error(function() {
        layer.close(loadingIndex);
        layer.msg('网络错误，修复失败', {
            icon: 2,
            time: 2000
        });
    });
}
</script>
<?php endif; ?>

<script>
    var vm = new Vue({
        el: "#app",
        data: {
            touch: [],
            list: []
        },
        created() {
            $.ajax({
                type: 'post',
                async: false,
                url: "<?php echo url('marketing/fabulous'); ?>",
                data: {'getData': true},
                dataType: 'json',
                success: data => {
                    this.touch = data[0];
                    if (data[1].define_price == null) {
                        data[1].define_price = '{"0":{"time":"0","price":"0","sort":"0"}}';
                    }
                    data[1].define_price = eval('(' + data[1].define_price + ')');
                    this.list = data[1];
                }
            });
        },
        methods: {
            addDelimit() {
                var deIndex = 0;
                Object.keys(this.list.define_price).forEach((key, index) => {
                    if (deIndex < parseInt(key)) {
                        deIndex = parseInt(key);
                    }
                });
                this.$set(this.list.define_price, deIndex + 1, {time: 0, price: 0, sort: 0});
            },
            removeDelimit(index) {
                index = parseInt(index);
                var keyIndex = Object.keys(this.list.define_price).length - 1 - index;
                if (keyIndex != 0) {
                    for (var i = 0; i <= keyIndex; i++) {
                        this.list.define_price[index + i] = this.list.define_price[index + i + 1];
                    }
                    this.$delete(this.list.define_price, Object.keys(this.list.define_price).length - 1);
                } else {
                    this.$delete(this.list.define_price, index);
                }
            },
            dataSort() {
                for (var i = 0; i < Object.keys(this.list.define_price).length; i++) {
                    for (var j = 0; j < Object.keys(this.list.define_price).length - i - 1; j++) {
                        if (parseInt(this.list.define_price[j].sort) > parseInt(this.list.define_price[j + 1].sort)) {
                            var tempData = {};
                            tempData = this.list.define_price[j];
                            this.list.define_price[j] = this.list.define_price[j + 1];
                            this.list.define_price[j + 1] = tempData;
                        }
                    }
                }
            },
            holdSave() {
                switch (parseInt(this.list.chop_type)) {
                    case 0:
                        if (parseFloat(this.list.hono_price) == 0 || parseInt(this.list.discount_scale) == 0) {
                            layer.msg('会员价格 或 首次折扣比例 不能为零');
                            return;
                        }
                        if (parseInt(this.list.discount_scale) > 100) {
                            layer.msg('首次折扣比例数值不能大于100');
                            return;
                        }
                        break;
                    case 1:
                        var checkPrice = false;
                        Object.values(this.list.define_price).forEach((items, index) => {
                            if (parseFloat(items.price) == 0 || parseInt(items.time) == 0) {
                                checkPrice = true;
                                return;
                            }
                        });
                        if (checkPrice) {
                            layer.msg('自定义 会员价格 或 开通天数 不能为零');
                            return;
                        }
                        this.dataSort();
                        break;
                }
                $.post("<?php echo url('marketing/fabulous'); ?>", this.list, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
            }
        }, mounted(){
            this.$watch('list.chop_type', (newValue, oldValue) => {
                switch (parseInt(newValue)) {
                    case 0:
                        this.list.define_price = eval('(' + '{"0":{"time":"0","price":"0","sort":"0"}}' + ')');
                        break;
                    case 1:
                        this.list.hono_price = 29.9;
                        this.list.first_discount = 0;
                        this.list.discount_scale = 100;
                        break;
                }
            });
            this.$watch('list.first_discount', (newValue, oldValue) => {
                if (newValue == 0) {
                    this.list.discount_scale = 100;
                }
            });
        }
    });
</script>

</body>
</html>