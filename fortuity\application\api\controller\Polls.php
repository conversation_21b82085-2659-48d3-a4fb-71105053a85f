<?php


namespace app\api\controller;


use app\api\service\Alternative;
use app\api\service\TmplService;
use app\api\service\Util;
use think\Db;

class Polls extends Base
{
    /**
     * 投票
     */
    public function vote_do()
    {
        $data = input('param.');
        if (empty($data['vo_id'])) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '请选择某个选项！']);
        }
        $paper = Db::name('paper')->where('id', $data['paper_id'])->where('much_id', $data['much_id'])->find();
        //检测是否投过
        $check = Db::name('user_vote')->where('user_id', $this->user_info['id'])->where('paper_id', $data['paper_id'])->where('much_id', $data['much_id'])->find();
        if ($check) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '已经投过票了！']);
        }
        if ($paper['vote_deadline'] != 0 && $paper['vote_deadline'] <= time()) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '投票已截止！']);
        }
        if ($paper['study_status'] == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '帖子未审核！']);
        }
        if ($paper['study_status'] == 2) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '帖子未通过审核！']);
        }

        $item = json_decode($data['vo_id'], true);
        if (is_array($item)) {
            foreach ($item as $k => $v) {
                $item[$k]['user_id'] = $this->user_info['id'];
                $item[$k]['paper_id'] = $data['paper_id'];
                $item[$k]['decide_time'] = time();
                $item[$k]['much_id'] = $data['much_id'];
            }
        }
        $res = Db::name('user_vote')->insertAll($item);
        if (!$res) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '投票失败！']);
        }
        return $this->json_rewrite(['status' => 'success', 'msg' => '投票成功！']);
    }

    /**
     *  视频
     */
    public function get_full_video()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('ac21ba4f-a2d1-4134-92aa-76b400c6e366', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'status' => 'error', 'msg' => '应用未开通此插件！']);
        }
        $where = array();
        $util = new Util();
        $where_not_in = array();
        $page = $data['page'];
        $check_qq = 'no';
        if (isset($data['tory_id'])) {
            $where['p.tory_id'] = ['eq', $data['tory_id']];
            $where['p.topping_time'] = ['eq', 0];
            $check_qq = $util->check_qq($this->user_info['user_wechat_open_id'], $data['tory_id']);
        }
        if ($this->version == 1) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
            $where['p.is_buy'] = ['eq', 0];
        }
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($authority['hair_video_arbor'] == 0) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
        }

        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        //查询当前用户是否是VIP
        $user_vip = $util->get_user_vip($this->user_info['id']);

        //查询有权限的圈子
        $q_tory = Db::name('territory')->where('status', 1)->where('much_id', $data['much_id'])->select();
        $q_tory_id = '';
        $vip_tory_id = '';
        $level_tory_id = '';
        foreach ($q_tory as $k => $v) {
            if ($v['attention'] == 1) {
                $user_trailing = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->where('tory_id', $v['id'])->find();
                if (empty($user_trailing)) {
                    $q_tory_id .= $v['id'] . ",";
                }
            }
            if ($v['attention'] == 2) {
                $vip_tory_id .= $v['id'] . ",";
            }
            if ($v['visit_level'] > $this->user_info['level']) {
                $level_tory_id .= $v['id'] . ",";
            }

        }
        //return $level_tory_id;
        //权限
        $q_tory_id = substr($q_tory_id, 0, -1);
        //等级
        $level_tory_id = substr($level_tory_id, 0, -1);
        //vip
        $vip_tory_id = substr($vip_tory_id, 0, -1);


        if ($check_qq == 'no') {
            if ($user_vip == 0 && $vip_tory_id && $cg == 0) {
                $q_tory_id = $q_tory_id . ',' . $vip_tory_id;
            }
            if ($level_tory_id && $cg == 0) {
                $q_tory_id = $q_tory_id . ',' . $level_tory_id;
            }
            if ($cg == 0) {
                $where['t.id'] = array('not in', $q_tory_id);
            }
        }

        //查询被封禁的用户
        $user_status = Db::name('user')->where('much_id', $data['much_id'])->where('status', 0)->select();
        $user_status_id = '';
        foreach ($user_status as $k => $v) {
            $user_status_id .= $v['id'] . ",";
        }
        $user_status_id = substr($user_status_id, 0, -1);
        //$where['p.essence_time'] = ['eq', 0];
        $listRows = 2;
        if ($page > 1) {
            $listRows = 3;
        }
        if (empty($data['token'])) {
            $listRows = 1;
        }
        //数组记录ID
        $ids = '';
        //数组去重
        if (empty($data['ids'])) {
            if (empty($data['id'])) {
                //查询当前进入页面的ID那个帖子
                $rand_info = Db::name('paper')
                    ->alias('p')
                    ->join('user u', 'p.user_id=u.id')
                    ->join('territory t', 'p.tory_id=t.id')
                    ->where('p.whether_delete', '0')
                    ->where('p.study_status', '1')
                    ->where('p.much_id', $data['much_id'])
                    ->where('t.status', 1)
                    ->where('p.study_type', 2)
                    ->where('t.is_del', 0)
                    ->where('p.is_buy', 0)
                    ->orderRaw('rand()')
                    ->field('p.id')
                    ->find();
                $data['id'] = $rand_info['id'];
            }
            $ids = $data['id'];
        } else {
            $ids = substr($data['ids'], 0, -1);
        }
        //dump($ids);
        $list = Db::name('paper')
            ->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '1')
            ->where('p.much_id', $data['much_id'])
            ->where('t.status', 1)
            ->where('p.is_buy', 0)
            ->where('p.study_type', 2)
            ->where('t.is_del', 0)
            ->where($where)
            ->where('p.study_video', 'not null')
            ->where('p.id', 'not in', $ids)
            ->whereNotIn('p.user_id', $user_status_id)
            ->field('p.video_type,p.third_part_vid,p.uccid,u.user_wechat_open_id,p.study_video,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.is_open,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name')
            ->orderRaw('rand()')
            ->page($page, $listRows)
            ->select();
        //查询当前进入页面的ID那个帖子
        $paper_info = Db::name('paper')
            ->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '1')
            ->where('p.much_id', $data['much_id'])
            ->where('t.status', 1)
            ->where('p.study_type', 2)
            ->where('t.is_del', 0)
            ->where('p.id', $data['id'])
            ->field('p.video_type,p.third_part_vid,p.uccid,u.user_wechat_open_id,p.study_video,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.is_open,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name')
            ->find();
        if (!empty($paper_info) && $listRows == 2) {
            array_unshift($list, $paper_info);
            $ids = '';
        }
        if (!empty($data['ids'])) {
            $ids = substr($data['ids'], 0, -1);
        }
        if ($list) {
            foreach ($list as $k => $v) {
                $ids .= $v['id'] . ',';
                if (isset($data['tory_id'])) {//判断是否是 圈主或者管理员
                    $list[$k]['check_qq'] = $util->check_qq($v['user_wechat_open_id'], $v['tory_id']);
                }
                //$count = Db::name('user_collect')->where('paper_id', $v['id'])->count();
                //$list[$k]['info_sc_count'] = formatNumber($count);
                $list[$k]['user_wechat_open_id'] = null;
                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);
                $list[$k]['image_part'] = json_decode($v['image_part']);
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);

                if (mb_strlen(strip_tags(emoji_decode($v['study_content'])), 'utf8') > 45) {
                    $list[$k]['is_len'] = 1;
                } else {
                    $list[$k]['is_len'] = 0;
                }
                $list[$k]['study_content'] = subtext(strip_tags(emoji_decode($v['study_content'])), 45);
                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = $util->get_paper_reply($v['id'], $data['much_id']);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
                $list[$k]['study_collect'] = $util->get_paper_collect($v['id'], $data['much_id']);
                $shoucang = Db::name('user_collect')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_study_collect'] = $shoucang == 0 ? false : true;
                $zan = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $zan == 0 ? false : true;
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $list[$k]['red'] = $red;
                //获取当前话题
                if ($v['tg_id'] != 0) {
                    $gambit = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $v['tg_id'])->find();
                    $list[$k]['gambit_name'] = emoji_decode(str_replace("#", '', $gambit['gambit_name']));
                    $list[$k]['gambit_id'] = $gambit['id'];
                }
                //当前用户昵称
                $list[$k]['special'] = $util->get_user_special_nickname($v['user_id'], $data['much_id']);
                //如果是腾讯视频
                if ($v['video_type'] == 1) {
                    $list[$k]['study_video'] = $this->getVideoInfo($v['third_part_vid']);
                }
                //如果是解析视频
                if ($v['video_type'] == 2) {
                    $vid = Alternative::GetAnalysis($v['third_part_vid'], $data['much_id'], 0);
                    if ($vid['code'] == 1) {
                        $list[$k]['study_video'] = $vid['video'];
                    }
//                    else {
//                        return $this->json_rewrite(['code' => 1, 'status' => 'error', 'msg' => '视频不见了！']);
//                    }
                }
                $list[$k]['url'] = $list[$k]['study_video'];
                $list[$k]['study_content'] = Alternative::ExpressionHtml($list[$k]['study_content']);
                //判断是否有身份铭牌
                if (intval($v['uccid']) != 0) {
                    //查询佩戴的身份
                    $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                    $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                    $list[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                    $list[$k]['user_id'] = 0;
                    //$list[$k]['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                    $list[$k]['user_nick_name'] = $card_info['forgery_name'];
                    $list[$k]['attr'] = '';
                } else {
                    $attr = $util->get_att_info($v['user_id'], $data['much_id']);
                    $list[$k]['attr'] = $attr;
                }

            }
            $rs['info'] = $list;
        } else {
            $rs['info'] = array();
        }
        $rs['version'] = $this->version;
        $rs['ids'] = $ids;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取视频评论
     */
    public function get_video_reply()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('ac21ba4f-a2d1-4134-92aa-76b400c6e366', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'status' => 'error', 'msg' => '应用未开通此插件！']);
        }
        $id = $data['id'];
        $page = $data['page'];
        $where = array();
        $where['r.reply_status'] = ['eq', 1];
        //获取帖子uid
        $t_uid = Db::name('paper')->where('id', $id)->where('much_id', $data['much_id'])->find();
        $pl = Db::name('paper_reply')->alias('r')
            ->join('user u', 'u.id=r.user_id')
            ->where('r.paper_id', $id)
            ->where('r.whether_delete', '0')
            ->where($where)
            ->where('r.whether_type', 0)
            ->field('r.uccid,r.id,r.paper_id,r.user_id,r.reply_type,r.phase,r.reply_content,r.image_part,r.reply_voice,r.reply_voice_time,r.apter_time,r.prove_time,r.praise,u.gender,u.wear_merit,u.level,u.user_nick_name,u.user_head_sculpture,u.user_wechat_open_id')
            ->order('r.praise desc,r.phase')
            ->page($page, '5')
            ->select();
        foreach ($pl as $k => $v) {
            $pl[$k]['yinchang'] = 1;
            $pl[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            $util = new Util();
            $pl[$k]['is_qq'] = $util->check_qq($v['user_wechat_open_id'], $util->get_user_applaud($v['id'])['tory_id']);
            $pl[$k]['user_wechat_open_id'] = null;
            $pl[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
            $pl[$k]['reply_content'] = emoji_decode($v['reply_content']);
            $pl[$k]['reply_content'] = Alternative::ExpressionHtml($pl[$k]['reply_content']);
            $pl[$k]['apter_time'] = formatTime($v['apter_time']);
            $pl[$k]['image_part'] = json_decode($v['image_part']);
            $pl[$k]['is_voice'] = false;
            $pl[$k]['is_paper_user'] = $util->get_page_user($id)['user_id'];
            $check_hf_zan = Db::name('user_applaud')->where('applaud_type', 1)->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->find();
            $pl[$k]['is_huifu_zan_count'] = formatNumber($v['praise']);
            if ($check_hf_zan > 0) {
                $pl[$k]['is_huifu_zan'] = true;
            } else {
                $pl[$k]['is_huifu_zan'] = false;
            }
            $pl[$k]['is_huifu_zan_count'] = formatNumber($pl[$k]['is_huifu_zan_count']);
            $hui_and_key = Db::name('paper_reply_duplex')->where('duplex_status', 1)
                ->where('whether_delete', 0)->where('reply_id', $v['id'])->where('duplex_status', 1)->count();
            $pl[$k]['huifu_count'] = $hui_and_key == 0 ? '' : formatNumber($hui_and_key);

            //评论回复
            $info_list = Db::name('paper_reply_duplex')->alias('r')
                ->join('user u', 'u.id=r.user_id')
                ->where('r.reply_id', $v['id'])
                ->where('r.duplex_status', 1)
                ->where('r.whether_delete', 0)
                ->count();

            //回复是否有红包
            $res = Db::name('user_red_packet')->where('reply_id', $v['id'])->where('much_id', $data['much_id'])->find();
            if ($res) {
                if ($res['obtain_fraction'] == 0) {
                    $pl[$k]['is_red_hui'] = $res['obtain_conch'];
                } else {
                    $pl[$k]['is_red_hui'] = $res['obtain_fraction'];
                }
            } else {
                $pl[$k]['is_red_hui'] = 0;
            }
            $pl[$k]['huifu_huifu_count'] = $info_list;
            $pl[$k]['level_info'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
            //当前用户的勋章
            $pl[$k]['wear_merit'] = $util->get_medal($v['wear_merit'], $data['much_id']);
            //当前用户昵称
            $pl[$k]['special'] = $util->get_user_special_nickname($v['user_id'], $data['much_id']);
            $pl[$k]['avatar_frame'] = $util->get_user_avatar_frame($v['user_id'], $data['much_id']);
            $pl[$k]['expandList'] = array();
            //判断是否有身份铭牌
            if (intval($v['uccid']) != 0) {
                //查询佩戴的身份
                $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                $pl[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                $pl[$k]['user_id'] = 0;
                //$pl[$k]['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                $pl[$k]['user_nick_name'] = $card_info['forgery_name'];
            } else {
                $pl[$k]['attest'] = $util->get_att_info($v['user_id'], $data['much_id']);
            }
            if ($t_uid['user_id'] == $v['user_id']) {
                if ($t_uid['uccid'] != 0) {
                    $pl[$k]['yinchang'] = 0;
                }
            }

        }

        $hui_count = Db::name('paper_reply')->where('reply_status', 1)->where('whether_delete', 0)->where('paper_id', $id)->count();
        $rs['huifu_count'] = formatNumber($hui_count);
        $rs['huifu'] = $pl;
        return $this->json_rewrite($rs);
    }

    /**
     * 展开回复
     */
    public function get_expand_list()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('ac21ba4f-a2d1-4134-92aa-76b400c6e366', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'status' => 'error', 'msg' => '应用未开通此插件！']);
        }
        //获取当前评论下的回复 使用分页
        $list = Db::name('paper_reply_duplex')
            ->where('much_id', $data['much_id'])
            ->where('whether_delete', 0)
            ->where('duplex_status', 1)
            ->where('reply_id', $data['id'])
            ->page($data['page'], 3)
            ->field('id,uccid,user_id,reply_id,re_uccid,reply_user_id,duplex_content,duplex_time')
            ->order('id')
            ->select();
        foreach ($list as $k => $v) {
            $list[$k]['duplex_time'] = formatTime($v['duplex_time']);
            $list[$k]['duplex_content'] = Alternative::ExpressionHtml(emoji_decode($v['duplex_content']));;
            //回复者用户详情
            $reply_user = Db::name('user')->where('id', $v['user_id'])->field('id,user_head_sculpture,user_nick_name')->find();
            $reply_user['user_nick_name'] = emoji_decode($reply_user['user_nick_name']);
            $list[$k]['reply_user'] = $reply_user;
            //回复的回复用户详情
            if ($v['reply_user_id'] != 0) {
                $reply_user_reply = Db::name('user')->where('id', $v['reply_user_id'])->field('id,user_head_sculpture,user_nick_name')->find();
                $reply_user_reply['user_nick_name'] = emoji_decode($reply_user_reply['user_nick_name']);
                $list[$k]['reply_user_reply'] = $reply_user_reply;
            }
        }
        return $this->json_rewrite($list);
    }

    /**
     * 点评
     */
    public function paper_review()
    {
        $data = input('param.');
        $util = new Util();
        $paper_info = $util->get_paper($data['paper_id']);
        $da_xiao = $util->check_qq($data['openid'], $paper_info['tory_id']);
        //查询是否自己点评自己
        if ($paper_info['user_id'] == $this->user_info['id']) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '这是您自己的帖子！']);
        }
        //查询设置
        $paper_review_config = Db::name('paper_review_config')->where('much_id', $data['much_id'])->find();
        //自动审核 0关闭 1开启
        $is_auto_audit = empty($paper_review_config) ? 0 : $paper_review_config['is_auto_audit'];
        //0圈主 1所有人
        $is_all_review = empty($paper_review_config) ? 0 : $paper_review_config['is_all_review'];
        //查询是否有权限
        if ($is_all_review == 0 && $da_xiao != 'da') {
            return $this->json_rewrite(['status' => 'error', 'msg' => '您没有权限！']);
        }
        //查询是否已经点评
        $check_repeat = Db::name('paper_review_score')
            ->where('pa_id', $data['paper_id'])
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if ($check_repeat) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '已点评过！']);
        }
        //查询是否为空
        if (empty($data['comment_text'])) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '点评内容不能为空！']);
        }
        //查询敏感文字
        $check_title = $util->get_check_msg($data['comment_text'], $data['much_id'], $data['openid']);
        if ($check_title['status'] == 'error') {
            return $this->json_rewrite($check_title);
        }
        //查询圈子所在广场
        $nedl = $util->get_territory_needle($paper_info['tory_id'], $data['much_id']);
        $item['ne_id'] = $nedl['id'];
        $item['tory_id'] = $paper_info['tory_id'];
        $item['pa_id'] = $data['paper_id'];
        $item['user_id'] = $this->user_info['id'];
        $item['assess_score'] = bcadd($data['star'], 1);
        $item['assess_content'] = emoji_encode($data['comment_text']);
        $item['assess_time'] = time();
        $item['is_show'] = $data['is_show'];
        $item['much_id'] = $data['much_id'];
        $item['audit_status'] = $is_auto_audit;
        $res = Db::name('paper_review_score')->insert($item);
        if (!$res) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '点评失败，请稍后重试！']);
        }
        Db::name('paper_review_common_terms')->insert(['user_id' => $this->user_info['id'], 'common_content' => $data['comment_text'], 'create_time' => time(), 'much_id' => $data['much_id']]);
        //发送模板消息
        $util->add_template(['much_id' => $data['much_id'],
            'at_id' => 'YL0009',
            'user_id' => $paper_info['user_id'],
            'page' => 'yl_welore/pages/packageA/article/index?id=' . $paper_info['id'] . '&type=' . $paper_info['study_type'],
            'keyword1' => '您的帖子有新的点评',
            'keyword4' => date('Y年m月d日 H:i:s', time()),
        ]);
        return $this->json_rewrite(['status' => 'success', 'msg' => $is_auto_audit == 0 ? '点评成功,请等待审核！' : '点评成功!']);
    }

    /**
     * 我的常用语
     */
    public function paper_review_terms()
    {
        $data = input('param.');
        $list = Db::name('paper_review_common_terms')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->order('id desc')->limit(15)->select();
        return $this->json_rewrite($list);
    }

    /**
     * 删除点评
     */
    public function del_comment()
    {
        $data = input('param.');
        //查询权限
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '权限不足，删除失败！']);
        }
        //查询是否有这个点评
        //查询是否已经点评
        $check_repeat = Db::name('paper_review_score')
            ->where('pa_id', $data['paper_id'])
            ->where('id', $data['com_id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if (empty($check_repeat)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '点评已被删除！']);
        }
        $res = Db::name('paper_review_score')->where('id', $check_repeat['id'])->delete();
        if (!$res) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '删除失败,请稍候重试！']);
        }
        return $this->json_rewrite(['status' => 'success', 'msg' => '删除成功！']);
    }

    /**
     * 通过点评
     */
    public function ok_comment()
    {
        $data = input('param.');
        //查询权限
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '权限不足，审核失败！']);
        }
        //查询是否有这个点评
        //查询是否已经点评
        $check_repeat = Db::name('paper_review_score')
            ->where('id', $data['com_id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if (empty($check_repeat)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '点评已被删除！']);
        }
        $res = Db::name('paper_review_score')->where('id', $check_repeat['id'])->update(['audit_status' => 1]);
        if (!$res) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '审核失败,请稍候重试！']);
        }
        return $this->json_rewrite(['status' => 'success', 'msg' => '审核通过成功！']);
    }

    /**
     * 审核未通过
     */
    public function shenqing_submit()
    {
        $data = input('param.');
        //  更改审核状态
        $d['audit_status'] = 1;
        $d['audit_reason'] = emoji_encode($data['reason']);
        $prsInfo = Db::name('paper_review_score')->where('id', $data['com_id'])->where('much_id', $data['much_id'])->find();
        $u = Db::name('paper_review_score')->where('id', $data['com_id'])->where('much_id', $data['much_id'])->update($d);
        if (!$u) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '审核失败,请稍候重试！']);
        }
        $tmplService = new TmplService();
        $tmplService->add_template([
            'much_id' => $this->much_id,
            'at_id' => 'YL0009',
            'user_id' => $prsInfo['user_id'],
            'page' => 'yl_welore/pages/user_smail/index',
            'keyword1' => "很抱歉，您的点评未通过审核！",
            'keyword4' => date('Y年m月d日 H:i:s', time()),
        ]);
        Db::name('user_smail')->insert(['user_id' => $prsInfo['user_id'], 'maring' => "很抱歉，您的点评未通过审核，拒绝原因：{$d['audit_reason']}", 'clue_time' => time(), 'status' => 0, 'much_id' => $data['much_id']]);
        return $this->json_rewrite(['status' => 'success', 'msg' => '审核拒绝成功！']);
    }

    public function get_emj_list()
    {

        return $this->json_rewrite(Alternative::GetEmoji());
    }
}