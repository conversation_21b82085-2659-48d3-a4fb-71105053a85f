<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="referrer" content="never">
    <title>选择图片</title>
</head>
<link rel="stylesheet" href="assets/css/bootstrap.min.css?v=1.0">
<link rel="stylesheet" href="assets/css/amazeui.min.css?v=1.0"/>
<style>a,a:hover{text-decoration:none;}.pagination{font-size:12px;}.left-li{color:#ffffff;text-align:center;padding:12px 0px;font-size:14px;cursor:pointer;}.right-img{float:left;}::-webkit-scrollbar{width:3px;height:3px;background-color:#F5F5F5;}::-webkit-scrollbar-track{-webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0.3);border-radius:10px;background-color:#F5F5F5;}::-webkit-scrollbar-thumb{border-radius:10px;-webkit-box-shadow:inset 0 0 6px rgba(0,0,0,.3);background-color:#555;}.select_group{background-image:linear-gradient(120deg,#89f7fe 0%,#66a6ff 100%);}.right-x{position:absolute;right:10px;top:-14px;height:30px;width:30px;background-color:rgba(255,0,51,0.7);border-radius:50%;text-align:center;line-height:30px;cursor:pointer;}.right-img-li{max-height:100%; max-width:100%;object-fit: fill;}.img-select{box-shadow:0px 0px 5px 5px rgb(102,153,255)!important;}.jq-ui-button{cursor:pointer;border:0;outline:0;font-size:14px;padding:4px 18px;display:inline-block;vertical-align:middle;overflow-wrap:break-word;border-radius:4px;color:#fff;box-sizing:border-box;cursor:pointer;}.jq-ui-button-blue{background:#57799c;}.jq-ui-button-red{background:#cb6262;}</style>
<body style="width:100%;height:100%;">
<div class="am-g">
    <div class="am-u-sm-2" style="padding:0px;">
        <div style="min-height:600px;;background-color: #212121">
            <div style="color: #fff;text-align: center;padding: 30px 0px 15px 0px;;font-size: 20px;font-weight: 700;">
                图片库 <span class="am-icon-picture-o"></span>
            </div>
            <div class="test-1" style="color: #ffffff;text-align: center;height: 400px;overflow: auto;">
                {volist name="$gclassify" id="vo"}
                <a href="{:url('images/dialogimages')}&gclasid={$vo.id}&dynamicStyle={$dynamicStyle}&pictureIndex={$pictureIndex}">
                    <div class="left-li {if $vo.id==$gclasid}select_group{/if}">
                        {$vo.name}<span style="margin-left: 5px;" class="am-badge am-badge-danger am-round">{$vo.count}</span>
                    </div>
                </a>
                {/volist}
            </div>
            <div style="cursor:pointer;margin: 10px auto;color:#ffffff;background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;" onclick="sngchoice();">
                <span class="am-icon-cloud-upload"></span> 批量上传
            </div>
            <form id="snup" style="display: none;">
                <input type="file" id="sngpic" name="sngpic" accept="image/*" multiple onchange="snuload();">
            </form>
            <div id="custom-network-image" style="cursor:pointer;margin: 15px auto 0 auto;color:#ffffff;background-image: linear-gradient(-60deg, #ff5858 0%, #f09819 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;">
                <span class="am-icon-cloud"></span> 网络图片
            </div>
        </div>
    </div>
    <div class="am-u-sm-10" style="padding:0px;">
        <div  style="width: 100%;min-height:600px;box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 1);">
            <div style="cursor:pointer;margin-right: 20px;float:right;margin-top: 10px;color:#ffffff;background-image: linear-gradient(-225deg, #65379B 0%, #886AEA 53%, #6457C6 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;" onclick="saloof();">
                <span class="am-icon-tag"></span>创建图库
            </div>
            <div style="cursor:pointer;margin-right: 20px;float:right;margin-top: 10px;color:#ffffff;background-image: linear-gradient(-225deg, #7152bb 0%, #c34e4e 53%, #4b808c 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;" onclick="unimgs();">
                <span class="am-icon-trash-o"></span> 删除图片
            </div>
            <div style="clear:both;height:0"></div>
            <div class="test-5" style="padding: 20px;height: 430px;width: 100%;overflow: auto;">
                <ul class="am-avg-sm-5 am-thumbnails">
                    {volist name="gallery" id="vo"}
                    <li style="position: relative;">
                        <div style="display:flex;align-items:center;justify-content:center;cursor:pointer;width:120px;height:120px;box-shadow:10px 5px 10px 0px rgb(204,204,204);border-radius:5px;" onclick="spicking(this);">
                            <img class="right-img-li" 
                                src="{$vo.img_url}" 
                                data-src="{$vo.img_url}"
                                alt="{$vo.img_title}" 
                                title="{$vo.img_title}" 
                                data-urid="{$vo.id}"
                                onerror="this.src='static/wechat/image_vip_top.jpg'"/>
                        </div>
                    </li>
                    {/volist}
                </ul>
            </div>
            <div style="text-align: center;">
                {$gallery->render()}
            </div>
            <div style="text-align: center;">
                <div style="cursor:pointer;margin:0 auto;color:#ffffff;background-image: linear-gradient(to right, #434343 0%, black 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;" onclick="ubacktrack();">
                    确定
                </div>
            </div>
        </div>
    </div>
</div>
<div id="custom-modal-backdrop" style="display:none;width:100%;height:100%;position:absolute;top:0;left:0;background:#0a0a0a;z-index:999;opacity:0.4;"></div>
<div id="custom-modal-reveal" style="display:none;width:60%;height:30%;position:absolute;top:30%;left:22%;margin:0 auto;background:#f8f8f8;z-index:1000;text-align:center;">
    <div style="width:100%;color:#333;font-weight:bold;font-size:2.2rem;margin-top:2.0rem;">
        网络图片地址
    </div>
    <div id="imageAppendReset" style="width:100%;">
        <div id="imageAppend">
            <div class="image-append-offspring" style="clear:both;">
                <div style="width:72%;float:left;text-align:right;margin-top:15px;">
                    <input class="network-image-address" type="text" style="height:28px;width:80%;">
                </div>
                <div style="width:28%;float:left;text-align:left;font-weight:500;font-size:18px;margin-top:15px;">
                <span style="color:#333;margin-left:10px;">
                    <button class="jq-ui-button jq-ui-button-blue" onclick="increasedNetworkImageAddress();">增加地址栏</button>
                </span>
                </div>
            </div>
        </div>
    </div>
    <div style="width:100%;position:absolute;bottom:0px;color:#0e90d2;font-size:1.6rem;">
        <div style="float:left;width:50%;border-top:1px solid #dedede;border-right:1px solid #dedede;padding:10px 0;cursor:pointer;" onclick="increaseWebImage();">确定</div>
        <div id="custom-modal-close" style="float:left;width:50%;border-top:1px solid #dedede;padding:10px 0;cursor:pointer;">取消</div>
    </div>
</div>
</body>
<script src="assets/js/jquery.min.js?v=1.0"></script>
<script src="assets/js/bootstrap.min.js?v=1.0"></script>
<script src="static/layer/layer.js?v=1.0"></script>
<script>
    // 全局函数：获取图片的原始URL（用于数据保存）
    window.getOriginalSrc = function(element) {
        try {
            if (element && element.getAttribute) {
                // 优先从 data-original-src 获取
                var originalSrc = element.getAttribute('data-original-src');
                if (originalSrc) {
                    return originalSrc;
                }
                // 其次从 data-src 获取
                var dataSrc = element.getAttribute('data-src');
                if (dataSrc) {
                    return dataSrc;
                }
                // 最后尝试从当前 src 还原
                var currentSrc = element.src || element.getAttribute('src');
                if (currentSrc && currentSrc.indexOf("{:url('urge/proxy/proxy_resource')}") !== -1) {
                    var match = currentSrc.match(/[?&]url=([^&]+)/);
                    if (match && match[1]) {
                        return decodeURIComponent(match[1]);
                    }
                }
                return currentSrc;
            }
            return element;
        } catch (e) {
            console.warn('getOriginalSrc failed:', e);
            return element;
        }
    };
</script>
<script>

    !function () {
        $('#custom-network-image').click(function () {
            $('#network-image-address').val('');
            $('#custom-modal-backdrop').show('slow');
            var resetHtml = '<div id="imageAppend">\n' +
                '            <div class="image-append-offspring" style="clear:both;">\n' +
                '                <div style="width:72%;float:left;text-align:right;margin-top:15px;">\n' +
                '                    <input class="network-image-address" type="text" style="height:28px;width:80%;">\n' +
                '                </div>\n' +
                '                <div style="width:28%;float:left;text-align:left;font-weight:500;font-size:18px;margin-top:15px;">\n' +
                '                <span style="color:#333;margin-left:10px;">\n' +
                '                    <button class="jq-ui-button jq-ui-button-blue" onclick="increasedNetworkImageAddress();">增加地址栏</button>\n' +
                '                </span>\n' +
                '                </div>\n' +
                '            </div>\n' +
                '        </div>';
            $('#imageAppendReset').html(resetHtml);
            $('#custom-modal-reveal').css({'height': '30%', 'top': '30%'});
            $('#custom-modal-reveal').show('slow');
        });
        $('#custom-modal-close').click(function () {
            $('#network-image-address').val('');
            $('#custom-modal-backdrop').hide('slow');
            $('#custom-modal-reveal').hide('slow');
        });
    }();


    function saloof() {
        layer.open({
            type: 2,
            anim:2,
            scrollbar: true,
            area: ['550px', '230px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/newImgs')}", 'no']
        });
    }


    function sngchoice() {
        var gclasid = $.trim("{$gclasid}");
        if (gclasid == '') {
            parent.layer.msg('请创建一个图库');
            return;
        }
        $('#sngpic').click();
    }

    function snuload() {
        var imgQualified = true;
        $($('#sngpic').get(0).files).each(function (i) {
            var suffix = $('#sngpic').get(0).files[i].name.lastIndexOf('.');
            var ext = $('#sngpic').get(0).files[i].name.substring(suffix, $('#sngpic').get(0).files[i].name.length).toLowerCase();
            if (ext != '.png' && ext != '.gif' && ext != '.jpg' && ext != '.jpeg' && ext != '.bmp') {
                parent.layer.msg('批量选择文件类型错误,请上传图片类型');
                $('#sngpic').val('');
                imgQualified = false;
                return false;
            }
        });
        if (imgQualified) {
            parent.layer.load();
            $($('#sngpic').get(0).files).each(function (i) {
                var formData = new FormData();
                formData.append('sngpic', $('#sngpic').get(0).files[i]);
                $.ajax({
                    type: "post",
                    url: "{:url('upload/operate')}&gclasid={$gclasid}&picture=library",
                    async: false,
                    data: formData,
                    processData: false,
                    contentType: false,
                    dataType: 'json',
                    success: function (data) {
                        setTimeout(function () {
                            parent.layer.closeAll('loading');
                            if (data.status == 'success') {
                                if (($('#sngpic').get(0).files.length - 1) == i) {
                                    handleHttpResources();
                                    location.reload();
                                }
                            } else {
                                parent.layer.msg('上传失败，请检查上传配置');
                                $('#sngpic').val('');
                                return false;
                            }
                        }, 1600);
                    }
                });
            });
        }
    }

    function unimgs(){
        var euid = $.trim($('.img-select img').attr('data-urid'));
        if (euid == '') {
            parent.layer.msg('请选择要删除的图片');
            return;
        }
        $.post("{:url('images/unimgs')}", {'euid': parseInt(euid)}, function (data) {
            if (data.code > 0) {
                parent.layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                parent.layer.msg(data.msg, {icon: 5, time: 1000});
            }
        }, 'json');
    }

    function ubacktrack() {
        var selectedImg = $('.img-select img');
        var eurl = $.trim(selectedImg.attr('src'));
        var originalUrl = selectedImg.attr('data-src');

        if (eurl === '') {
            parent.layer.msg('请选择图片');
            return;
        }

        var dynamicStyle = '{$dynamicStyle}';
        var pictureIndex = Number('{$pictureIndex}');

        // 获取要返回的URL - 如果是HTTPS环境且原始URL是HTTP，则返回代理URL
        var returnUrl = originalUrl;
        if (window.location.protocol === 'https:' && originalUrl && originalUrl.startsWith('http://')) {
            // 检查图片是否已经被代理
            var currentSrc = selectedImg.attr('src');
            if (currentSrc && currentSrc.indexOf("{:url('urge/proxy/proxy_resource')}") !== -1) {
                // 已经使用代理，返回代理URL
                returnUrl = currentSrc;
            } else {
                // 未使用代理，但为了兼容父页面的onerror处理，返回代理URL
                returnUrl = "{:url('urge/proxy/proxy_resource')}&url=" + encodeURIComponent(originalUrl);
            }
        }

        if (pictureIndex !== -2) {
            if (pictureIndex !== -1) {
                if (dynamicStyle === '') {
                    parent.sutake(returnUrl, pictureIndex, 0);
                } else {
                    parent.sutake(returnUrl, pictureIndex, Number(dynamicStyle));
                }
            } else {
                parent.sutake(returnUrl, pictureIndex, 1);
            }
            return;
        }

        if (dynamicStyle === 'richText') {
            parent.sutake(returnUrl, 1);
        } else {
            parent.sutake(returnUrl);
        }
        parent.layer.closeAll();
    }

    var increasedNetworkImageAddress = function () {
        var cmrHeight = $('#custom-modal-reveal').height();
        if (cmrHeight < 450) {
            var cmrOffset = $('#custom-modal-reveal').offset();
            $('#custom-modal-reveal').height(cmrHeight + 45 + 'px');
            if (cmrOffset.top > 80) {
                $('#custom-modal-reveal').css('top', cmrOffset.top - 20 + 'px');
            }
        } else {
            $('#imageAppend').css({'height': '315px', 'overflow-y': 'auto'});
        }
        var html = '<div class="image-append-offspring" style="clear:both;">\n' +
            '                <div style="width:72%;float:left;text-align:right;margin-top:15px;">\n' +
            '                    <input class="network-image-address" type="text" style="height:28px;width:80%;">\n' +
            '                </div>\n' +
            '                <div style="width:28%;float:left;text-align:left;font-weight:500;font-size:18px;margin-top:15px;">\n' +
            '                <span style="color:#333;margin-left:10px;">\n' +
            '                    <button class="jq-ui-button jq-ui-button-red" onclick="removeNetworkImageAddress(this);">移除地址栏</button>\n' +
            '                </span>\n' +
            '                </div>\n' +
            '            </div>';
        $('#imageAppend').append(html);
    }

    var removeNetworkImageAddress = function (obj) {
        $(obj).parent().parent().parent().remove();
        var offspringHeight = 0;
        $('.image-append-offspring').each(function () {
            offspringHeight += 45;
        });
        if (offspringHeight < 315) {
            var cmrHeight = $('#custom-modal-reveal').height();
            var cmrOffset = $('#custom-modal-reveal').offset();
            $('#custom-modal-reveal').height(cmrHeight - 45 + 'px');
            $('#custom-modal-reveal').css('top', cmrOffset.top + 20 + 'px');
            $('#imageAppend').css({'height': 'auto', 'overflow-y': 'visible'});
        }
    }

    var increaseWebImage = function () {
        var i = 0;
        var j = 0;
        $('.network-image-address').each(function () {
            i++;
            var webImg = $.trim($(this).val());
            $.ajaxSettings.async = false;
            $.post("{:url('images/pullNetworkPicture')}", {
                'gclasid': '{$gclasid}', 
                'webImg': webImg
            }, function (data) {
                if (data.code > 0) {
                    j++;
                }
            });
            $.ajaxSettings.async = true;
        });
        if (i == j) {
            parent.layer.msg('添加成功', {icon: 1, time: 1600}, function () {
                handleHttpResources();
                location.reload();
            });
        } else {
            parent.layer.msg('未知错误', {icon: 5, time: 2000});
        }
    }

    function spicking(obj) {
        if ($(obj).hasClass('img-select')) {
            $(obj).removeClass('img-select');
        } else {
            $('.img-select').removeClass('img-select');
            $(obj).addClass('img-select');
        }
    }

    function handleHttpResources() {
        if (window.location.protocol === 'https:') {
            document.querySelectorAll('img[src^="http://"]').forEach(function(img) {
                var originalSrc = img.getAttribute('data-src') || img.src;
                var proxyUrl = "{:url('urge/proxy/proxy_resource')}&url=" + encodeURIComponent(originalSrc);

                var tmpImg = new Image();
                tmpImg.onload = function() {
                    // 原始图片可访问，存储原始URL但不修改显示
                    img.setAttribute('data-original-src', originalSrc);
                }
                tmpImg.onerror = function() {
                    // 原始图片不可访问，使用代理并存储原始URL
                    img.setAttribute('data-original-src', originalSrc);
                    img.src = proxyUrl;
                }
                tmpImg.src = originalSrc;
            });
        }
    }

    $(document).ready(function() {
        handleHttpResources();
    });

    var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                handleHttpResources();
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
</script>
</html>
