<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:93:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/vicinity/new_located.html";i:1755498243;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755516152;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a></li>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>
    .tpl-portlet-components {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        padding: 40px;
    }

    .portlet-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 25px;
        margin-bottom: 35px;
        border-bottom: 2px solid #f5f5f5;
    }

    .caption {
        font-size: 20px;
        color: #23b7e5;
        font-weight: 600;
    }

    .caption .am-icon-plus {
        margin-right: 8px;
        font-size: 18px;
    }

    .am-form-horizontal .am-form-group {
        margin: 45px 0;
    }

    .am-form-horizontal .am-form-label {
        text-align: right;
        font-weight: 600;
        padding-top: 12px;
        font-size: 15px;
        color: #333;
    }

    .am-form-horizontal .am-form-group .am-u-sm-9 {
        padding-left: 25px;
    }

    .am-form-horizontal input[type='text'] {
        border-radius: 6px;
        border: 1px solid #ddd;
        padding: 12px 15px;
        transition: all 0.3s;
        width: 100%;
        height: 48px;
        font-size: 14px;
    }

    .am-form-horizontal input[type='text']:focus {
        border-color: #23b7e5;
        box-shadow: 0 0 0 2px rgba(35,183,229,0.1);
    }

    .el-input__inner {
        border-radius: 6px;
        border: 1px solid #ddd;
        padding: 12px 15px;
        transition: all 0.3s;
        background: #fff !important;
        height: 48px;
        font-size: 14px;
    }

    .el-input__inner:focus {
        border-color: #23b7e5;
        box-shadow: 0 0 0 3px rgba(35,183,229,0.1);
    }

    .am-btn {
        padding: 12px 30px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.3s;
        margin-right: 15px;
    }

    .am-btn-primary {
        background-color: #23b7e5;
        border-color: #23b7e5;
        min-width: 120px;
    }

    .am-btn-primary:hover {
        background-color: #49c5ec;
        border-color: #49c5ec;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(35,183,229,0.3);
    }

    .am-btn-default {
        background-color: #f8f9fa;
        border-color: #ddd;
        color: #666;
        min-width: 100px;
    }

    .am-btn-default:hover {
        background-color: #e9ecef;
        border-color: #ccc;
        transform: translateY(-1px);
    }

    .required {
        color: #e74c3c;
        margin-left: 4px;
        font-weight: bold;
    }

    .error-message {
        color: #e74c3c;
        font-size: 13px;
        margin-top: 8px;
        padding: 8px 12px;
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 4px;
        display: flex;
        align-items: center;
    }

    .error-message::before {
        content: "⚠";
        margin-right: 6px;
        font-weight: bold;
    }

    .help-text {
        color: #666;
        font-size: 13px;
        margin-top: 8px;
        padding: 6px 0;
        line-height: 1.4;
    }

    .has-error input,
    .has-error .el-input__inner {
        border-color: #e74c3c;
        box-shadow: 0 0 0 3px rgba(231,76,60,0.1);
    }

    /* 按钮组间距 */
    .am-form-group:last-child {
        margin-top: 50px;
        padding-top: 30px;
        border-top: 1px solid #f0f0f0;
    }

    /* 禁用状态 */
    .am-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: none !important;
    }

    /* 坐标获取链接样式 */
    .coordinate-links {
        margin-top: 8px;
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .coord-link {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        color: #495057;
        text-decoration: none;
        font-size: 12px;
        transition: all 0.3s;
    }

    .coord-link:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
        color: #23b7e5;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .coord-link .am-icon-map-marker,
    .coord-link .am-icon-location-arrow {
        margin-right: 4px;
        font-size: 12px;
    }

    @media (max-width: 768px) {
        .coordinate-links {
            flex-direction: column;
            gap: 8px;
        }

        .coord-link {
            justify-content: center;
        }
    }
</style>
<div id="app" class="tpl-portlet-components" v-cloak>
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-plus"></span> 新增圈子位置
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-8 am-u-md-offset-2">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            圈子名称<span class="required">*</span>
                        </label>
                        <div class="am-u-sm-9" :class="{'has-error': errors.toryId}">
                            <el-select
                                class="w-100"
                                v-model="item.toryId"
                                filterable
                                placeholder="请选择要设置位置的圈子">
                                <el-option v-for="tory in toryOptions" :key="tory.id" :label="tory.realm_name" :value="tory.id"></el-option>
                            </el-select>
                            <div v-if="errors.toryId" class="error-message">{{errors.toryId}}</div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            圈子经度<span class="required">*</span>
                        </label>
                        <div class="am-u-sm-9" :class="{'has-error': errors.longitude}">
                            <input
                                type="text"
                                v-model="item.longitude"
                                placeholder="请输入圈子的经度坐标">
                            <div class="help-text">经度范围：-180 到 180，例如：116.40</div>
                            <div class="coordinate-links">
                                <a href="https://lbs.amap.com/tools/picker" target="_blank" class="coord-link">
                                    <span class="am-icon-map-marker"></span> 高德地图获取坐标
                                </a>
                                <a href="https://api.map.baidu.com/lbsapi/getpoint/index.html" target="_blank" class="coord-link">
                                    <span class="am-icon-location-arrow"></span> 百度地图获取坐标
                                </a>
                            </div>
                            <div v-if="errors.longitude" class="error-message">{{errors.longitude}}</div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            圈子纬度<span class="required">*</span>
                        </label>
                        <div class="am-u-sm-9" :class="{'has-error': errors.latitude}">
                            <input
                                type="text"
                                v-model="item.latitude"
                                placeholder="请输入圈子的纬度坐标">
                            <div class="help-text">纬度范围：-90 到 90，例如：39.92</div>
                            <div class="coordinate-links">
                                <a href="https://lbs.amap.com/tools/picker" target="_blank" class="coord-link">
                                    <span class="am-icon-map-marker"></span> 高德地图获取坐标
                                </a>
                                <a href="https://api.map.baidu.com/lbsapi/getpoint/index.html" target="_blank" class="coord-link">
                                    <span class="am-icon-location-arrow"></span> 百度地图获取坐标
                                </a>
                            </div>
                            <div v-if="errors.latitude" class="error-message">{{errors.latitude}}</div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-offset-3">
                            <button
                                type="button"
                                class="am-btn am-btn-primary"
                                @click="holdSave"
                                :disabled="isLoading">
                                {{isLoading ? '保存中...' : '保存'}}
                            </button>
                            <button
                                type="button"
                                class="am-btn am-btn-default"
                                onclick="history.back()"
                                :disabled="isLoading">
                                返回
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<?php if($much_role=='founder'): ?>
<script>
    var updateFix = function () {
        layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("<?php echo url('index/repairMissing'); ?>", function (data) {
                if (data !== false) {
                    layer.msg('数据库缺失字段修复完成', {time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('修复失败', {time: 1600}, function () {
                        location.reload();
                    });
                }
            });
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }
</script>
<?php endif; ?>
<script>
    new Vue({
        el: '#menu',
        data() {
            return {
                menuActive: '<?php echo $menuActive; ?>',
                menuList: []
            };
        },
        created() {
            const menuList = '<?php echo $menuList; ?>';
            if (menuList !== '') {
                const menuData = JSON.parse(decodeURIComponent(atob(menuList)));
                for (let i = 0; i < menuData.length; i++) {
                    if (menuData[i].pid === 0) {
                        menuData[i].childMenuItems = [];
                        this.menuList.push(menuData[i]);
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        if (menuData[i].pid === this.menuList[j].id) {
                            const childItem = JSON.parse(JSON.stringify(menuData[i]));
                            childItem.grandsonList = [];
                            this.menuList[j].childMenuItems.push(childItem);
                            break;
                        }
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        for (let k = 0; k < this.menuList[j].childMenuItems.length; k++) {
                            if (menuData[i].pid === this.menuList[j].childMenuItems[k].id) {
                                this.menuList[j].childMenuItems[k].grandsonList.push(menuData[i]);
                                break;
                            }
                        }
                    }
                }
            }
        }
    });

    $(function () {
        setInterval(reballot, 15000);
    });

    var reballot = function () {
        $.getJSON("<?php echo url('ordinary'); ?>", function (data) {
            var i = 0;
            if (data.notice > 0 || data.vacant > 0) {
                i++;
            }
            $('#notice-0,#notice-1').text(data.notice);
            $('#vacant-0,#vacant-1').text(data.vacant);
            if ((data.notice + data.vacant) <= data.preCount) {
                i = 0;
            } else {
                $.post("<?php echo url('receipt'); ?>", {'multiply': (data.notice + data.vacant)});
            }
            if (i > 0) {
                var player = $("#backPlayer")[0];
                player.play();
            }
        });
    }

    var retakeCache = function () {
        $.get("<?php echo url('index/purgeCache'); ?>", function () {
            layer.msg('缓存清理完成', {time: 1000}, function () {
                location.reload();
            });
        });
    }
</script>
<script>
// 添加全局变量
window.EDITOR_MODE = false;  // 标记是否在编辑器模式

// AJAX 拦截器 - 自动还原代理 URL 为原始 URL
(function() {
    // 代理 URL 的匹配模式
    var proxyUrlPattern = /[?&]url=([^&]+)/;
    var proxyBaseUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>";

    // URL 还原函数
    function restoreOriginalUrl(url) {
        try {
            // 检查是否是代理 URL
            if (typeof url === 'string' && url.indexOf(proxyBaseUrl) !== -1) {
                var match = url.match(proxyUrlPattern);
                if (match && match[1]) {
                    // 解码并返回原始 URL
                    return decodeURIComponent(match[1]);
                }
            }
            return url;
        } catch (e) {
            // 出错时返回原始值，确保不影响正常功能
            console.warn('URL restore failed:', e);
            return url;
        }
    }

    // 增强版 URL 还原函数 - 支持从 DOM 元素的 data 属性获取原始 URL
    window.getOriginalSrc = function(element) {
        try {
            if (element && element.getAttribute) {
                // 优先从 data-original-src 获取
                var originalSrc = element.getAttribute('data-original-src');
                if (originalSrc) {
                    return originalSrc;
                }
                // 其次尝试从当前 src 还原
                var currentSrc = element.src || element.getAttribute('src');
                return restoreOriginalUrl(currentSrc);
            }
            return element;
        } catch (e) {
            console.warn('getOriginalSrc failed:', e);
            return element;
        }
    };

    // 递归处理数据对象，还原其中的代理 URL
    function restoreDataUrls(data) {
        try {
            if (typeof data === 'string') {
                return restoreOriginalUrl(data);
            } else if (Array.isArray(data)) {
                return data.map(restoreDataUrls);
            } else if (data && typeof data === 'object') {
                var restored = {};
                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        restored[key] = restoreDataUrls(data[key]);
                    }
                }
                return restored;
            }
            return data;
        } catch (e) {
            // 出错时返回原始数据
            console.warn('Data restore failed:', e);
            return data;
        }
    }

    // 保存原始的 jQuery AJAX 方法
    var originalAjax = $.ajax;
    var originalPost = $.post;
    var originalGet = $.get;

    // 重写 $.ajax
    $.ajax = function(options) {
        try {
            if (options && options.data) {
                options.data = restoreDataUrls(options.data);
            }
        } catch (e) {
            console.warn('AJAX data restore failed:', e);
        }
        return originalAjax.apply(this, arguments);
    };

    // 重写 $.post
    $.post = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('POST data restore failed:', e);
        }
        return originalPost.call(this, url, data, success, dataType);
    };

    // 重写 $.get
    $.get = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('GET data restore failed:', e);
        }
        return originalGet.call(this, url, data, success, dataType);
    };
})();

// 修改检测函数
function checkResourceAccess() {
    var isHttps = window.location.protocol === 'https:';
    
    if (isHttps) {
        // 处理图片
        document.querySelectorAll('img[src^="http://"]').forEach(function(img) {
            // 如果是编辑器模式，所有图片都使用代理URL显示
            if(window.EDITOR_MODE) {
                var originalSrc = img.getAttribute('data-src') || img.src;
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
                return;
            }
            
            // 非编辑器模式下排除富文本编辑器内的图片
            if(img.closest('.w-e-text') || img.closest('.w-e-text-container')) {
                return;
            }
            
            var originalSrc = img.getAttribute('data-src') || img.src;
            var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);

            var tmpImg = new Image();
            tmpImg.onload = function() {
                // 原始图片可访问，不做处理
            }
            tmpImg.onerror = function() {
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = proxyUrl;
            }
            tmpImg.src = originalSrc;
        });
        
        // 处理视频
        document.querySelectorAll('video').forEach(function(video) {
            // 处理video的src属性
            if (video.src && video.src.startsWith('http://')) {
                handleResource(video, 'src');
            }
            
            // 处理source标签
            video.querySelectorAll('source[src^="http://"]').forEach(function(source) {
                handleResource(source, 'src');
            });
        });
    }
}

// 统一的资源处理函数
function handleResource(element, srcAttr) {
    var originalSrc = element[srcAttr];
    var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
    
    // 创建临时对象测试资源是否可访问
    var tmpObj = new Image(); // 用Image对象测试连接可用性
    tmpObj.onload = function() {
        // 原始资源可访问，不做处理
    }
    tmpObj.onerror = function() {
        // 原始资源不可访问，切换到代理
        // 存储原始 URL 到 data 属性
        element.setAttribute('data-original-src', originalSrc);
        element[srcAttr] = proxyUrl;

        // 同时更新父级a标签的href
        var parent = element.parentElement;
        if (parent && parent.tagName === 'A') {
            parent.setAttribute('data-original-href', parent.href);
            parent.href = proxyUrl;
        }
    }
    tmpObj.src = originalSrc;
}

// 页面加载完成后执行检查
window.addEventListener('load', checkResourceAccess);

// 动态加载的内容处理
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            checkResourceAccess();
        }
    });
});

// 监听DOM变化
observer.observe(document.body, {
    childList: true,
    subtree: true
});
</script>

<script>
    var vm = new Vue({
        el: '#app',
        data() {
            return {
                item: {
                    toryId: '',
                    longitude: '',
                    latitude: ''
                },
                toryOptions: [],
                isLoading: false,
                errors: {
                    toryId: '',
                    longitude: '',
                    latitude: ''
                }
            }
        },
        created() {
            // 初始化圈子选项
            this.toryOptions = JSON.parse(decodeURIComponent(atob('<?php echo $toryListData; ?>')));
        },
        methods: {
            // 清除错误信息
            clearErrors: function() {
                this.errors = {
                    toryId: '',
                    longitude: '',
                    latitude: ''
                };
            },

            // 表单验证
            validateForm: function() {
                this.clearErrors();
                let isValid = true;

                // 验证圈子选择
                if (!this.item.toryId) {
                    this.errors.toryId = '请选择圈子';
                    isValid = false;
                }

                // 验证经度
                const longitude = $.trim(this.item.longitude);
                if (!longitude) {
                    this.errors.longitude = '请输入圈子经度';
                    isValid = false;
                } else if (isNaN(longitude) || longitude < -180 || longitude > 180) {
                    this.errors.longitude = '经度必须是-180到180之间的数字';
                    isValid = false;
                }

                // 验证纬度
                const latitude = $.trim(this.item.latitude);
                if (!latitude) {
                    this.errors.latitude = '请输入圈子纬度';
                    isValid = false;
                } else if (isNaN(latitude) || latitude < -90 || latitude > 90) {
                    this.errors.latitude = '纬度必须是-90到90之间的数字';
                    isValid = false;
                }

                return isValid;
            },

            // 保存数据
            holdSave: function () {
                if (!this.validateForm()) {
                    return;
                }

                if (this.isLoading) {
                    return;
                }

                this.isLoading = true;
                var setData = JSON.parse(JSON.stringify(this.item));

                $.post("<?php echo url('vicinity/new_located'); ?>", {
                    tory_id: setData.toryId,
                    longitude: setData.longitude,
                    latitude: setData.latitude
                }, (data) => {
                    this.isLoading = false;
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1500}, function () {
                            location.href = "<?php echo url('vicinity/located_list'); ?>";
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2500});
                    }
                }, 'json').fail(() => {
                    this.isLoading = false;
                    layer.msg('网络错误，请稍后重试', {icon: 5, time: 2500});
                });
            }
        }
    });
</script>

</body>
</html>