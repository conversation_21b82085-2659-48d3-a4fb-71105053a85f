<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:93:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/shield/thread_config.html";i:1749789416;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755516152;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a></li>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-code {margin-right: 5px;}
    
    .am-form-horizontal .am-form-group { margin-bottom: 20px; }
    .am-form-horizontal .am-form-label { text-align: right; font-weight: 500; padding-top: .5em; }
    .am-form-horizontal .am-form-group .am-u-sm-9 { padding-left: 15px; }
    .am-form-horizontal input[type='text'], .am-form-horizontal select {
        border-radius: 3px;
        border: 1px solid #e8e8e8;
        padding: 6px 10px;
        transition: all 0.3s;
        width: 100%;
        height: 38px;
    }
    .am-form-horizontal input[type='text']:focus, .am-form-horizontal select:focus {
        border-color: #23b7e5;
        box-shadow: 0 0 0 2px rgba(35,183,229,0.1);
    }
    .am-checkbox-inline { margin-right: 20px; }

    .image-uploader-wrapper { display: flex; align-items: center; }
    .image-preview { width: 120px; height: 120px; border: 1px dashed #d9d9d9; border-radius: 4px; cursor: pointer; display: flex; align-items: center; justify-content: center; overflow: hidden; }
    .image-preview:hover { border-color: #23b7e5; }
    .image-preview img { max-width: 100%; max-height: 100%; }
    .image-preview .upload-icon { font-size: 28px; color: #8c939d; }

    .am-btn-primary { background-color: #23b7e5; border-color: #23b7e5; border-radius: 3px; }
    .am-btn-primary:hover { background-color: #49c5ec; border-color: #49c5ec; }

</style>
<div id="app" class="tpl-portlet-components" v-cloak>
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-code"></span> 热帖配置
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-8 am-u-md-offset-2">
                <div class="am-form am-form-horizontal">

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">热榜标题</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.title" placeholder="请输入榜单名称">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">样式风格</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.styleType">
                                <option value="0">样式1</option>
                                <option value="1">样式2</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">热榜图片</label>
                        <div class="am-u-sm-9">
                             <div class="image-uploader-wrapper">
                                <div class="image-preview" onclick="cuonice();">
                                    <img v-if="item.headImg" :src="item.headImg" onerror="this.src='static/wechat/image_vip_top.jpg'" alt="热榜图片">
                                    <i v-else class="am-icon-plus upload-icon"></i>
                                </div>
                            </div>
                             <form id="snup" style="display: none;">
                                <input type="file" id="sngpic" name="sngpic" onchange="snuload();">
                            </form>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">统计范围</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.statisticsTime">
                                <option value="0">全部</option>
                                <option value="1">天</option>
                                <option value="2">周</option>
                                <option value="3">月</option>
                                <option value="4">季</option>
                                <option value="5">年</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序条件</label>
                        <div class="am-u-sm-9">
                            <label class="am-checkbox-inline">
                                <input v-model="item.sortCondition" type="checkbox" value="liulan"> 浏览
                            </label>
                            <label class="am-checkbox-inline">
                                <input v-model="item.sortCondition" type="checkbox" value="dianzan"> 点赞
                            </label>
                            <label class="am-checkbox-inline">
                                <input v-model="item.sortCondition" type="checkbox" value="shoucang"> 收藏
                            </label>
                            <label class="am-checkbox-inline">
                                <input v-model="item.sortCondition" type="checkbox" value="huifu"> 回复
                            </label>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">显示开关</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.displaySwitch">
                                <option value="1">显示</option>
                                <option value="0">隐藏</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="am-form-group">
                         <div class="am-u-sm-9 am-u-sm-offset-3">
                            <button type="button" class="am-btn am-btn-primary" @click="holdSave">
                                保存
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<?php if($much_role=='founder'): ?>
<script>
    var updateFix = function () {
        layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("<?php echo url('index/repairMissing'); ?>", function (data) {
                if (data !== false) {
                    layer.msg('数据库缺失字段修复完成', {time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('修复失败', {time: 1600}, function () {
                        location.reload();
                    });
                }
            });
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }
</script>
<?php endif; ?>
<script>
    new Vue({
        el: '#menu',
        data() {
            return {
                menuActive: '<?php echo $menuActive; ?>',
                menuList: []
            };
        },
        created() {
            const menuList = '<?php echo $menuList; ?>';
            if (menuList !== '') {
                const menuData = JSON.parse(decodeURIComponent(atob(menuList)));
                for (let i = 0; i < menuData.length; i++) {
                    if (menuData[i].pid === 0) {
                        menuData[i].childMenuItems = [];
                        this.menuList.push(menuData[i]);
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        if (menuData[i].pid === this.menuList[j].id) {
                            const childItem = JSON.parse(JSON.stringify(menuData[i]));
                            childItem.grandsonList = [];
                            this.menuList[j].childMenuItems.push(childItem);
                            break;
                        }
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        for (let k = 0; k < this.menuList[j].childMenuItems.length; k++) {
                            if (menuData[i].pid === this.menuList[j].childMenuItems[k].id) {
                                this.menuList[j].childMenuItems[k].grandsonList.push(menuData[i]);
                                break;
                            }
                        }
                    }
                }
            }
        }
    });

    $(function () {
        setInterval(reballot, 15000);
    });

    var reballot = function () {
        $.getJSON("<?php echo url('ordinary'); ?>", function (data) {
            var i = 0;
            if (data.notice > 0 || data.vacant > 0) {
                i++;
            }
            $('#notice-0,#notice-1').text(data.notice);
            $('#vacant-0,#vacant-1').text(data.vacant);
            if ((data.notice + data.vacant) <= data.preCount) {
                i = 0;
            } else {
                $.post("<?php echo url('receipt'); ?>", {'multiply': (data.notice + data.vacant)});
            }
            if (i > 0) {
                var player = $("#backPlayer")[0];
                player.play();
            }
        });
    }

    var retakeCache = function () {
        $.get("<?php echo url('index/purgeCache'); ?>", function () {
            layer.msg('缓存清理完成', {time: 1000}, function () {
                location.reload();
            });
        });
    }
</script>
<script>
// 添加全局变量
window.EDITOR_MODE = false;  // 标记是否在编辑器模式

// AJAX 拦截器 - 自动还原代理 URL 为原始 URL
(function() {
    // 代理 URL 的匹配模式
    var proxyUrlPattern = /[?&]url=([^&]+)/;
    var proxyBaseUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>";

    // URL 还原函数
    function restoreOriginalUrl(url) {
        try {
            // 检查是否是代理 URL
            if (typeof url === 'string' && url.indexOf(proxyBaseUrl) !== -1) {
                var match = url.match(proxyUrlPattern);
                if (match && match[1]) {
                    // 解码并返回原始 URL
                    return decodeURIComponent(match[1]);
                }
            }
            return url;
        } catch (e) {
            // 出错时返回原始值，确保不影响正常功能
            console.warn('URL restore failed:', e);
            return url;
        }
    }

    // 增强版 URL 还原函数 - 支持从 DOM 元素的 data 属性获取原始 URL
    window.getOriginalSrc = function(element) {
        try {
            if (element && element.getAttribute) {
                // 优先从 data-original-src 获取
                var originalSrc = element.getAttribute('data-original-src');
                if (originalSrc) {
                    return originalSrc;
                }
                // 其次尝试从当前 src 还原
                var currentSrc = element.src || element.getAttribute('src');
                return restoreOriginalUrl(currentSrc);
            }
            return element;
        } catch (e) {
            console.warn('getOriginalSrc failed:', e);
            return element;
        }
    };

    // 递归处理数据对象，还原其中的代理 URL
    function restoreDataUrls(data) {
        try {
            if (typeof data === 'string') {
                return restoreOriginalUrl(data);
            } else if (Array.isArray(data)) {
                return data.map(restoreDataUrls);
            } else if (data && typeof data === 'object') {
                var restored = {};
                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        restored[key] = restoreDataUrls(data[key]);
                    }
                }
                return restored;
            }
            return data;
        } catch (e) {
            // 出错时返回原始数据
            console.warn('Data restore failed:', e);
            return data;
        }
    }

    // 保存原始的 jQuery AJAX 方法
    var originalAjax = $.ajax;
    var originalPost = $.post;
    var originalGet = $.get;

    // 重写 $.ajax
    $.ajax = function(options) {
        try {
            if (options && options.data) {
                options.data = restoreDataUrls(options.data);
            }
        } catch (e) {
            console.warn('AJAX data restore failed:', e);
        }
        return originalAjax.apply(this, arguments);
    };

    // 重写 $.post
    $.post = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('POST data restore failed:', e);
        }
        return originalPost.call(this, url, data, success, dataType);
    };

    // 重写 $.get
    $.get = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('GET data restore failed:', e);
        }
        return originalGet.call(this, url, data, success, dataType);
    };
})();

// 修改检测函数
function checkResourceAccess() {
    var isHttps = window.location.protocol === 'https:';
    
    if (isHttps) {
        // 处理图片
        document.querySelectorAll('img[src^="http://"]').forEach(function(img) {
            // 如果是编辑器模式，所有图片都使用代理URL显示
            if(window.EDITOR_MODE) {
                var originalSrc = img.getAttribute('data-src') || img.src;
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
                return;
            }
            
            // 非编辑器模式下排除富文本编辑器内的图片
            if(img.closest('.w-e-text') || img.closest('.w-e-text-container')) {
                return;
            }
            
            var originalSrc = img.getAttribute('data-src') || img.src;
            var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);

            var tmpImg = new Image();
            tmpImg.onload = function() {
                // 原始图片可访问，不做处理
            }
            tmpImg.onerror = function() {
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = proxyUrl;
            }
            tmpImg.src = originalSrc;
        });
        
        // 处理视频
        document.querySelectorAll('video').forEach(function(video) {
            // 处理video的src属性
            if (video.src && video.src.startsWith('http://')) {
                handleResource(video, 'src');
            }
            
            // 处理source标签
            video.querySelectorAll('source[src^="http://"]').forEach(function(source) {
                handleResource(source, 'src');
            });
        });
    }
}

// 统一的资源处理函数
function handleResource(element, srcAttr) {
    var originalSrc = element[srcAttr];
    var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
    
    // 创建临时对象测试资源是否可访问
    var tmpObj = new Image(); // 用Image对象测试连接可用性
    tmpObj.onload = function() {
        // 原始资源可访问，不做处理
    }
    tmpObj.onerror = function() {
        // 原始资源不可访问，切换到代理
        // 存储原始 URL 到 data 属性
        element.setAttribute('data-original-src', originalSrc);
        element[srcAttr] = proxyUrl;

        // 同时更新父级a标签的href
        var parent = element.parentElement;
        if (parent && parent.tagName === 'A') {
            parent.setAttribute('data-original-href', parent.href);
            parent.href = proxyUrl;
        }
    }
    tmpObj.src = originalSrc;
}

// 页面加载完成后执行检查
window.addEventListener('load', checkResourceAccess);

// 动态加载的内容处理
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            checkResourceAccess();
        }
    });
});

// 监听DOM变化
observer.observe(document.body, {
    childList: true,
    subtree: true
});
</script>

<script>
    var vm = new Vue({
        el: '#app',
        data() {
            return {
                item: {
                    title: '<?php echo $list['custom_title']; ?>',
                    styleType: '<?php echo $list['style_type']; ?>',
                    headImg: '<?php echo $list['custom_head_img']; ?>',
                    statisticsTime: '<?php echo $list['statistics_time']; ?>',
                    sortCondition: [],
                    displaySwitch: '<?php echo $list['display_switch']; ?>'
                },
                onLock: false,
            }
        }, created() {
            this.item.sortCondition = JSON.parse('<?php echo $list['custom_sort_condition']; ?>');
        }, methods: {
            holdSave() {
                if (this.item.title === '') {
                    layer.msg('请输入热榜标题');
                    return;
                }
                if (!this.onLock) {
                    this.onLock = true;
                    var setData = this.item;
                    $.ajax({
                        type: "post",
                        url: "<?php echo url('shield/thread_config'); ?>",
                        data: setData,
                        success: function (data) {
                            if (data.code > 0) {
                                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                    location.reload();
                                });
                            } else {
                                layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                    this.onLock = false;
                                });
                            }
                        }
                    });
                }
            }
        }
    })

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["<?php echo url('images/dialogImages'); ?>&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        vm.item.headImg = eurl;
        vm.$forceUpdate();
        layer.closeAll();
    }

</script>

</body>
</html>