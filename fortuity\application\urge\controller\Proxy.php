<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use think\Response;

/**
 * 资源代理控制器
 */
// 定义一个名为 Proxy 的类，它继承自项目中的 Base 控制器
class Proxy extends Base
{
    /**
     * 每秒最大传输速率（字节）
     * 1MB/s
     */
    // 定义一个常量，表示速率限制为 1MB/s
    const RATE_LIMIT = 1048576;

    /**
     * cURL请求超时时间（秒）
     */
    // 定义一个常量，表示cURL请求的超时时间为30秒
    const TIMEOUT = 30;

    /**
     * 小图片缓存阈值（字节）
     * 2MB
     */
    // 定义一个常量，表示需要缓存的小图片的大小阈值为 2MB
    const IMAGE_CACHE_THRESHOLD = 2097152;

    /**
     * 代理资源入口
     * @return Response
     */
    // 定义公共方法 proxy_resource，作为代理功能的入口
    public function proxy_resource()
    {
        // 从HTTP请求中获取 'url' 参数
        $url = $this->request->get('url');
        // 检查URL是否为空或格式不正确
        if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
            // 如果URL无效，则返回一个400错误响应
            return Response::create('Invalid URL', 'html', 400);
        }

        // 增加安全检查，防止SSRF攻击
        if (!$this->_isSafeUrl($url)) {
            // 如果URL不安全（例如指向内网），则返回一个403禁止访问的响应
            return Response::create('URL is not allowed.', 'html', 403);
        }

        // 使用 try-catch 块来捕获后续代码中可能出现的异常
        try {
            // 1. 先用HEAD请求获取文件信息
            $headerInfo = $this->_getResourceHeader($url);

            // 检查远端服务器返回的HTTP状态码，如果大于等于400，则表示有错误
            if ($headerInfo['http_code'] >= 400) {
                // 返回一个错误响应，状态码与远端服务器保持一致
                return Response::create('Resource not found or access denied.', 'html', $headerInfo['http_code']);
            }

            // 从头信息中获取内容长度
            $contentLength = $headerInfo['content_length'];
            // 从头信息中获取内容类型
            $contentType = $headerInfo['content_type'];

            // 2. 根据文件类型和大小决定处理方式
            // 判断内容类型是否以 'image/' 开头，以确定是否为图片
            $isImage = strpos($contentType, 'image/') === 0;

            // 如果是图片，且内容长度大于0且小于缓存阈值
            if ($isImage && $contentLength > 0 && $contentLength < self::IMAGE_CACHE_THRESHOLD) {
                // 调用处理缓存图片的方法
                return $this->_proxyImage($url, $contentType);
            }

            // 对于视频、大图片或未知大小的文件，调用流式传输方法
            $this->_streamResource($url, $headerInfo);

            // 因为 _streamResource 方法会以 exit 结束，所以这行代码理论上不会被执行
            return new Response();
            // 捕获可能发生的 \Exception 异常
        } catch (\Exception $e) {
            // 在生产环境中，应当记录错误日志（此行为被注释，可按需开启）
            // \think\Log::error('Proxy Error: ' . $e->getMessage());
            // 返回一个500服务器内部错误响应
            return Response::create('Proxy service internal error.', 'html', 500);
        }
    }

    /**
     * 代理并缓存小图片
     * @param string $url
     * @param string $contentType
     * @return Response
     */
    // 定义私有方法 _proxyImage 用于处理小图片的代理和缓存
    private function _proxyImage($url, $contentType)
    {
        // 根据URL的md5哈希值生成一个唯一的缓存键
        $cacheKey = 'proxy_img_' . md5($url);
        // 尝试从缓存中获取数据
        $cached = cache($cacheKey);

        // 如果缓存命中
        if ($cached) {
            // 从缓存数组中获取图片内容
            $content = $cached['content'];
            // 从缓存数组中获取内容类型
            $contentType = $cached['type'];
            // 如果缓存未命中
        } else {
            // 创建一个新的cURL句柄
            $ch = $this->_createCurlHandle($url);
            // 设置cURL选项，将结果作为字符串返回，而不是直接输出
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            // 执行cURL请求，获取图片内容
            $content = curl_exec($ch);
            // 获取请求的HTTP状态码
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            // 关闭cURL句柄
            curl_close($ch);

            // 如果请求成功（状态码200）并且获取到了内容
            if ($httpCode === 200 && $content) {
                // 将获取到的内容和类型存入缓存，有效期为86400秒（1天）
                cache($cacheKey, [
                    'content' => $content,
                    'type' => $contentType
                ], 86400);
                // 如果请求失败
            } else {
                // 返回一个502网关错误响应
                return Response::create('Failed to fetch image content.', 'html', 502);
            }
        }

        // 创建一个ThinkPHP响应对象
        return Response::create($content, 'html')
            // 设置响应头：内容类型
            ->header('Content-Type', $contentType)
            // 设置响应头：内容长度
            ->header('Content-Length', strlen($content))
            // 设置响应头：缓存控制策略
            ->header('Cache-Control', 'public, max-age=86400');
    }

    /**
     * 流式代理资源（视频、大文件等）
     * @param string $url
     * @param array $headerInfo
     * @return void
     */
    // 定义私有方法 _streamResource 用于处理大文件或视频的流式传输
    private function _streamResource($url, $headerInfo)
    {
        // 从头信息中获取内容长度
        $contentLength = $headerInfo['content_length'];
        // 从头信息中获取内容类型
        $contentType = $headerInfo['content_type'];

        // 从请求头中获取 'range' 信息，用于断点续传
        $range = $this->request->header('range');
        // 初始化范围的起始位置
        $start = 0;
        // 初始化范围的结束位置
        $end = 0;
        // 标记是否为部分内容请求
        $isPartial = false;

        // 如果内容长度已知，且收到了 'range' 请求头，则处理范围请求
        if ($contentLength > 0 && $range && preg_match('/bytes=(\d+)-(\d+)?/', $range, $matches)) {
            // 标记为部分内容请求
            $isPartial = true;
            // 从正则表达式匹配结果中获取起始字节
            $start = intval($matches[1]);
            // 默认的结束字节是文件的最后一个字节
            $end = $contentLength - 1;
            // 如果 'range' 请求中明确指定了结束字节
            if (isset($matches[2]) && $matches[2] !== '') {
                // 使用请求中指定的结束字节
                $end = intval($matches[2]);
            }
            // 确保请求的结束字节不超过文件的实际大小
            $end = min($end, $contentLength - 1);
            // 如果请求的范围无效（例如起始大于结束）
            if ($start > $end || $start < 0) {
                // 则退回为完整请求
                $isPartial = false;
                // 起始位置设为0
                $start = 0;
                // 结束位置设为文件末尾
                $end = $contentLength - 1;
            }
            // 如果内容长度已知，但没有 'range' 请求
        } elseif ($contentLength > 0) {
            // 设置结束位置为文件末尾
            $end = $contentLength - 1;
        }

        // 计算本次传输的长度；如果总长度未知，则设为-1
        $length = ($contentLength > 0) ? ($end - $start + 1) : -1;

        // 创建一个新的cURL句柄
        $ch = $this->_createCurlHandle($url);

        // 初始化cURL选项数组
        $curlOptions = array();
        // 如果是部分内容请求
        if ($isPartial) {
            // 设置cURL的CURLOPT_RANGE选项，告诉服务器我们需要的范围
            $curlOptions[CURLOPT_RANGE] = $start . '-' . $end;
        }

        // 设置cURL的写回调函数，这是流式输出和速率限制的核心
        $curlOptions[CURLOPT_WRITEFUNCTION] = function ($curl, $data) {
            // 直接将cURL获取到的数据块输出到浏览器
            echo $data;
            // 如果输出缓冲区存在，则刷新它
            if (ob_get_level() > 0) {
                // 刷新PHP的输出缓冲区
                ob_flush();
            }
            // 刷新Web服务器（如Nginx, Apache）的缓冲区
            flush();

            // 获取当前数据块的大小
            $chunkSize = strlen($data);
            // 如果设置了速率限制
            if (self::RATE_LIMIT > 0) {
                // 计算传输这个数据块理论上需要的时间
                $expectedTime = $chunkSize / self::RATE_LIMIT;
                // 让脚本暂停相应的时间（单位：微秒），以达到限速效果
                usleep((int)($expectedTime * 1000000));
            }

            // 返回已处理的数据块大小，这是cURL所要求的
            return $chunkSize;
        };
        // 将所有cURL选项应用到句柄上
        curl_setopt_array($ch, $curlOptions);

        // 如果是部分内容请求
        if ($isPartial) {
            // 发送 206 Partial Content 状态码
            header('HTTP/1.1 206 Partial Content');
            // 发送 Content-Range 头，告诉客户端当前发送的是哪一部分
            header(sprintf('Content-Range: bytes %d-%d/%d', $start, $end, $contentLength));
            // 如果是完整请求
        } else {
            // 发送 200 OK 状态码
            header('HTTP/1.1 200 OK');
        }

        // 发送内容类型头
        header('Content-Type: ' . $contentType);

        // 如果长度已知，发送 Content-Length 头
        if ($length !== -1) {
            header('Content-Length: ' . $length);
        }
        // 如果内容长度已知，发送 Accept-Ranges 头，表示服务器支持范围请求
        if ($contentLength > 0) {
            header('Accept-Ranges: bytes');
        }

        // 发送缓存控制头
        header('Cache-Control: public, max-age=86400');

        // 执行cURL请求，数据将通过回调函数直接输出
        curl_exec($ch);
        // 关闭cURL句柄
        curl_close($ch);

        // 终止脚本执行，防止ThinkPHP输出额外内容
        exit;
    }

    /**
     * 获取资源HTTP头信息
     * @param string $url
     * @return array
     * @throws \Exception
     */
    // 定义私有方法 _getResourceHeader 用于获取远程资源的头信息
    private function _getResourceHeader($url)
    {
        // 创建一个新的cURL句柄
        $ch = $this->_createCurlHandle($url);
        // 设置cURL选项，只获取响应头，不获取响应体
        curl_setopt($ch, CURLOPT_NOBODY, true);
        // 必须设置为true，否则header会直接输出
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // 执行cURL请求
        curl_exec($ch);

        // 将获取到的信息存入一个数组
        $info = [
            // 获取HTTP状态码
            'http_code' => curl_getinfo($ch, CURLINFO_HTTP_CODE),
            // 获取下载内容的大小
            'content_length' => (int)curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD),
            // 获取内容的MIME类型
            'content_type' => curl_getinfo($ch, CURLINFO_CONTENT_TYPE),
        ];
        // 如果获取到的内容长度小于0（例如在某些错误情况下），则将其修正为0
        if ($info['content_length'] < 0) {
            $info['content_length'] = 0;
        }
        // 关闭cURL句柄
        curl_close($ch);
        // 返回包含头信息的数组
        return $info;
    }

    /**
     * 检查URL是否安全，防止SSRF
     * @param string $url
     * @return bool
     */
    // 定义私有方法 _isSafeUrl 用于检查URL是否安全
    private function _isSafeUrl($url)
    {
        // 解析URL，只获取主机名部分
        $host = parse_url($url, PHP_URL_HOST);
        // 如果无法解析出主机名，则认为不安全
        if (!$host) {
            return false;
        }

        // 如果主机名本身就是一个IP地址
        if (filter_var($host, FILTER_VALIDATE_IP)) {
            // 直接使用该IP地址进行验证
            $ip = $host;
            // 如果主机名是域名
        } else {
            // 解析域名对应的IP地址
            $ip = gethostbyname($host);
        }

        // 如果 gethostbyname 解析失败，它会原样返回域名
        if ($ip === $host) {
            // 这种情况下认为是不安全的
            return false;
        }

        // 使用 filter_var 验证IP地址是否为公网地址
        // FILTER_FLAG_NO_PRIV_RANGE: 拒绝私有地址 (e.g., 192.168.x.x)
        // FILTER_FLAG_NO_RES_RANGE: 拒绝保留地址 (e.g., 127.0.0.1)
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            // 如果不是公网地址，则认为不安全
            return false;
        }

        // 如果通过所有检查，则认为URL是安全的
        return true;
    }

    /**
     * 创建并返回一个配置好的cURL句柄
     * @param string $url
     * @return resource|\CurlHandle
     * @throws \Exception
     */
    // 定义私有方法 _createCurlHandle 用于创建一个预配置的cURL句柄
    private function _createCurlHandle($url)
    {
        // 初始化一个cURL会话
        $ch = curl_init();
        // 如果初始化失败
        if ($ch === false) {
            // 抛出一个异常
            throw new \Exception('Failed to initialize cURL session.');
        }
        // 定义一个包含通用cURL选项的数组
        $options = [
            // 设置请求的URL
            CURLOPT_URL => $url,
            // 设置为跟随HTTP重定向
            CURLOPT_FOLLOWLOCATION => true,
            // 设置最大重定向次数为5
            CURLOPT_MAXREDIRS => 5,
            // 禁用SSL证书验证（在生产环境中，强烈建议开启并配置CA证书）
            CURLOPT_SSL_VERIFYPEER => false,
            // 禁用SSL主机名验证
            CURLOPT_SSL_VERIFYHOST => false,
            // 设置请求超时时间
            CURLOPT_TIMEOUT => self::TIMEOUT,
            // 设置User-Agent头，优先使用客户端的，如果没有则使用一个默认值
            CURLOPT_USERAGENT => $this->request->header('User-Agent') ?: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        ];
        // 将所有选项应用到cURL句柄上
        curl_setopt_array($ch, $options);
        // 返回配置好的cURL句柄
        return $ch;
    }
}
