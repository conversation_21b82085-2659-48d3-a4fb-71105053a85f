# Change Log

## [1.1.3](https://github.com/guzzle/guzzle-services/tree/1.1.3) (2017-10-06)

[Full Changelog](https://github.com/guzzle/guzzle-services/compare/1.1.2...HEAD)

**Closed issues:**

- Parameter type configuration causes issue when filters change input type [\#147](https://github.com/guzzle/guzzle-services/issues/147)

**Merged pull requests:**

- Use wire name when visiting array [\#152](https://github.com/guzzle/guzzle-services/pull/152) ([my2ter](https://github.com/my2ter))

- Adding descriptive error message on parameter failure [\#144](https://github.com/guzzle/guzzle-services/pull/144) ([igorsantos07](https://github.com/igorsantos07))

## [1.1.2](https://github.com/guzzle/guzzle-services/tree/1.1.2) (2017-05-19)

[Full Changelog](https://github.com/guzzle/guzzle-services/compare/1.1.1...1.1.2)

**Closed issues:**

- Default values ignored in 1.1 [\#146](https://github.com/guzzle/guzzle-services/issues/146)

- Operations extends is broken in 1.1.1 [\#145](https://github.com/guzzle/guzzle-services/issues/145)

## [1.1.1](https://github.com/guzzle/guzzle-services/tree/1.1.1) (2017-05-15)

[Full Changelog](https://github.com/guzzle/guzzle-services/compare/1.1.0...1.1.1)

**Closed issues:**

- Filters are applied twice [\#134](https://github.com/guzzle/guzzle-services/issues/134)

- Is it possible to NOT urlencode a specific uri parameter value? [\#97](https://github.com/guzzle/guzzle-services/issues/97)

**Merged pull requests:**

- Fix minor typos in documentation. [\#139](https://github.com/guzzle/guzzle-services/pull/139) ([forevermatt](https://github.com/forevermatt))

- Do not mutate command at validation [\#135](https://github.com/guzzle/guzzle-services/pull/135) ([danizord](https://github.com/danizord))

- Added tests for JSON array of arrays and array of objects [\#131](https://github.com/guzzle/guzzle-services/pull/131) ([selfcatering](https://github.com/selfcatering))

- Allow filters on response model [\#138](https://github.com/guzzle/guzzle-services/pull/138) ([danizord](https://github.com/danizord))

- Exposing properties to a parent class [\#136](https://github.com/guzzle/guzzle-services/pull/136) ([Napas](https://github.com/Napas))

## [1.1.0](https://github.com/guzzle/guzzle-services/tree/1.1.0) (2017-01-31)

[Full Changelog](https://github.com/guzzle/guzzle-services/compare/1.0.1...1.1.0)

**Closed issues:**

- Grab a list of objects when they are not located at top level of a json response \(HATEOAS\) [\#90](https://github.com/guzzle/guzzle-services/issues/90)

- Regression of Issue \#51 - XmlLocation response not handling multiple tags of the same name correctly [\#82](https://github.com/guzzle/guzzle-services/issues/82)

- PUT requests with parameters with location of "postField" result in Exception [\#78](https://github.com/guzzle/guzzle-services/issues/78)

- Allow to provide Post Body as an Array [\#77](https://github.com/guzzle/guzzle-services/issues/77)

**Merged pull requests:**

- Bring more flexibility to query params serialization [\#132](https://github.com/guzzle/guzzle-services/pull/132) ([bakura10](https://github.com/bakura10))

- Allow to fix validation for parameters with a format [\#130](https://github.com/guzzle/guzzle-services/pull/130) ([bakura10](https://github.com/bakura10))

## [1.0.1](https://github.com/guzzle/guzzle-services/tree/1.0.1) (2017-01-13)

[Full Changelog](https://github.com/guzzle/guzzle-services/compare/1.0.0...1.0.1)

**Implemented enhancements:**

- Set a name when pushing ValidatedDescriptionHandler to stack [\#127](https://github.com/guzzle/guzzle-services/issues/127)

**Fixed bugs:**

- combine method in Uri [\#101](https://github.com/guzzle/guzzle-services/issues/101)

- Undefined Variable [\#88](https://github.com/guzzle/guzzle-services/issues/88)

- Regression in array parameter serialization [\#128](https://github.com/guzzle/guzzle-services/issues/128)

- Unable to POST multiple multipart parameters [\#123](https://github.com/guzzle/guzzle-services/issues/123)

**Closed issues:**

- Tag pre 1.0.0 release [\#121](https://github.com/guzzle/guzzle-services/issues/121)

- Adjust inline documentation of Parameter [\#120](https://github.com/guzzle/guzzle-services/issues/120)

- postField location not recognized after upgrading to 1.0 [\#119](https://github.com/guzzle/guzzle-services/issues/119)

- Create a new release for the guzzle6 branch [\#118](https://github.com/guzzle/guzzle-services/issues/118)

- Compatibility problem with PHP7.0 ? [\#116](https://github.com/guzzle/guzzle-services/issues/116)

- What is the correct type of Parameter static option [\#113](https://github.com/guzzle/guzzle-services/issues/113)

- Improve the construction of baseUri in Description [\#112](https://github.com/guzzle/guzzle-services/issues/112)

- Please create version tag for current master branch [\#110](https://github.com/guzzle/guzzle-services/issues/110)

- Problems with postField params [\#98](https://github.com/guzzle/guzzle-services/issues/98)

**Merged pull requests:**

- Fix serialization of query params [\#129](https://github.com/guzzle/guzzle-services/pull/129) ([bakura10](https://github.com/bakura10))

## [1.0.0](https://github.com/guzzle/guzzle-services/tree/1.0.0) (2016-11-24)

[Full Changelog](https://github.com/guzzle/guzzle-services/compare/0.6.0...1.0.0)

**Closed issues:**

- AbstractClient' not found [\#117](https://github.com/guzzle/guzzle-services/issues/117)

**Merged pull requests:**

- Make Guzzle Services compatible with Guzzle6 [\#109](https://github.com/guzzle/guzzle-services/pull/109) ([Konafets](https://github.com/Konafets))

## [0.6.0](https://github.com/guzzle/guzzle-services/tree/0.6.0) (2016-10-21)

[Full Changelog](https://github.com/guzzle/guzzle-services/compare/0.5.0...0.6.0)

**Closed issues:**

- Broken composer install [\#111](https://github.com/guzzle/guzzle-services/issues/111)

- The visit\(\) method is expected to return a RequestInterface but it doesn't in JsonLocation [\#106](https://github.com/guzzle/guzzle-services/issues/106)

- Allow parameters in baseUrl [\#102](https://github.com/guzzle/guzzle-services/issues/102)

- Have default params at client construction, gone away? [\#100](https://github.com/guzzle/guzzle-services/issues/100)

- Runtime Exception Error is always empty [\#99](https://github.com/guzzle/guzzle-services/issues/99)

- PHP Fatal error:  Unsupported operand types in guzzlehttp/guzzle-services/src/GuzzleClient.php on line 72 [\#95](https://github.com/guzzle/guzzle-services/issues/95)

- Date of next version [\#94](https://github.com/guzzle/guzzle-services/issues/94)

- Map null reponse values to defined reponse model properties [\#91](https://github.com/guzzle/guzzle-services/issues/91)

- Map a json-array into a Model [\#80](https://github.com/guzzle/guzzle-services/issues/80)

- If property specified in json model but empty, notice raised [\#75](https://github.com/guzzle/guzzle-services/issues/75)

- Allow primitive response types for operations [\#73](https://github.com/guzzle/guzzle-services/issues/73)

- Allow shortened definition of properties in models [\#71](https://github.com/guzzle/guzzle-services/issues/71)

- Where's the ServiceDescriptionLoader/AbstractConfigLoader? [\#68](https://github.com/guzzle/guzzle-services/issues/68)

- errorResposnes from operation is never used [\#66](https://github.com/guzzle/guzzle-services/issues/66)

- Updating the description  [\#65](https://github.com/guzzle/guzzle-services/issues/65)

- Parameter type validation is too strict [\#7](https://github.com/guzzle/guzzle-services/issues/7)

**Merged pull requests:**

- fix code example [\#115](https://github.com/guzzle/guzzle-services/pull/115) ([snoek09](https://github.com/snoek09))

- Bug Fix for GuzzleClient constructor [\#96](https://github.com/guzzle/guzzle-services/pull/96) ([peterfox](https://github.com/peterfox))

- add plugin section to readme [\#93](https://github.com/guzzle/guzzle-services/pull/93) ([gimler](https://github.com/gimler))

- Allow mapping null response values to defined response model properties [\#92](https://github.com/guzzle/guzzle-services/pull/92) ([shaun785](https://github.com/shaun785))

- Updated exception message for better debugging [\#85](https://github.com/guzzle/guzzle-services/pull/85) ([stovak](https://github.com/stovak))

- Gracefully handle null return from $this-\>getConfig\('defaults'\) [\#84](https://github.com/guzzle/guzzle-services/pull/84) ([fuhry](https://github.com/fuhry))

- Fixing issue \#82 to address regression for handling elements with the sa... [\#83](https://github.com/guzzle/guzzle-services/pull/83) ([sprak3000](https://github.com/sprak3000))

- Fix for specified property but no value in json \(notice for undefined in... [\#76](https://github.com/guzzle/guzzle-services/pull/76) ([rfink](https://github.com/rfink))

- Add ErrorHandler subscriber [\#67](https://github.com/guzzle/guzzle-services/pull/67) ([bakura10](https://github.com/bakura10))

- Fix combine base url and command uri [\#108](https://github.com/guzzle/guzzle-services/pull/108) ([vlastv](https://github.com/vlastv))

- Fixing JsonLocation::visit\(\) not returning a request \#106 [\#107](https://github.com/guzzle/guzzle-services/pull/107) ([Pinolo](https://github.com/Pinolo))

- Fix call to undefined method "GuzzleHttp\Psr7\Uri::combine" [\#105](https://github.com/guzzle/guzzle-services/pull/105) ([horrorin](https://github.com/horrorin))

- fix description for get request example [\#87](https://github.com/guzzle/guzzle-services/pull/87) ([snoek09](https://github.com/snoek09))

- Allow raw values \(non array/object\) for root model definitions [\#74](https://github.com/guzzle/guzzle-services/pull/74) ([rfink](https://github.com/rfink))

- Allow shortened definition of properties by assigning them directly to a type [\#72](https://github.com/guzzle/guzzle-services/pull/72) ([rfink](https://github.com/rfink))

## [0.5.0](https://github.com/guzzle/guzzle-services/tree/0.5.0) (2014-12-23)

[Full Changelog](https://github.com/guzzle/guzzle-services/compare/0.4.0...0.5.0)

**Closed issues:**

- Does it supports custom class instantiate to define an operation using a service description [\#62](https://github.com/guzzle/guzzle-services/issues/62)

- Tag version 0.4.0 [\#61](https://github.com/guzzle/guzzle-services/issues/61)

- XmlLocation not adding attributes to non-leaf child nodes [\#52](https://github.com/guzzle/guzzle-services/issues/52)

- XmlLocation response not handling multiple tags of the same name correctly [\#51](https://github.com/guzzle/guzzle-services/issues/51)

- Validation Bug [\#47](https://github.com/guzzle/guzzle-services/issues/47)

- CommandException doesn't contain response data [\#44](https://github.com/guzzle/guzzle-services/issues/44)

- \[Fix included\] XmlLocation requires text value to have attributes [\#37](https://github.com/guzzle/guzzle-services/issues/37)

- Question: Mocking a Response does not throw exception [\#35](https://github.com/guzzle/guzzle-services/issues/35)

- allow default 'location' on Model [\#26](https://github.com/guzzle/guzzle-services/issues/26)

- create mock subscriber requests from descriptions [\#25](https://github.com/guzzle/guzzle-services/issues/25)

**Merged pull requests:**

- Documentation: Add 'boolean-string' as a supported "format" value [\#63](https://github.com/guzzle/guzzle-services/pull/63) ([jwcobb](https://github.com/jwcobb))

## [0.4.0](https://github.com/guzzle/guzzle-services/tree/0.4.0) (2014-11-03)

[Full Changelog](https://github.com/guzzle/guzzle-services/compare/0.3.0...0.4.0)

**Closed issues:**

- Exceptions Thrown From Subscribers Are Ignored? [\#58](https://github.com/guzzle/guzzle-services/issues/58)

- Totally Broken With Guzzle 5 [\#57](https://github.com/guzzle/guzzle-services/issues/57)

- GuzzleHTTP/Command Dependency fail [\#50](https://github.com/guzzle/guzzle-services/issues/50)

- Request parameter PathLocation [\#46](https://github.com/guzzle/guzzle-services/issues/46)

- Requesting a new version tag [\#45](https://github.com/guzzle/guzzle-services/issues/45)

- CommandException expects second parameter to be CommandTransaction instance  [\#43](https://github.com/guzzle/guzzle-services/issues/43)

- Cannot add Autorization header to my requests [\#39](https://github.com/guzzle/guzzle-services/issues/39)

- Resouce Itterators [\#36](https://github.com/guzzle/guzzle-services/issues/36)

- Question [\#33](https://github.com/guzzle/guzzle-services/issues/33)

- query location array can be comma separated [\#31](https://github.com/guzzle/guzzle-services/issues/31)

- Automatically returns array from command? [\#30](https://github.com/guzzle/guzzle-services/issues/30)

- Arrays nested under objects in JSON response broken? [\#27](https://github.com/guzzle/guzzle-services/issues/27)

- Question? [\#23](https://github.com/guzzle/guzzle-services/issues/23)

**Merged pull requests:**

- Bump the version in the readme [\#60](https://github.com/guzzle/guzzle-services/pull/60) ([GrahamCampbell](https://github.com/GrahamCampbell))

- Bump the next version to 0.4 [\#56](https://github.com/guzzle/guzzle-services/pull/56) ([GrahamCampbell](https://github.com/GrahamCampbell))

- Fixed the guzzlehttp/command version constraint [\#55](https://github.com/guzzle/guzzle-services/pull/55) ([GrahamCampbell](https://github.com/GrahamCampbell))

- Work with latest Guzzle 5 and Command updates [\#54](https://github.com/guzzle/guzzle-services/pull/54) ([mtdowling](https://github.com/mtdowling))

- Addressing Issue \#51 & Issue \#52 [\#53](https://github.com/guzzle/guzzle-services/pull/53) ([sprak3000](https://github.com/sprak3000))

- added description interface to extend it [\#49](https://github.com/guzzle/guzzle-services/pull/49) ([danieledangeli](https://github.com/danieledangeli))

- Update readme to improve documentation \(\#46\) [\#48](https://github.com/guzzle/guzzle-services/pull/48) ([bonndan](https://github.com/bonndan))

- Fixed the readme version constraint [\#42](https://github.com/guzzle/guzzle-services/pull/42) ([GrahamCampbell](https://github.com/GrahamCampbell))

- Update .travis.yml [\#41](https://github.com/guzzle/guzzle-services/pull/41) ([GrahamCampbell](https://github.com/GrahamCampbell))

- Added a branch alias [\#40](https://github.com/guzzle/guzzle-services/pull/40) ([GrahamCampbell](https://github.com/GrahamCampbell))

- Fixes Response\XmlLocation requires text value [\#38](https://github.com/guzzle/guzzle-services/pull/38) ([magnetik](https://github.com/magnetik))

- Removing unnecessary \(\) from docblock [\#32](https://github.com/guzzle/guzzle-services/pull/32) ([jamiehannaford](https://github.com/jamiehannaford))

- Fix JSON response location so that both is supported: arrays nested unde... [\#28](https://github.com/guzzle/guzzle-services/pull/28) ([ukautz](https://github.com/ukautz))

- Throw Any Exceptions On Process [\#59](https://github.com/guzzle/guzzle-services/pull/59) ([GrahamCampbell](https://github.com/GrahamCampbell))

- Allow extension to work recursively over models [\#34](https://github.com/guzzle/guzzle-services/pull/34) ([jamiehannaford](https://github.com/jamiehannaford))

- A custom class can be configured for command instances. [\#29](https://github.com/guzzle/guzzle-services/pull/29) ([robinvdvleuten](https://github.com/robinvdvleuten))

- \[WIP\] doing some experimentation [\#24](https://github.com/guzzle/guzzle-services/pull/24) ([cordoval](https://github.com/cordoval))

## [0.3.0](https://github.com/guzzle/guzzle-services/tree/0.3.0) (2014-06-01)

[Full Changelog](https://github.com/guzzle/guzzle-services/compare/0.2.0...0.3.0)

**Closed issues:**

- Testing Guzzle Services doesn't work [\#19](https://github.com/guzzle/guzzle-services/issues/19)

- Description factory [\#18](https://github.com/guzzle/guzzle-services/issues/18)

- support to load service description from file [\#15](https://github.com/guzzle/guzzle-services/issues/15)

- Update dependency on guzzlehttp/command [\#11](https://github.com/guzzle/guzzle-services/issues/11)

**Merged pull requests:**

- Add license file [\#22](https://github.com/guzzle/guzzle-services/pull/22) ([siwinski](https://github.com/siwinski))

- Fix 'Invalid argument supplied for foreach\(\)' [\#21](https://github.com/guzzle/guzzle-services/pull/21) ([Olden](https://github.com/Olden))

- Fixed string zero \('0'\) values not being filtered in XML. [\#20](https://github.com/guzzle/guzzle-services/pull/20) ([dragonwize](https://github.com/dragonwize))

- baseUrl can be a string or an uri template [\#16](https://github.com/guzzle/guzzle-services/pull/16) ([robinvdvleuten](https://github.com/robinvdvleuten))

## [0.2.0](https://github.com/guzzle/guzzle-services/tree/0.2.0) (2014-03-30)

[Full Changelog](https://github.com/guzzle/guzzle-services/compare/0.1.0...0.2.0)

**Closed issues:**

- please remove wiki [\#13](https://github.com/guzzle/guzzle-services/issues/13)

- Parameter validation fails for union types [\#12](https://github.com/guzzle/guzzle-services/issues/12)

- question on integration with Guzzle4 [\#8](https://github.com/guzzle/guzzle-services/issues/8)

- typehints for operations property [\#6](https://github.com/guzzle/guzzle-services/issues/6)

- improve exception message [\#5](https://github.com/guzzle/guzzle-services/issues/5)

**Merged pull requests:**

- Update composer.json [\#14](https://github.com/guzzle/guzzle-services/pull/14) ([GrahamCampbell](https://github.com/GrahamCampbell))

- Update composer.json [\#9](https://github.com/guzzle/guzzle-services/pull/9) ([GrahamCampbell](https://github.com/GrahamCampbell))

- some fixes [\#4](https://github.com/guzzle/guzzle-services/pull/4) ([cordoval](https://github.com/cordoval))

- Fix the CommandException path used in ValidateInput [\#2](https://github.com/guzzle/guzzle-services/pull/2) ([mookle](https://github.com/mookle))

- Minor improvements [\#1](https://github.com/guzzle/guzzle-services/pull/1) ([GrahamCampbell](https://github.com/GrahamCampbell))

- Use latest guzzlehttp/command to fix dependencies [\#10](https://github.com/guzzle/guzzle-services/pull/10) ([sbward](https://github.com/sbward))

- some collaboration using Gush :\) [\#3](https://github.com/guzzle/guzzle-services/pull/3) ([cordoval](https://github.com/cordoval))

## [0.1.0](https://github.com/guzzle/guzzle-services/tree/0.1.0) (2014-03-15)



\* *This Change Log was automatically generated by [github_changelog_generator](https://github.com/skywinder/Github-Changelog-Generator)*