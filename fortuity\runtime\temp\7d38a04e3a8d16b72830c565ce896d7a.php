<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:90:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/compass/dominator.html";i:1749696397;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755516152;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a></li>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;position:relative;}.tpl-portlet-components:after{content:"";display:table;clear:both;}.am-form{position:relative;overflow:visible;}.portlet-title{display:flex;justify-content:space-between;align-items:center;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid #f0f0f0;}.caption{font-size:16px;color:#23b7e5;font-weight:500;}.caption .am-icon-google-wallet{margin-right:5px;color:#23b7e5;}.tpl-portlet-input{position:relative;}.tpl-portlet-input input{height:32px;width:200px;padding:0 30px 0 10px;border:1px solid #e8e8e8;border-radius:4px;background:#fafafa;transition:all 0.3s;}.tpl-portlet-input input:focus{border-color:#23b7e5;background:#fff;box-shadow:0 0 0 2px rgba(35,183,229,0.1);}.tpl-portlet-input .am-icon-search{position:absolute;right:10px;top:50%;transform:translateY(-50%);color:#999;cursor:pointer;}.action-btn{display:inline-block;padding:4px 8px;background:#fff;border:1px solid #ddd;color:#23b7e5;border-radius:3px;font-size:12px;cursor:pointer;transition:all 0.3s;margin:2px;}.action-btn:hover{border-color:#23b7e5;color:#23b7e5;background-color:#f5fafd;}.am-table{border:1px solid #f0f0f0;border-radius:4px;font-size:13px;margin-bottom:15px;}.am-table>thead:first-child>tr:first-child>th{background-color:#f9f9f9;border-bottom:1px solid #eee;color:#333;font-weight:500;font-size:13px;text-align:center;padding:10px 8px;}.am-table>tbody>tr>td{padding:10px 8px;border-top:1px solid #f3f3f3;text-align:center;vertical-align:middle;color:#666;line-height:1.6;position:relative;}.am-table-striped>tbody>tr:nth-child(odd)>td{background-color:#fafafa;}.am-table>tbody>tr:hover>td{background-color:#f5fafd;}.am-modal-dialog{border-radius:4px;overflow:hidden;box-shadow:0 5px 15px rgba(0,0,0,0.2);}.am-modal-hd{background:#f9f9f9;padding:10px 15px;border-bottom:1px solid #eee;}.am-modal-bd{padding:15px;text-align:center;}.am-form-group{margin-bottom:15px;}.am-form-label{font-size:13px;color:#666;font-weight:normal;line-height:32px;text-align:right;padding:0;}.tpl-form-input{height:32px;padding:6px 10px;font-size:13px;border:1px solid #e8e8e8;border-radius:3px;transition:all 0.3s;width:100%;}.tpl-form-input:focus{border-color:#23b7e5;box-shadow:0 0 0 2px rgba(35,183,229,0.1);}.confirm-btn{background:#23b7e5;color:white;border:none;border-radius:3px;padding:6px 15px;font-size:13px;cursor:pointer;transition:all 0.3s;}.confirm-btn:hover{background:#1a9fd4;box-shadow:0 2px 5px rgba(26,159,212,0.2);}select.tpl-form-input{padding-top:0;padding-bottom:0;line-height:32px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-google-wallet"></span> <?php echo emoji_decode($toryInfo['realm_name']); ?> 管理团队列表
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <a href="javascript:void(0);" class="am-btn am-btn-success am-btn-sm" onclick="clearText();"
                        data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 400, height: 295}">
                        <span class="am-icon-plus"></span> 任命用户职位
                    </a>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                            <tr>
                                <th width="20%">用户</th>
                                <th width="20%">昵称</th>
                                <th width="20%">openid</th>
                                <th width="20%">职位</th>
                                <th width="20%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(is_array($tylBulordInfo) || $tylBulordInfo instanceof \think\Collection || $tylBulordInfo instanceof \think\Paginator): $i = 0; $__LIST__ = $tylBulordInfo;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <tr>
                                <td>
                                    <?php if($vo['user_head_sculpture'] && $vo['user_head_sculpture']
                                    !='/yl_welore/style/icon/default.png'): ?>
                                    <img src="<?php echo $vo['user_head_sculpture']; ?>"
                                        style="width: 40px;height: 40px;border-radius: 50%;">
                                    <?php else: ?>
                                    <img src="<?php echo urlBridging('static/disappear/tourist.png'); ?>"
                                        style="width: 40px;height: 40px;border-radius: 50%;">
                                    <?php endif; ?>
                                </td>
                                <td><?php echo emoji_decode($vo['user_nick_name']); ?></td>
                                <td><?php echo $vo['user_wechat_open_id']; ?></td>
                                <td>
                                    <span class="am-badge am-badge-primary">圈主</span>
                                </td>
                                <td>
                                    <button type="button" class="action-btn am-btn-secondary"
                                        onclick="sendGifts('<?php echo $toryInfo['id']; ?>',1,0,'<?php echo $vo['user_wechat_open_id']; ?>');">
                                        取消任命
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; endif; else: echo "" ;endif; if(is_array($tylSulordInfo) || $tylSulordInfo instanceof \think\Collection || $tylSulordInfo instanceof \think\Paginator): $i = 0; $__LIST__ = $tylSulordInfo;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <tr>
                                <td>
                                    <img src="<?php echo $vo['user_head_sculpture']; ?>"
                                        style="width:40px;height:40px;border-radius:50%;">
                                </td>
                                <td><?php echo emoji_decode($vo['user_nick_name']); ?></td>
                                <td><?php echo $vo['user_wechat_open_id']; ?></td>
                                <td>
                                    <span class="am-badge am-badge-secondary">管理员</span>
                                </td>
                                <td>
                                    <button type="button" class="action-btn am-btn-secondary"
                                        onclick="sendGifts('<?php echo $toryInfo['id']; ?>',1,1,'<?php echo $vo['user_wechat_open_id']; ?>');">
                                        取消任命
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; endif; else: echo "" ;endif; if(is_array($tylSnviteBulordInfo) || $tylSnviteBulordInfo instanceof \think\Collection || $tylSnviteBulordInfo instanceof \think\Paginator): $cursor = 0; $__LIST__ = $tylSnviteBulordInfo;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($cursor % 2 );++$cursor;?>
                            <tr>
                                <td>
                                    <img src="<?php echo $vo['user_head_sculpture']; ?>"
                                        style="width:40px;height:40px;border-radius:50%;">
                                </td>
                                <td><?php echo emoji_decode($vo['user_nick_name']); ?></td>
                                <td><?php echo $vo['user_wechat_open_id']; ?></td>
                                <td>
                                    <span class="am-badge am-badge-warning" title="点击查看申请理由" style="cursor: pointer;"
                                        onclick="reasonForApplication('<?php echo $tylSnviteBulord[$cursor - 1]['upshot']; ?>','<?php echo date('Y-m-d H:i:s',$tylSnviteBulord[$cursor - 1]['time']); ?>');">
                                        圈主 (待审)
                                    </span>
                                </td>
                                <td>
                                    <button type="button" class="action-btn am-btn-success"
                                        onclick="certificationAuthority('<?php echo $toryInfo['id']; ?>',0,0,'<?php echo $tylSnviteBulord[$cursor - 1]['openid']; ?>');">
                                        通过
                                    </button>
                                    <button type="button" class="action-btn am-btn-danger"
                                        onclick="certificationAuthority('<?php echo $toryInfo['id']; ?>',1,0,'<?php echo $tylSnviteBulord[$cursor - 1]['openid']; ?>');">
                                        拒绝
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; endif; else: echo "" ;endif; if(is_array($tylEnviteSulordInfo) || $tylEnviteSulordInfo instanceof \think\Collection || $tylEnviteSulordInfo instanceof \think\Paginator): $cursor = 0; $__LIST__ = $tylEnviteSulordInfo;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($cursor % 2 );++$cursor;?>
                            <tr>
                                <td>
                                    <img src="<?php echo $vo['user_head_sculpture']; ?>"
                                        style="width:40px;height:40px;border-radius:50%;">
                                </td>
                                <td><?php echo emoji_decode($vo['user_nick_name']); ?></td>
                                <td><?php echo $vo['user_wechat_open_id']; ?></td>
                                <td>
                                    <span class="am-badge am-badge-warning" title="点击查看申请理由" style="cursor: pointer;"
                                        onclick="reasonForApplication('<?php echo $tylEnviteSulord[$cursor - 1]['upshot']; ?>','<?php echo date('Y-m-d H:i:s',$tylEnviteSulord[$cursor - 1]['time']); ?>');">
                                        管理员 (待审)
                                    </span>
                                </td>
                                <td>
                                    <button type="button" class="action-btn am-btn-success"
                                        onclick="certificationAuthority('<?php echo $toryInfo['id']; ?>',0,1,'<?php echo $tylEnviteSulord[$cursor - 1]['openid']; ?>');">
                                        通过
                                    </button>
                                    <button type="button" class="action-btn am-btn-danger"
                                        onclick="certificationAuthority('<?php echo $toryInfo['id']; ?>',1,1,'<?php echo $tylEnviteSulord[$cursor - 1]['openid']; ?>');">
                                        拒绝
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
        <div class="am-modal-dialog">
            <div class="am-modal-hd">
                <span>任命职位</span>
                <a id="euModalClose" href="javascript: void(0);" class="am-close am-close-spin"
                    data-am-modal-close>&times;</a>
            </div>
            <input id="virtual-user" type="hidden" value="0" />
            <div class="am-modal-bd am-form">
                <div class="am-form-group am-g">
                    <label class="am-u-sm-3 am-form-label">用户信息</label>
                    <div class="am-u-sm-9">
                        <input type="text" id="openid" oninput="extolled(this);" class="tpl-form-input"
                            placeholder="请输入用户openid">
                        <span id="sehred"
                            style="display:block;position:static;margin-top:4px;color:blue;font-size:12px;">　</span>
                    </div>
                </div>
                <div class="am-form-group am-g">
                    <label class="am-u-sm-3 am-form-label">圈子职位</label>
                    <div class="am-u-sm-9">
                        <select id="job" class="tpl-form-input">
                            <option value="0">圈主</option>
                            <option value="1">管理员</option>
                        </select>
                    </div>
                </div>
                <div class="am-u-sm-12" style="margin-top:15px;text-align: center;">
                    <button type="button" class="confirm-btn" onclick="sendGifts('<?php echo $toryInfo['id']; ?>',0,null,null);">
                        确定任命
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<?php if($much_role=='founder'): ?>
<script>
    var updateFix = function () {
        layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("<?php echo url('index/repairMissing'); ?>", function (data) {
                if (data !== false) {
                    layer.msg('数据库缺失字段修复完成', {time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('修复失败', {time: 1600}, function () {
                        location.reload();
                    });
                }
            });
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }
</script>
<?php endif; ?>
<script>
    new Vue({
        el: '#menu',
        data() {
            return {
                menuActive: '<?php echo $menuActive; ?>',
                menuList: []
            };
        },
        created() {
            const menuList = '<?php echo $menuList; ?>';
            if (menuList !== '') {
                const menuData = JSON.parse(decodeURIComponent(atob(menuList)));
                for (let i = 0; i < menuData.length; i++) {
                    if (menuData[i].pid === 0) {
                        menuData[i].childMenuItems = [];
                        this.menuList.push(menuData[i]);
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        if (menuData[i].pid === this.menuList[j].id) {
                            const childItem = JSON.parse(JSON.stringify(menuData[i]));
                            childItem.grandsonList = [];
                            this.menuList[j].childMenuItems.push(childItem);
                            break;
                        }
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        for (let k = 0; k < this.menuList[j].childMenuItems.length; k++) {
                            if (menuData[i].pid === this.menuList[j].childMenuItems[k].id) {
                                this.menuList[j].childMenuItems[k].grandsonList.push(menuData[i]);
                                break;
                            }
                        }
                    }
                }
            }
        }
    });

    $(function () {
        setInterval(reballot, 15000);
    });

    var reballot = function () {
        $.getJSON("<?php echo url('ordinary'); ?>", function (data) {
            var i = 0;
            if (data.notice > 0 || data.vacant > 0) {
                i++;
            }
            $('#notice-0,#notice-1').text(data.notice);
            $('#vacant-0,#vacant-1').text(data.vacant);
            if ((data.notice + data.vacant) <= data.preCount) {
                i = 0;
            } else {
                $.post("<?php echo url('receipt'); ?>", {'multiply': (data.notice + data.vacant)});
            }
            if (i > 0) {
                var player = $("#backPlayer")[0];
                player.play();
            }
        });
    }

    var retakeCache = function () {
        $.get("<?php echo url('index/purgeCache'); ?>", function () {
            layer.msg('缓存清理完成', {time: 1000}, function () {
                location.reload();
            });
        });
    }
</script>
<script>
// 添加全局变量
window.EDITOR_MODE = false;  // 标记是否在编辑器模式

// AJAX 拦截器 - 自动还原代理 URL 为原始 URL
(function() {
    // 代理 URL 的匹配模式
    var proxyUrlPattern = /[?&]url=([^&]+)/;
    var proxyBaseUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>";

    // URL 还原函数
    function restoreOriginalUrl(url) {
        try {
            // 检查是否是代理 URL
            if (typeof url === 'string' && url.indexOf(proxyBaseUrl) !== -1) {
                var match = url.match(proxyUrlPattern);
                if (match && match[1]) {
                    // 解码并返回原始 URL
                    return decodeURIComponent(match[1]);
                }
            }
            return url;
        } catch (e) {
            // 出错时返回原始值，确保不影响正常功能
            console.warn('URL restore failed:', e);
            return url;
        }
    }

    // 增强版 URL 还原函数 - 支持从 DOM 元素的 data 属性获取原始 URL
    window.getOriginalSrc = function(element) {
        try {
            if (element && element.getAttribute) {
                // 优先从 data-original-src 获取
                var originalSrc = element.getAttribute('data-original-src');
                if (originalSrc) {
                    return originalSrc;
                }
                // 其次尝试从当前 src 还原
                var currentSrc = element.src || element.getAttribute('src');
                return restoreOriginalUrl(currentSrc);
            }
            return element;
        } catch (e) {
            console.warn('getOriginalSrc failed:', e);
            return element;
        }
    };

    // 递归处理数据对象，还原其中的代理 URL
    function restoreDataUrls(data) {
        try {
            if (typeof data === 'string') {
                return restoreOriginalUrl(data);
            } else if (Array.isArray(data)) {
                return data.map(restoreDataUrls);
            } else if (data && typeof data === 'object') {
                var restored = {};
                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        restored[key] = restoreDataUrls(data[key]);
                    }
                }
                return restored;
            }
            return data;
        } catch (e) {
            // 出错时返回原始数据
            console.warn('Data restore failed:', e);
            return data;
        }
    }

    // 保存原始的 jQuery AJAX 方法
    var originalAjax = $.ajax;
    var originalPost = $.post;
    var originalGet = $.get;

    // 重写 $.ajax
    $.ajax = function(options) {
        try {
            if (options && options.data) {
                options.data = restoreDataUrls(options.data);
            }
        } catch (e) {
            console.warn('AJAX data restore failed:', e);
        }
        return originalAjax.apply(this, arguments);
    };

    // 重写 $.post
    $.post = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('POST data restore failed:', e);
        }
        return originalPost.call(this, url, data, success, dataType);
    };

    // 重写 $.get
    $.get = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('GET data restore failed:', e);
        }
        return originalGet.call(this, url, data, success, dataType);
    };
})();

// 修改检测函数
function checkResourceAccess() {
    var isHttps = window.location.protocol === 'https:';
    
    if (isHttps) {
        // 处理图片
        document.querySelectorAll('img[src^="http://"]').forEach(function(img) {
            // 如果是编辑器模式，所有图片都使用代理URL显示
            if(window.EDITOR_MODE) {
                var originalSrc = img.getAttribute('data-src') || img.src;
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
                return;
            }
            
            // 非编辑器模式下排除富文本编辑器内的图片
            if(img.closest('.w-e-text') || img.closest('.w-e-text-container')) {
                return;
            }
            
            var originalSrc = img.getAttribute('data-src') || img.src;
            var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);

            var tmpImg = new Image();
            tmpImg.onload = function() {
                // 原始图片可访问，不做处理
            }
            tmpImg.onerror = function() {
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = proxyUrl;
            }
            tmpImg.src = originalSrc;
        });
        
        // 处理视频
        document.querySelectorAll('video').forEach(function(video) {
            // 处理video的src属性
            if (video.src && video.src.startsWith('http://')) {
                handleResource(video, 'src');
            }
            
            // 处理source标签
            video.querySelectorAll('source[src^="http://"]').forEach(function(source) {
                handleResource(source, 'src');
            });
        });
    }
}

// 统一的资源处理函数
function handleResource(element, srcAttr) {
    var originalSrc = element[srcAttr];
    var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
    
    // 创建临时对象测试资源是否可访问
    var tmpObj = new Image(); // 用Image对象测试连接可用性
    tmpObj.onload = function() {
        // 原始资源可访问，不做处理
    }
    tmpObj.onerror = function() {
        // 原始资源不可访问，切换到代理
        // 存储原始 URL 到 data 属性
        element.setAttribute('data-original-src', originalSrc);
        element[srcAttr] = proxyUrl;

        // 同时更新父级a标签的href
        var parent = element.parentElement;
        if (parent && parent.tagName === 'A') {
            parent.setAttribute('data-original-href', parent.href);
            parent.href = proxyUrl;
        }
    }
    tmpObj.src = originalSrc;
}

// 页面加载完成后执行检查
window.addEventListener('load', checkResourceAccess);

// 动态加载的内容处理
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            checkResourceAccess();
        }
    });
});

// 监听DOM变化
observer.observe(document.body, {
    childList: true,
    subtree: true
});
</script>

<script>

    var clearText = function () {
        $('#openid').val('');
        $('#sehred').text('');
    }

    var extolled = function (obj) {
        obj.value = $.trim(obj.value);
        if (obj.value != '') {
            $.getJSON("<?php echo url('compass/getopenid'); ?>&virtual=1", { "openid": obj.value }, function (data) {
                if (data.name != '') {
                    $('#sehred').css('color', 'blue');
                    $('#sehred').text(data.name);
                } else {
                    $('#sehred').css('color', 'red');
                    $('#sehred').text('\u7528\u6237\u006f\u0070\u0065\u006e\u0069\u0064\u8f93\u5165\u9519\u8bef');
                }
            });
        }
    }

    var sendGifts = function (tyid, type, job, openid) {
        switch (type) {
            case 0:
                openid = $.trim($('#openid').val());
                if (openid == '') {
                    layer.msg('用户openid不能为空');
                    return;
                }
                job = $('#job').val();
                $.post("<?php echo url('compass/nominate'); ?>", { tyid, type, job, openid }, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, { icon: 5, time: 2000 });
                    }
                }, 'json');
                break;
            case 1:
                layer.prompt({ title: '请输入取消任命原因 : ' }, function (reason, index) {
                    if ($.trim(reason) == '') {
                        return;
                    }
                    $.post("<?php echo url('compass/nominate'); ?>", { tyid, type, job, openid, reason }, function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, { icon: 5, time: 2000 });
                        }
                    }, 'json');
                });
                break;
        }
    }


    var reasonForApplication = function (justification, time) {
        if ($.trim(justification) == '') {
            justification = '申请理由：该用户没有输入申请理由！';
        } else {
            justification = '申请理由：' + justification;
        }
        layer.alert(justification, { title: '职位申请时间：' + time });
    }

    var certificationAuthority = function (tyid, genre, type, openid) {
        switch (genre) {
            case 0:
                layer.confirm('您确定要同意此用户的申请请求吗？', {
                    btn: ['确定', '取消'], title: '提示'
                }, function () {
                    $.post("<?php echo url('compass/turnover'); ?>", { tyid, genre, type, openid }, function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, { icon: 5, time: 2000 });
                        }
                    }, 'json');
                }, function (index) {
                    layer.close(index);
                });
                break;
            case 1:
                layer.prompt({ title: '请输入拒绝原因 : ' }, function (reason, index) {
                    if ($.trim(reason) == '') {
                        return;
                    }
                    $.post("<?php echo url('compass/turnover'); ?>", { tyid, genre, type, openid, reason }, function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, { icon: 5, time: 2000 });
                        }
                    }, 'json');
                });
                break;
        }
    }
</script>

</body>
</html>