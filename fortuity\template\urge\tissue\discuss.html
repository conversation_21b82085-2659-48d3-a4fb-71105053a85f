{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-comment-o {margin-right: 5px;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}

    .batch-actions-group > .am-btn { border-radius: 3px;}
    .batch-actions-group > .am-btn + .am-btn { margin-left: 8px; }

    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-top: 15px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}

    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a, .am-pagination > .am-active > a:hover {background-color: #23b7e5;border-color: #23b7e5;color:#fff;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}

    .action-dropdown .am-dropdown-content { font-size: 12px; }
    .action-dropdown .am-dropdown-content > li > a { color: #333; }
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-comment-o"></span> 评论列表
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索评论或用户...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12" style="text-align: right;">
                <div class="am-btn-group am-btn-group-sm batch-actions-group">
                    <button type="button" class="am-btn am-btn-success" onclick="batchReviewProve(1);">批量通过</button>
                    <button type="button" class="am-btn am-btn-secondary" onclick="batchReviewProve(2);">批量拒绝</button>
                    <button type="button" class="am-btn am-btn-danger" onclick="delDiscuss(1);">批量删除</button>
                </div>
            </div>
        </div>

        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="5%">
                                <input id="withole" type="checkbox" class="tpl-table-fz-check">全选
                            </th>
                            <th width="8%">ID</th>
                            <th width="12%">用户昵称</th>
                            <th width="25%">评论内容</th>
                            <th width="15%">评论时间</th>
                            <th width="10%">审核状态</th>
                            <th width="15%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                <input type="checkbox" class="tpl-table-fz-check elctive" value="{$vo.id}">
                            </td>
                            <td>{$vo.id}</td>
                            <td>
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode|subtext=10}
                                </a>
                            </td>
                            <td>{$vo.duplex_content|emoji_decode|strip_tags}</td>
                            <td>
                                {:date('Y-m-d H:i:s',$vo.duplex_time)}
                            </td>
                            <td>
                                {if $vo.duplex_status == 0}
                                <span class="am-text-warning">待审核</span>
                                {elseif $vo.duplex_status == 1}
                                <span class="am-text-success">已通过</span>
                                {elseif $vo.duplex_status == 2}
                                <span class="am-text-danger">未通过</span>
                                {/if}
                            </td>
                            <td>
                                <div class="am-dropdown am-dropdown-up action-dropdown" data-am-dropdown>
                                    <button class="am-btn am-btn-default am-btn-xs am-dropdown-toggle" data-am-dropdown-toggle>
                                        <span class="am-icon-cog"></span> 操作 <span class="am-icon-caret-down"></span>
                                    </button>
                                    <ul class="am-dropdown-content">
                                        <li>
                                            <a href="javascript:void(0);" onclick="uploof('{$vo.reply_id}');">
                                                <i class="am-icon-search"></i> 回复详情
                                            </a>
                                        </li>
                                        {if $vo.duplex_status == 0}
                                        <li>
                                            <a href="javascript:void(0);" onclick="reviewProve('{$vo.id}',1)">
                                               <i class="am-icon-check"></i> 审核通过
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" onclick="reviewProve('{$vo.id}',2)">
                                                <i class="am-icon-close"></i> 审核拒绝
                                            </a>
                                        </li>
                                        {/if}
                                        {if $vo.duplex_status == 2}
                                        <li>
                                            <a href="javascript:void(0);" onclick="layer.msg('{$vo.check_opinion}');">
                                                <i class="am-icon-info-circle"></i> 拒绝原因
                                            </a>
                                        </li>
                                        {/if}
                                        <li class="am-divider"></li>
                                        <li>
                                            <a href="javascript:void(0);" onclick="delDiscuss(0,'{$vo.id}')">
                                                <i class="am-icon-trash"></i> 删除评论
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    !function () {
        $('#withole').click(function () {
            $('.elctive').prop('checked', $(this).prop('checked'));
        });

        $('.elctive').click(function () {
            var allChecked = true;
            $('.elctive').each(function () {
                if (!$(this).prop('checked')) {
                    allChecked = false;
                    return false;
                }
            });
            $('#withole').prop('checked', allChecked);
        });
    }();

    var getSelectedIds = function() {
        return $('.elctive:checked').map(function() {
            return $(this).val();
        }).get();
    }

    var batchReviewProve = function (process) {
        var acid = getSelectedIds();
        if (acid.length === 0) {
            layer.alert('很抱歉，当前没有选中要操作的数据', {'title': '系统提示'});
            return;
        }
        reviewProve(acid, process);
    }

    var reviewProve = function (acid, process) {
        var ids = Array.isArray(acid) ? acid : [acid];
        var actionText = process === 1 ? '通过审核' : '拒绝审核';

        var performAction = function(reason) {
            var loadIndex = layer.load(1, { shade: [0.1,'#fff'] });
            $.post("{:url('tissue/reviewProve')}", { acid: ids.join(','), process: process, caption: reason }, function(data) {
                layer.close(loadIndex);
                if (data.code > 0) {
                    layer.msg(data.msg, { icon: 1, time: 1600 }, function() { location.reload(); });
                } else {
                    layer.msg(data.msg, { icon: 5, time: 2000 });
                }
            }, 'json');
        };

        if (process === 1) {
            layer.confirm("您确定要" + actionText + "选中的评论吗？", {
                btn: ['确定', '取消'], title: '提示'
            }, function (index) {
                layer.close(index);
                performAction('');
            });
        } else if (process === 2) {
            layer.prompt({
                title: '请输入拒绝原因：',
                formType: 2,
                area: ['300px', '100px'],
            }, function (value, index) {
                layer.close(index);
                performAction(value);
            });
        }
    }
    
    var delDiscuss = function (isBatch, acid) {
        var ids = isBatch ? getSelectedIds() : [acid];
        if (ids.length === 0) {
            layer.alert('很抱歉，当前没有选中要操作的数据', {'title': '系统提示'});
            return;
        }

        layer.confirm("您确定要删除选中的评论吗？此操作不可恢复。", {
            btn: ['确定', '取消'], title: '危险操作'
        }, function (index) {
            layer.close(index);
            var loadIndex = layer.load(1, { shade: [0.1,'#fff'] });
            $.post("{:url('tissue/delDiscuss')}", { acid: ids.join(',') }, function (data) {
                layer.close(loadIndex);
                if (data.code > 0) {
                    layer.msg(data.msg, { icon: 1, time: 1600 }, function() { location.reload(); });
                } else {
                    layer.msg(data.msg, { icon: 5, time: 2000 });
                }
            }, 'json');
        });
    }

    var uploof = function (id) {
        layer.open({
            type: 2,
            title: '查看详情',
            shadeClose: true,
            shade: 0.8,
            area: ['60%', '70%'],
            content: "{:url('essay/setReply')}&uplid=" + id
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        var url = "{:url('tissue/discuss')}&page={$page}";
        if (fz_name) {
            url += "&hazy_name=" + encodeURIComponent(fz_name);
        }
        location.href = url;
    }
</script>
{/block}