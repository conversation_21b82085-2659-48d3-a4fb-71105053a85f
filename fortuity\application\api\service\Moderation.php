<?php

namespace app\api\service;

use TencentCloud\Common\Credential;
use TencentCloud\Common\Exception\TencentCloudSDKException;
use TencentCloud\Common\Profile\ClientProfile;
use TencentCloud\Common\Profile\HttpProfile;
use TencentCloud\Ims\V20201229\ImsClient;
use TencentCloud\Ims\V20201229\Models\ImageModerationRequest;
use TencentCloud\Tms\V20201229\Models\TextModerationRequest;
use TencentCloud\Tms\V20201229\TmsClient;
use think\Controller;
use think\Db;

class Moderation extends Controller
{
    public static function Text($much_id, $msg)
    {
        $verificationList = Db::name('user_violation')->where('much_id', $much_id)->find();
        //解密
        $tencentCloudContentSecurityConfig = json_decode($verificationList['tencent_cloud_content_security_config'], true);
        $tencentCloudContentSecurityConfig['secretId'] = authcode($tencentCloudContentSecurityConfig['secretId'], 'DECODE', 'YuluoNetwork');
        $tencentCloudContentSecurityConfig['secretKey'] = authcode($tencentCloudContentSecurityConfig['secretKey'], 'DECODE', 'YuluoNetwork');
        try {
            $cred = new Credential($tencentCloudContentSecurityConfig['secretId'], $tencentCloudContentSecurityConfig['secretKey']);
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("tms.tencentcloudapi.com");
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            $client = new TmsClient($cred, $tencentCloudContentSecurityConfig['region'], $clientProfile);
            $req = new TextModerationRequest();
            $params = array(
                "Content" => base64_encode($msg)
            );
            $req->fromJsonString(json_encode($params));
            $resp = $client->TextModeration($req);
            $arr = json_decode($resp->toJsonString(), true);
            if ($arr['Suggestion'] == 'Block') {
                return ['status' => 'error', 'code' => 1, 'msg' => '输入内容含有违规词！'];
            }
            return ['status' => 'success', 'code' => 0];
        } catch (TencentCloudSDKException $e) {
            return ['status' => 'error', 'code' => 1, 'msg' => $e->getMessage()];
        }
    }

    public static function Img($much_id, $imgURL)
    {

        $verificationList = Db::name('user_violation')->where('much_id', $much_id)->find();
        //解密
        $tencentCloudContentSecurityConfig = json_decode($verificationList['tencent_cloud_content_security_config'], true);
        $tencentCloudContentSecurityConfig['secretId'] = authcode($tencentCloudContentSecurityConfig['secretId'], 'DECODE', 'YuluoNetwork');
        $tencentCloudContentSecurityConfig['secretKey'] = authcode($tencentCloudContentSecurityConfig['secretKey'], 'DECODE', 'YuluoNetwork');
        try {
            $cred = new Credential($tencentCloudContentSecurityConfig['secretId'], $tencentCloudContentSecurityConfig['secretKey']);
            $httpProfile = new HttpProfile();
            $httpProfile->setEndpoint("ims.tencentcloudapi.com");
            $clientProfile = new ClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            $client = new ImsClient($cred, $tencentCloudContentSecurityConfig['region'], $clientProfile);
            $req = new ImageModerationRequest();
            $params = array(
                "FileUrl" => $imgURL
            );
            $req->fromJsonString(json_encode($params));
            $resp = $client->ImageModeration($req);
            $arr = json_decode($resp->toJsonString(), true);
            if ($arr['Suggestion'] == 'Block') {
                return ['status' => 'error', 'code' => 1, 'msg' => '内容违规！-1'];
            }
            return ['status' => 'success', 'code' => 0];
        } catch (TencentCloudSDKException $e) {
            return ['status' => 'error', 'code' => 1, 'msg' => $e->getMessage()];
        }


    }
}