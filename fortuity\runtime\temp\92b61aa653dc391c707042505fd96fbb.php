<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:88:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/systems/applets.html";i:1703614128;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755608968;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-cog"></span> 小程序设置
        </div>
        <div class="tpl-portlet-input tpl-fz-ml right">
            <span style="font-size: 12px;font-weight: bold;">版本号 : <?php echo $signCode; ?></span>
            <span style="margin-left:10px;font-size: 12px;font-weight: bold;">应用ID : <?php echo $much_id; ?></span>
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top: 20px;">
                <div class="am-form am-form-horizontal">

                    <div class="am-panel am-panel-default">
                        <div class="am-panel-hd">小程序信息</div>
                        <div class="am-panel-bd">

                            <div class="am-form-group" style="display: none;">
                                <label class="am-u-sm-3 am-form-label">小程序名称</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="appName" placeholder="<?php if($configList['app_name']): ?><?php echo $configList['app_name']; else: ?>请输入你的小程序名称<?php endif; ?>">
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">小程序AppID</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="appId" placeholder="<?php if($configList['app_id']): ?><?php echo ciphertext($configList['app_id']); else: ?>请输入你的小程序标识号<?php endif; ?>">
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">小程序AppSecret</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="appSecret" placeholder="<?php if($configList['app_secret']): ?><?php echo ciphertext($configList['app_secret']); else: ?>请输入你的小程序密钥<?php endif; ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="am-panel am-panel-default">
                        <div class="am-panel-hd">微信支付配置</div>
                        <div class="am-panel-bd">

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">微信支付商户号</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="appMchid" placeholder="<?php if($configList['app_mchid']): ?><?php echo ciphertext($configList['app_mchid']); else: ?>请输入你的微信支付商户号<?php endif; ?>">
                                    <small>
                                        商户号 APP_MCHID
                                        <a href="https://pay.weixin.qq.com/" target="_blank">登录微信支付商户平台</a>，
                                        在【账户中心-账户设置-商户信息】中查看
                                    </small>
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">企业付款到零钱 API</label>
                                <div class="am-u-sm-9">
                                    <select id="versionType">
                                        <option value="0" <?php if($configList['version_type']==0): ?>selected<?php endif; ?>>API v2</option>
                                        <option value="1" <?php if($configList['version_type']==1): ?>selected<?php endif; ?>>API v3</option>
                                    </select>
                                    <small>[ 新注册商户建议选择 API v3 ]</small>
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">证书序列号</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="certificateSerial" placeholder="<?php if($configList['certificate_serial_number']): ?><?php echo ciphertext($configList['certificate_serial_number']); else: ?>请输入你的微信支付证书序列号<?php endif; ?>">
                                    <small>
                                        [ 选择 API v2 接口时 此处无需填写 ]
                                    </small>
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">微信支付密钥 v2</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="appKey" placeholder="<?php if($configList['app_key']): ?><?php echo ciphertext($configList['app_key']); else: ?>请输入你的微信支付 v2 密钥<?php endif; ?>">
                                    <small>
                                        微信支付密钥 APP_KEY
                                        <a href="https://pay.weixin.qq.com/" target="_blank">登录微信支付商户平台</a>，
                                        在【账户中心-账户设置-API安全】中设置
                                    </small>
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">微信支付密钥 v3</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="appKeyV3" placeholder="<?php if($configList['app_key_v3']): ?><?php echo ciphertext($configList['app_key_v3']); else: ?>请输入你的微信支付 v3 密钥<?php endif; ?>">
                                    <small>
                                        微信支付密钥 APP_KEY
                                        <a href="https://pay.weixin.qq.com/" target="_blank">登录微信支付商户平台</a>，
                                        在【账户中心-账户设置-API安全】中设置
                                    </small>
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">微信支付回调地址</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="payReact" value="<?php echo $configList['pay_react']; ?>" placeholder="系统默认生成，非必要不建议修改">
                                    <small>
                                        <i style="color: crimson;margin-right: 10px;">系统默认生成，如非必要不建议修改</i>
                                        <span>生成规则：</span><strong>https://站点域名/addons/yl_welore/web/payReact.php</strong>
                                    </small>
                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="am-panel am-panel-default">
                        <div class="am-panel-hd">微信支付证书信息</div>
                        <div class="am-panel-bd">
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">微信支付 apiclient_cert.pem</label>
                                <div class="am-u-sm-9">
                                    <textarea id="apiclientCert" style="height: 280px;resize: none;margin-top: 5px;" placeholder="为保证安全性, 不显示证书内容. 若要修改, 请直接输入"></textarea>
                                    <small style="color: red;">如果不需要提现功能无需填写</small>
                                </div>
                            </div>
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">微信支付 apiclient_key.pem</label>
                                <div class="am-u-sm-9">
                                    <textarea id="apiclientKey" style="height: 280px;resize: none;margin-top: 5px;" placeholder="为保证安全性, 不显示证书内容. 若要修改, 请直接输入"></textarea>
                                    <small style="color: red;">如果不需要提现功能无需填写</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group" style="display: flex;justify-content: center;">
                        <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                    </div>

                </div>
            </div>
        </div>
    </div>

</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<script src="./assets/js/base-enhanced.js"></script>
<script>
var baseConfig = {
    urls: {
        proxyBase: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        proxyResource: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        repairMissing: "<?php echo url('index/repairMissing'); ?>",
        purgeCache: "<?php echo url('index/purgeCache'); ?>",
        ordinary: "<?php echo url('ordinary'); ?>",
        receipt: "<?php echo url('receipt'); ?>"
    },
    menuActive: '<?php echo $menuActive; ?>',
    menuList: '<?php echo $menuList; ?>',
    much_role: '<?php echo $much_role; ?>',
    notice: <?php echo $notice; ?>,
    vacant: <?php echo $vacant; ?>
};
BaseEnhanced.init(baseConfig);
window.retakeCache = BaseEnhanced.retakeCache;
window.getOriginalSrc = BaseEnhanced.getOriginalSrc;
</script>
<?php if($much_role=='founder'): ?>
<script>
window.updateFix = function() {
    layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
        btn: ['确定', '取消'],
        title: '提示'
    }, function() {
        // 执行修复操作
        performDatabaseRepair();
    }, function(index) {
        // 取消操作
        layer.close(index);
    });
};

function performDatabaseRepair() {
    var loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });
    $.post("<?php echo url('index/repairMissing'); ?>", function(data) {
        layer.close(loadingIndex);
        if (data !== false) {
            layer.msg('数据库缺失字段修复完成', {
                icon: 1,
                time: 1500
            }, function() {
                location.reload();
            });
        } else {
            layer.msg('修复失败，请稍后重试', {
                icon: 2,
                time: 2000
            }, function() {
                location.reload();
            });
        }
    }).error(function() {
        layer.close(loadingIndex);
        layer.msg('网络错误，修复失败', {
            icon: 2,
            time: 2000
        });
    });
}
</script>
<?php endif; ?>

<script>


    var slock = false;
    var holdSave = function () {
        if (!slock) {
            var appName = $.trim($('#appName').val());
            var appId = $.trim($("#appId").val());
            var appSecret = $.trim($('#appSecret').val());
            var appMchid = $.trim($('#appMchid').val());
            var appKey = $.trim($('#appKey').val());
            var appKeyV3 = $.trim($('#appKeyV3').val());
            var payReact = $.trim($('#payReact').val());
            var certificateSerial = $.trim($('#certificateSerial').val());
            var versionType = $.trim($('#versionType').val());
            var apiclientCert = $.trim($('#apiclientCert').val());
            var apiclientKey = $.trim($('#apiclientKey').val());
            slock = true;
            $.ajax({
                type: "post",
                url: "<?php echo url('applets'); ?>",
                data: {
                    'usid': '<?php echo $configList['id']; ?>',
                    'appName': appName,
                    'appId': appId,
                    'appSecret': appSecret,
                    'appMchid': appMchid,
                    'appKey': appKey,
                    'appKeyV3': appKeyV3,
                    'payReact': payReact,
                    'certificateSerial': certificateSerial,
                    'versionType': versionType,
                    'apiclientCert': apiclientCert,
                    'apiclientKey': apiclientKey
                },
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                            slock = false;
                        });
                    }
                }
            });
        }
    }
</script>

</body>
</html>