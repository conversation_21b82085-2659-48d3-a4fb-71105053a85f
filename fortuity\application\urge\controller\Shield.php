<?php


namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\Alternative;
use app\common\Playful;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

class Shield extends Base
{

    //身份铭牌列表
    public function disguise()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('camouflage_card')
            ->where('forgery_name', 'like', "%{$hazy_name}%")
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->order('scores', 'asc')
            ->order('id', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //身份铭牌排序
    public function disguiseSort()
    {
        $syid = request()->post('asyId');
        $scores = request()->post('dalue');
        Db::startTrans();
        try {
            Db::name('camouflage_card')->where('id', $syid)->where('much_id', $this->much_id)->update(['scores' => $scores]);
            $result = true;
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
        }
        if ($result) {
            return json(['code' => 1, 'msg' => '保存成功']);
        }
    }

    // 新增身份铭牌
    public function newDisguise()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            $data['making_time'] = time();
            $data['much_id'] = $this->much_id;
            $data['is_del'] = 0;
            Db::startTrans();
            try {
                Db::name('camouflage_card')->insert($data);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '保存成功']);
            }
        } else {
            return $this->fetch();
        }
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, array(), 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    //编辑身份铭牌
    public function editDisguise()
    {
        if (request()->isPost() && request()->isAjax()) {
            $conflux = request()->post();
            $meid = $conflux['meid'];
            $data = $conflux['setData'];
            Db::startTrans();
            try {
                Db::name('camouflage_card')->where('id', $meid)->where('much_id', $this->much_id)->update($data);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '保存成功']);
            }
        } else {
            $layid = request()->get('layid');
            $list = Db::name('camouflage_card')->where('id', $layid)->where('is_del', 0)->where('much_id', $this->much_id)->find();
            if ($list) {
                $this->assign('list', $list);
                return $this->fetch();
            } else {
                $this->error('参数错误', 'shield/disguise');
            }
        }
    }

    //删除身份铭牌
    public function delDisguise()
    {
        if (request()->isPost() && request()->isAjax()) {
            $meid = request()->post('meid');
            $medalInfo = Db::name('camouflage_card')->where('id', $meid)->where('is_del', 0)->where('much_id', $this->much_id)->find();
            if (!$medalInfo) {
                return json(['code' => 0, 'msg' => '删除失败']);
            }
            Db::startTrans();
            try {
                Db::name('camouflage_card')->where('id', $meid)->where('is_del', 0)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '删除成功']);
        } else {
            $this->error('参数错误', 'shield/disguise');
        }
    }

    //横幅广告
    public function thread_banner_promotions()
    {
        $url = $this->defaultQuery();
        $list = Db::name('paper_heat_banner_ads')
            ->where('much_id', $this->much_id)
            ->order(['sort' => 'asc'])
            ->paginate(10, false, ['query' => ['s' => $url]]);
        $this->assign('list', $list);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //横幅广告排序
    public function thread_banner_promotions_sort()
    {
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $sort = request()->post('dalue');
            Db::startTrans();
            try {
                Db::name('paper_heat_banner_ads')->where('id', $syid)->where('much_id', $this->much_id)->update(['sort' => $sort]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            abort(404);
        }
    }

    //新增横幅广告
    public function new_thread_banner_promotions()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['img_url'] = input('post.imgUrl');
            $data['jump_type'] = input('post.jumpType');
            $data['appid'] = input('post.appid');
            $data['url'] = input('post.url');
            $data['sort'] = input('post.sort');
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('paper_heat_banner_ads')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            return $this->fetch();
        }
    }

    //编辑横幅广告
    public function edit_thread_banner_promotions()
    {
        $fid = intval(input('param.fid'));
        if (request()->isPost() && request()->isAjax()) {
            $data['img_url'] = input('post.imgUrl');
            $data['jump_type'] = input('post.jumpType');
            $data['appid'] = input('post.appid');
            $data['url'] = input('post.url');
            $data['sort'] = input('post.sort');
            Db::startTrans();
            try {
                Db::name('paper_heat_banner_ads')->where('id', $fid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $phbaInfo = Db::name('paper_heat_banner_ads')->where('id', $fid)->where('much_id', $this->much_id)->find();
            if ($phbaInfo) {
                $this->assign('list', $phbaInfo);
                return $this->fetch();
            } else {
                abort(404);
            }
        }
    }

    //删除横幅广告
    public function delete_thread_banner_promotions()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('ecid');
            Db::startTrans();
            try {
                Db::name('paper_heat_banner_ads')->where('id', $usid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '删除成功']);
        } else {
            abort(404);
        }
    }

    //热帖置顶
    public function thread_popularity()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['paper_id'] = intval(input('post.paperId'));
            $paInfo = Db::name('paper')->where('id', $data['paper_id'])->where('much_id', $this->much_id)->find();
            if (!$paInfo) {
                return json(['code' => 0, 'msg' => '保存失败，帖子编号不存在！']);
            }
            $data['put_top_start_time'] = intval(floatval(input('post.putTopStartTime')) / 1000);
            $data['put_top_end_time'] = intval(floatval(input('post.putTopEndTime')) / 1000);
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('paper_heat_put_top')->where('paper_id', $data['paper_id'])->where('much_id', $this->much_id)->delete();
                Db::name('paper_heat_put_top')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $url = $this->defaultQuery();
            $list = Db::name('paper')
                ->alias('per')
                ->join('paper_heat_put_top phpt', 'per.id=phpt.paper_id')
                ->join('user us', 'per.user_id=us.id', 'left')
                ->where('per.much_id&phpt.much_id&us.much_id', $this->much_id)
                ->field('phpt.id as phpt_id,phpt.put_top_start_time,phpt.put_top_end_time,per.*,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
                ->order('phpt.id', 'asc')
                ->paginate(10, false, ['query' => ['s' => $url]]);
            $expressionHtml = function ($val) {
                return Alternative::ExpressionHtml($val);
            };
            $this->assign('expressionHtml', $expressionHtml);
            $this->assign('list', $list);
            return $this->fetch();
        }
    }

    //获取热帖置顶详情
    public function get_thread_popularity_info()
    {
        $fid = intval(input('get.fid'));
        $phptInfo = Db::name('paper_heat_put_top')->where('paper_id', $fid)->where('much_id', $this->much_id)->find();
        return json(['code' => 1, 'data' => [
            'paperId' => $phptInfo['paper_id'],
            'putTopStartTime' => $phptInfo['put_top_start_time'] * 1000,
            'putTopEndTime' => $phptInfo['put_top_end_time'] * 1000
        ]]);
    }

    //删除热帖置顶
    public function delete_thread_popularity_info()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            Db::startTrans();
            try {
                Db::name('paper_heat_put_top')->where('paper_id', $fid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    //热帖配置
    public function thread_config()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['custom_title'] = input('post.title');
            $data['style_type'] = input('post.styleType');
            $data['custom_head_img'] = input('post.headImg');
            $data['statistics_time'] = intval(input('post.statisticsTime'));
            $data['custom_sort_condition'] = json_encode(input('post.sortCondition/a'), true);
            $data['display_switch'] = intval(input('post.displaySwitch'));
            Db::startTrans();
            try {
                Db::name('paper_heat_config')->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $phcInfo = Db::name('paper_heat_config')->where('much_id', $this->much_id)->find();
            if (!$phcInfo) {
                $domain = explode(':', $_SERVER['HTTP_HOST']);
                $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
                $phcInfo = [
                    'custom_title' => '热帖排行',
                    'style_type' => 0,
                    'custom_head_img' => "https://{$domain[0]}{$absAddress[0]}static/examine/put_top.jpg",
                    'statistics_time' => 0,
                    'custom_sort_condition' => '["liulan","dianzan","shoucang","huifu"]',
                    'display_switch' => 0,
                    'much_id' => $this->much_id
                ];
                Db::startTrans();
                try {
                    Db::name('paper_heat_config')->insert($phcInfo);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
            }
            $this->assign('list', $phcInfo);
            return $this->fetch();
        }
    }

}