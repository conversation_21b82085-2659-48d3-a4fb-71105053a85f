<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:89:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/compass/uplfence.html";i:1749659429;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755516152;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a></li>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;}.portlet-title{display:flex;justify-content:space-between;align-items:center;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid #f0f0f0;}.caption{font-size:16px;color:#333;font-weight:500;}.caption .am-icon-pencil-square-o{margin-right:5px;color:#23b7e5;}.am-form-label{text-align:right;color:#333;font-weight:normal;}.am-form-group small{display:block;margin-top:.5rem;color:#6c757d;font-size:12px;}.tpl-form-input{display:block;width:100%;height:36px;padding:8px 12px;font-size:14px;line-height:1.5;color:#495057;background-color:#fff;background-clip:padding-box;border:1px solid #ced4da;border-radius:4px;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;}.tpl-form-input:focus{color:#495057;background-color:#fff;border-color:#80bdff;outline:0;box-shadow:0 0 0 0.2rem rgba(35,183,229,.25);}textarea.tpl-form-input{height:auto;min-height:90px;resize:vertical;}.confirm-btn{background:#23b7e5;color:white;border:none;border-radius:3px;padding:8px 18px;font-size:14px;cursor:pointer;transition:all .3s;}.confirm-btn:hover{background:#1a9fd4;}.image-picker{display:flex;align-items:center;gap:15px;}.image-picker .image-preview{flex-shrink:0;border:1px dashed #ced4da;border-radius:4px;cursor:pointer;background-color:#f8f9fa;display:flex;align-items:center;justify-content:center;overflow:hidden;}.image-picker .image-preview img{max-width:100%;max-height:100%;object-fit:cover;}.image-picker .image-controls{display:flex;flex-direction:column;align-items:flex-start;gap:8px;}.action-btn{display:inline-block;padding:5px 10px;font-size:12px;font-weight:normal;line-height:1.4;text-align:center;cursor:pointer;border:1px solid #ced4da;border-radius:4px;transition:all .3s;background-color:#fff;}.action-btn:hover{background-color:#f8f9fa;}.action-btn.btn-danger{color:#fff;background-color:#dc3545;border-color:#dc3545;}.action-btn.btn-danger:hover{background-color:#c82333;border-color:#bd2130;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-pencil-square-o"></span> 编辑圈子
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal tpl-form-line-form">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" class="tpl-form-input" value="<?php echo emoji_decode($list['realm_name']); ?>">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子图标</label>
                        <div class="am-u-sm-9">
                            <div class="image-picker">
                                <div class="image-preview" onclick="cuonice(this);" style="width: 100px; height: 100px;">
                                    <img src="<?php echo $list['realm_icon']; ?>" onerror="this.src='static/disappear/default.png'"/>
                                    <input type="hidden" value="<?php echo $list['realm_icon']; ?>" name="circle-img">
                                </div>
                                <div class="image-controls">
                                    <button type="button" class="action-btn" onclick="$(this).closest('.image-picker').find('.image-preview').trigger('click')">选择图片</button>
                                    <small>建议尺寸：200*200px</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子背景图</label>
                        <div class="am-u-sm-9">
                            <div class="image-picker">
                                <div class="image-preview" onclick="cuonice(this);" style="width: 210px; height: 118px;">
                                    <img src="<?php echo $list['realm_back_img']; ?>" onerror="this.src='static/wechat/image_vip_top.jpg'"/>
                                    <input type="hidden" value="<?php echo $list['realm_back_img']; ?>" name="circle-back-img">
                                </div>
                                <div class="image-controls">
                                    <button type="button" class="action-btn" onclick="$(this).closest('.image-picker').find('.image-preview').trigger('click')">选择图片</button>
                                    <button type="button" class="action-btn btn-danger" onclick="deleteImage(this);">删除图片</button>
                                    <small>建议尺寸：16:9</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">群聊二维码</label>
                        <div class="am-u-sm-9">
                            <div class="image-picker">
                                <div class="image-preview" onclick="cuonice(this);" style="width: 120px; height: 120px;">
                                    <img src="<?php echo $list['group_qrcode']; ?>" onerror="this.src='static/disappear/default.png'"/>
                                    <input type="hidden" value="<?php echo $list['group_qrcode']; ?>" name="group-qrcode">
                                </div>
                                <div class="image-controls">
                                    <button type="button" class="action-btn" onclick="$(this).closest('.image-picker').find('.image-preview').trigger('click')">选择图片</button>
                                    <button type="button" class="action-btn btn-danger" onclick="deleteImage(this);">删除图片</button>
                                    <small>建议尺寸：1:1</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子类型</label>
                        <div class="am-u-sm-9">
                            <select id="needle_id" class="tpl-form-input">
                                <option value="0">请选择</option>
                                <?php if(is_array($needleList) || $needleList instanceof \think\Collection || $needleList instanceof \think\Paginator): $i = 0; $__LIST__ = $needleList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                <option value="<?php echo $vo['id']; ?>" <?php if($list['needle_id']==$vo['id']): ?>selected<?php endif; ?>><?php echo $vo['name']; ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                            <small>选择圈子属于的广场</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子简介</label>
                        <div class="am-u-sm-9">
                            <textarea rows="5" id="intro" placeholder="请输入圈子简介" class="tpl-form-input" style="resize: none;"><?php echo $list['realm_synopsis']; ?></textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">浏览权限</label>
                        <div class="am-u-sm-9">
                            <select id="attention" class="tpl-form-input">
                                <option value="0" <?php if($list['attention']==0): ?>selected<?php endif; ?>>所有用户可见</option>
                                <option value="1" <?php if($list['attention']==1): ?>selected<?php endif; ?>>审核加入可见</option>
                                <option value="2" <?php if($list['attention']==2): ?>selected<?php endif; ?>>会员用户可见</option>
                                <option value="3" <?php if($list['attention']==3): ?>selected<?php endif; ?>>用户关注可见</option>
                            </select>
                            <small>
                                审核加入可见说明：
                                <span style="color:red;">
                                    只有加入圈子的人才可以看到圈子里发布的内容，加入圈子，需要通过圈子管理员审核
                                </span>
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group" <?php if($list['attention']==0||$list['attention']==2): ?>style="display:none;"<?php endif; ?>>
                        <label class="am-u-sm-3 am-form-label">暗号状态</label>
                        <div class="am-u-sm-9">
                            <select id="atence">
                                <option value="0" <?php if($list['atence']==0): ?>selected<?php endif; ?>>关闭暗号</option>
                                <option value="1" <?php if($list['atence']==1): ?>selected<?php endif; ?>>开启暗号</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group" <?php if($list['atence']==0): ?>style="display:none;"<?php endif; ?>>
                        <label class="am-u-sm-3 am-form-label">暗号</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="atcipher" class="tpl-form-input" value="<?php echo $list['atcipher']; ?>" placeholder="请输入暗号">
                            <small style="color:red;">用户输入暗号后无需等待管理审核即可加入访问受限的圈子</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">发帖等级</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="release_level" class="tpl-form-input" value="<?php echo $list['release_level']; ?>" placeholder="请输入发帖等级">
                            <small>请输入圈子发帖等级限制 ( 圈主、管理员不受限制 )</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">浏览等级</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="visit_level" class="tpl-form-input" value="<?php echo $list['visit_level']; ?>" placeholder="请输入浏览等级">
                            <small>请输入圈子浏览等级限制 ( 圈主、管理员不受限制 )</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">发帖次数</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="release_count" class="tpl-form-input" value="<?php echo $list['release_count']; ?>" placeholder="请输入圈子发帖次数限制">
                            <small>0为不限制 ( 用户在此圈子里的发布帖子的次数 圈主、管理员不受限制 )</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子状态</label>
                        <div class="am-u-sm-9">
                            <select id="status" class="tpl-form-input">
                                <option value="0" <?php if($list['status']==0): ?>selected<?php endif; ?>>暂停访问</option>
                                <option value="1" <?php if($list['status']==1): ?>selected<?php endif; ?>>正常访问</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">关注人数</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="concern" class="tpl-form-input" value="<?php echo $list['concern']; ?>" placeholder="请输入关注人数">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="scores" class="tpl-form-input" value="<?php echo $list['scores']; ?>" placeholder="请输入排序数字">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-push-3">
                            <button type="button" class="confirm-btn" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<?php if($much_role=='founder'): ?>
<script>
    var updateFix = function () {
        layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("<?php echo url('index/repairMissing'); ?>", function (data) {
                if (data !== false) {
                    layer.msg('数据库缺失字段修复完成', {time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('修复失败', {time: 1600}, function () {
                        location.reload();
                    });
                }
            });
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }
</script>
<?php endif; ?>
<script>
    new Vue({
        el: '#menu',
        data() {
            return {
                menuActive: '<?php echo $menuActive; ?>',
                menuList: []
            };
        },
        created() {
            const menuList = '<?php echo $menuList; ?>';
            if (menuList !== '') {
                const menuData = JSON.parse(decodeURIComponent(atob(menuList)));
                for (let i = 0; i < menuData.length; i++) {
                    if (menuData[i].pid === 0) {
                        menuData[i].childMenuItems = [];
                        this.menuList.push(menuData[i]);
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        if (menuData[i].pid === this.menuList[j].id) {
                            const childItem = JSON.parse(JSON.stringify(menuData[i]));
                            childItem.grandsonList = [];
                            this.menuList[j].childMenuItems.push(childItem);
                            break;
                        }
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        for (let k = 0; k < this.menuList[j].childMenuItems.length; k++) {
                            if (menuData[i].pid === this.menuList[j].childMenuItems[k].id) {
                                this.menuList[j].childMenuItems[k].grandsonList.push(menuData[i]);
                                break;
                            }
                        }
                    }
                }
            }
        }
    });

    $(function () {
        setInterval(reballot, 15000);
    });

    var reballot = function () {
        $.getJSON("<?php echo url('ordinary'); ?>", function (data) {
            var i = 0;
            if (data.notice > 0 || data.vacant > 0) {
                i++;
            }
            $('#notice-0,#notice-1').text(data.notice);
            $('#vacant-0,#vacant-1').text(data.vacant);
            if ((data.notice + data.vacant) <= data.preCount) {
                i = 0;
            } else {
                $.post("<?php echo url('receipt'); ?>", {'multiply': (data.notice + data.vacant)});
            }
            if (i > 0) {
                var player = $("#backPlayer")[0];
                player.play();
            }
        });
    }

    var retakeCache = function () {
        $.get("<?php echo url('index/purgeCache'); ?>", function () {
            layer.msg('缓存清理完成', {time: 1000}, function () {
                location.reload();
            });
        });
    }
</script>
<script>
// 添加全局变量
window.EDITOR_MODE = false;  // 标记是否在编辑器模式

// AJAX 拦截器 - 自动还原代理 URL 为原始 URL
(function() {
    // 代理 URL 的匹配模式
    var proxyUrlPattern = /[?&]url=([^&]+)/;
    var proxyBaseUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>";

    // URL 还原函数
    function restoreOriginalUrl(url) {
        try {
            // 检查是否是代理 URL
            if (typeof url === 'string' && url.indexOf(proxyBaseUrl) !== -1) {
                var match = url.match(proxyUrlPattern);
                if (match && match[1]) {
                    // 解码并返回原始 URL
                    return decodeURIComponent(match[1]);
                }
            }
            return url;
        } catch (e) {
            // 出错时返回原始值，确保不影响正常功能
            console.warn('URL restore failed:', e);
            return url;
        }
    }

    // 增强版 URL 还原函数 - 支持从 DOM 元素的 data 属性获取原始 URL
    window.getOriginalSrc = function(element) {
        try {
            if (element && element.getAttribute) {
                // 优先从 data-original-src 获取
                var originalSrc = element.getAttribute('data-original-src');
                if (originalSrc) {
                    return originalSrc;
                }
                // 其次尝试从当前 src 还原
                var currentSrc = element.src || element.getAttribute('src');
                return restoreOriginalUrl(currentSrc);
            }
            return element;
        } catch (e) {
            console.warn('getOriginalSrc failed:', e);
            return element;
        }
    };

    // 递归处理数据对象，还原其中的代理 URL
    function restoreDataUrls(data) {
        try {
            if (typeof data === 'string') {
                return restoreOriginalUrl(data);
            } else if (Array.isArray(data)) {
                return data.map(restoreDataUrls);
            } else if (data && typeof data === 'object') {
                var restored = {};
                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        restored[key] = restoreDataUrls(data[key]);
                    }
                }
                return restored;
            }
            return data;
        } catch (e) {
            // 出错时返回原始数据
            console.warn('Data restore failed:', e);
            return data;
        }
    }

    // 保存原始的 jQuery AJAX 方法
    var originalAjax = $.ajax;
    var originalPost = $.post;
    var originalGet = $.get;

    // 重写 $.ajax
    $.ajax = function(options) {
        try {
            if (options && options.data) {
                options.data = restoreDataUrls(options.data);
            }
        } catch (e) {
            console.warn('AJAX data restore failed:', e);
        }
        return originalAjax.apply(this, arguments);
    };

    // 重写 $.post
    $.post = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('POST data restore failed:', e);
        }
        return originalPost.call(this, url, data, success, dataType);
    };

    // 重写 $.get
    $.get = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('GET data restore failed:', e);
        }
        return originalGet.call(this, url, data, success, dataType);
    };
})();

// 修改检测函数
function checkResourceAccess() {
    var isHttps = window.location.protocol === 'https:';
    
    if (isHttps) {
        // 处理图片
        document.querySelectorAll('img[src^="http://"]').forEach(function(img) {
            // 如果是编辑器模式，所有图片都使用代理URL显示
            if(window.EDITOR_MODE) {
                var originalSrc = img.getAttribute('data-src') || img.src;
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
                return;
            }
            
            // 非编辑器模式下排除富文本编辑器内的图片
            if(img.closest('.w-e-text') || img.closest('.w-e-text-container')) {
                return;
            }
            
            var originalSrc = img.getAttribute('data-src') || img.src;
            var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);

            var tmpImg = new Image();
            tmpImg.onload = function() {
                // 原始图片可访问，不做处理
            }
            tmpImg.onerror = function() {
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = proxyUrl;
            }
            tmpImg.src = originalSrc;
        });
        
        // 处理视频
        document.querySelectorAll('video').forEach(function(video) {
            // 处理video的src属性
            if (video.src && video.src.startsWith('http://')) {
                handleResource(video, 'src');
            }
            
            // 处理source标签
            video.querySelectorAll('source[src^="http://"]').forEach(function(source) {
                handleResource(source, 'src');
            });
        });
    }
}

// 统一的资源处理函数
function handleResource(element, srcAttr) {
    var originalSrc = element[srcAttr];
    var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
    
    // 创建临时对象测试资源是否可访问
    var tmpObj = new Image(); // 用Image对象测试连接可用性
    tmpObj.onload = function() {
        // 原始资源可访问，不做处理
    }
    tmpObj.onerror = function() {
        // 原始资源不可访问，切换到代理
        // 存储原始 URL 到 data 属性
        element.setAttribute('data-original-src', originalSrc);
        element[srcAttr] = proxyUrl;

        // 同时更新父级a标签的href
        var parent = element.parentElement;
        if (parent && parent.tagName === 'A') {
            parent.setAttribute('data-original-href', parent.href);
            parent.href = proxyUrl;
        }
    }
    tmpObj.src = originalSrc;
}

// 页面加载完成后执行检查
window.addEventListener('load', checkResourceAccess);

// 动态加载的内容处理
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            checkResourceAccess();
        }
    });
});

// 监听DOM变化
observer.observe(document.body, {
    childList: true,
    subtree: true
});
</script>

<script>

    !function () {
        $('#attention').change(function () {
            if ($(this).val() == 1) {
                $('#atence').parent().parent().show();
            } else {
                $('#atence').parent().parent().hide();
                $('#atence').val('0');
                $('#atcipher').parent().parent().hide();
            }
        });
        $('#atence').change(function () {
            if ($(this).val() == 1) {
                $('#atcipher').parent().parent().show();
            } else {
                $('#atcipher').parent().parent().hide();
            }
        });
    }();

    var cuonice = function (obj) {
        $(obj).children('img').addClass('img-select');
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["<?php echo url('images/dialogImages'); ?>&gclasid=0", 'no'],
            end: function (index, layer) {
                $('.img-select').removeClass('img-select');
            }
        });
    }

    var sutake = function (eurl) {
        $('.img-select').attr('src', eurl).parent().children('input').val(eurl);
        layer.closeAll();
    }

    var deleteImage = function (obj) {
        var picker = $(obj).closest('.image-picker');
        picker.find('img').attr('src', '').addClass('img-select');
        picker.find('input[type=hidden]').val('');
        var originalOnError = picker.find('img').get(0).onerror;
        picker.find('img').get(0).onerror = null;
        picker.find('img').attr('src','');
        picker.find('img').get(0).onerror = originalOnError;
        $('.img-select').removeClass('img-select');
    }

    var holdSave = function () {
        var setData = {};
        setData['uplid'] = '<?php echo $list['id']; ?>';
        setData['name'] = $.trim($('#name').val());
        setData['circle_img'] = $.trim($("[name='circle-img']").val());
        setData['circle_back_img'] = $.trim($("[name='circle-back-img']").val());
        setData['group_qrcode'] = $.trim($("[name='group-qrcode']").val());
        setData['needle_id'] = Number($('#needle_id').val());
        setData['intro']= $.trim($('#intro').val());
        setData['attention']= Number($('#attention').val());
        setData['atence']= Number($('#atence').val());
        setData['atcipher']= $.trim($('#atcipher').val());
        setData['status']= Number($('#status').val());
        setData['release_level']= Number($('#release_level').val());
        setData['visit_level']= Number($('#visit_level').val());
        setData['release_count']= Number($('#release_count').val());
        setData['concern']= Number($('#concern').val());
        setData['scores']=  Number($('#scores').val());
        if (setData['name'] === '') {
            layer.msg('广场名称不能为空');
            return;
        }
        if (setData['circle_img'] === '') {
            layer.msg('请上传广场图标');
            return;
        }
        if (setData['needle_id'] === 0) {
            layer.msg('请选择圈子类型');
            return;
        }
        if (setData['intro'] === '') {
            layer.msg('圈子简介不能为空');
            return;
        }
        $.ajax({
            type: "post",
            url: "<?php echo url('compass/uplfence'); ?>",
            data: setData,
            dataType: 'json',
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.href = "<?php echo url('compass/fence'); ?>";
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        slock = false;
                    });
                }
            }
        });
    }
</script>

</body>
</html>