<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:92:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/essay/edit_writings.html";i:1755267597;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755516152;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a></li>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>[v-cloak]{display:none!important}.am-modal-hd .am-close{top:-5px;right:-3px;}.w-e-menu{font-size:12px;}.w-e-text,.w-e-text-container{height:500px !important;}#word{position:absolute;z-index:10002;height:auto;background-color:white;border:black solid 1px;display:none;padding:4px 0.43em;}.click-work{font-size:14px;cursor:pointer;z-index:10003;font-weight:bold;}.click-work:hover{color:black;background-color:#f0f0f0;z-index:10003;}.el-input__inner{width:100% !important;margin-top:-1px !important;padding-left:30px !important;}.el-picker-panel.el-date-picker.el-popper{z-index:10001 !important;}</style>
<div id="app" class="tpl-portlet-components" v-cloak>
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-edit"></span> 编辑帖子
        </div>
        <div class="tpl-portlet-input tpl-fz-ml right">
            <a href="javascript:void(0);" @click="openArticleModal">
                <span style="font-size:12px;font-weight:bold;color:black;padding:4px 9px;background: #98b2b5;">
                    微信公众号文章采集
                </span>
            </a>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">用户昵称</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input style="background: #f4f6f7;" :title="postData.userNickName" type="text" :value="postData.userNickName" readonly>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">帖子标题</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" v-model="postData.title" placeholder="请输入帖子标题">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">标题颜色</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div style="display: flex;">
                                <input type="text" v-model="postData.titleColor">
                                <el-color-picker v-model="postData.titleColor"></el-color-picker>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">帖子类型</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select v-model="postData.patype" @change="changePostType">
                                <?php if($list['study_type'] != 4 && $list['study_type'] != 5): ?>
                                <option value="0" <?php if($list['study_type']==0): ?>selected<?php endif; ?>>图文帖</option>
                                <option value="1" <?php if($list['study_type']==1): ?>selected<?php endif; ?>>语音帖</option>
                                <option value="2" <?php if($list['study_type']==2): ?>selected<?php endif; ?>>视频帖</option>
                                <option value="3" <?php if($list['study_type']==3): ?>selected<?php endif; ?>>活动帖</option>
                                <option value="6" <?php if($list['study_type']==6): ?>selected<?php endif; ?>>视频号视频</option>
                                <?php else: ?>
                                <option value="4" <?php if($list['study_type']==4): ?>selected<?php endif; ?>>单选投票帖</option>
                                <option value="5" <?php if($list['study_type']==5): ?>selected<?php endif; ?>>多选投票帖</option>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" title="付费帖子">付费帖子</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select v-model="postData.isBuy" @change="changeBuyStatus">
                                <option value="0" <?php if($list['is_buy']==0): ?>selected<?php endif; ?>>关闭</option>
                                <option value="1" <?php if((!$netDiscPluginKey&&$list['is_buy']>=1)||$list['is_buy']==1): ?>selected<?php endif; ?>>内容付费</option>
                                <?php if($netDiscPluginKey): ?>
                                <option value="2" <?php if($list['is_buy']==2): ?>selected<?php endif; ?>>文件付费</option>
                                <option value="3" <?php if($list['is_buy']==3): ?>selected<?php endif; ?>>整体付费</option>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                    <div v-show="showPayHidden">
                        <?php if($netDiscPluginKey): ?>
                        <div v-show="showPayFileHidden">
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">
                                    付费文件
                                </label>
                                <div class="am-u-sm-8 am-u-end">
                                    <span @click="selectFiles">
                                        <input type="hidden" v-model="postData.ncId">
                                        <input type="hidden" v-model="postData.fileId">
                                        <input type="hidden" v-model="postData.filePid">
                                        <div id="fileNames" style="width:100%; background: #eee; color: #555; font-size:1.2rem; line-height: 1.2; border: 1px solid #ccc; padding: 1rem; cursor: pointer; display: inline-block;">
                                            {{ fileNames }}
                                        </div>
                                    </span>
                                </div>
                            </div>
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label" title="二次售卖">二次售卖</label>
                                <div class="am-u-sm-8 am-u-end">
                                    <select v-model="postData.fileIsSell">
                                        <option value="1" <?php if(!$list['buyFilesInfo']['ncsInfo'] || $list['buyFilesInfo']['ncsInfo']['is_sell']==1): ?>selected<?php endif; ?>>允许</option>
                                        <option value="0" <?php if($list['buyFilesInfo']['ncsInfo'] && $list['buyFilesInfo']['ncsInfo']['is_sell']==0): ?>selected<?php endif; ?>>禁止</option>
                                    </select>
                                    <small>
                                        <i style="color: #0f9ae0;">
                                            是否允许文件被其他用户二次售卖
                                        </i>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label" title="付费帖子">付费类型</label>
                            <div class="am-u-sm-8 am-u-end">
                                <select v-model="postData.buyPriceType">
                                    <option value="0" <?php if($list['buy_price_type']==0): ?>selected<?php endif; ?>><?php echo $defaultNavigate['currency']; ?>支付</option>
                                    <option value="1" <?php if($list['buy_price_type']==1): ?>selected<?php endif; ?>><?php echo $defaultNavigate['confer']; ?>支付</option>
                                    <!--
                                    <option value="2" <?php if($list['buy_price_type']==2): ?>selected<?php endif; ?>>微信支付</option>
                                    -->
                                </select>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">
                                付费售价
                            </label>
                            <div class="am-u-sm-8 am-u-end">
                                <input type="text" v-model="postData.buyPrice" @input="postData.buyPrice = digitalCheck(postData.buyPrice, 2)">
                            </div>
                        </div>
                    </div>
                    <?php if($callPhonePluginKey): ?>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">联系方式</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" v-model="postData.callPhone" @input="postData.callPhone = digitalCheck(postData.callPhone, 0)">
                            <small>用户可直接在帖子内拨打电话（插件功能 非必填项）</small>
                        </div>
                    </div>
                    <?php endif; ?>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">所属圈子</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select v-model="postData.toryid">
                                <?php if(is_array($toryInfo) || $toryInfo instanceof \think\Collection || $toryInfo instanceof \think\Paginator): $i = 0; $__LIST__ = $toryInfo;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                <option value="<?php echo $vo['id']; ?>" <?php if($list['tory_id']==$vo['id']): ?>selected<?php endif; ?>>
                                    <?php echo $vo['realm_name']; ?>
                                </option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div v-show="showSelectBrisk">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">活动认证</label>
                            <div class="am-u-sm-8 am-u-end">
                                <select v-model="postData.briskApprove">
                                    <option value="0" <?php if($briskTeamInfo['is_approve']==0): ?>selected<?php endif; ?>>未认证</option>
                                    <option value="1" <?php if($briskTeamInfo['is_approve']==1): ?>selected<?php endif; ?>>已认证</option>
                                </select>
                                <small>活动真实性是否已经经过认证</small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">活动地址</label>
                            <div class="am-u-sm-8 am-u-end">
                                <input type="text" v-model="postData.briskAddress" placeholder="请输入活动地址">
                                <small>
                                    <a href="https://lbs.amap.com/tools/picker" target="_blank" style="margin-right:10px;">点击打开地图获取坐标</a>
                                    例如 北京市天安门广场 坐标为 : ( 116.397724,39.903755 ) 其中 116.xxx 为经度 39.xxx 为纬度
                                </small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">坐标经度</label>
                            <div class="am-u-sm-3">
                                <input type="text" v-model="postData.briskAddressLongitude" placeholder="请输入活动地址坐标经度">
                            </div>
                            <label class="am-u-sm-2 am-form-label" style="white-space:nowrap;">坐标纬度</label>
                            <div class="am-u-sm-3 am-u-end">
                                <input type="text" v-model="postData.briskAddressLatitude" placeholder="请输入活动地址坐标纬度">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">活动开始时间</label>
                            <div class="am-u-sm-3">
                                <input id="dateStartTime" type="text" placeholder="请选择活动开始时间" readonly style="cursor: pointer;">
                            </div>
                            <label class="am-u-sm-2 am-form-label" style="white-space:nowrap;">活动结束时间</label>
                            <div class="am-u-sm-3 am-u-end">
                                <input id="dateEndTime" type="text" placeholder="请选择活动结束时间" readonly style="cursor: pointer;">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">活动人数</label>
                            <div class="am-u-sm-8 am-u-end">
                                <input type="number" v-model="postData.numberOfPeople" placeholder="请输入活动参加人数">
                                <small>设置数值 0 为不限制报名人数</small>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">话题名称</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" v-model="postData.gambit" @keyup="loadGambitSuggestions" placeholder="输入话题名称前后不要带 # 号 例如 #话题# 直接输入 话题 即可">
                            <div id="word" v-show="wordVisible">
                                <div class="click-work" v-for="item in wordSuggestions" @click="selectGambit(item.s)">{{ item.s }}</div>
                            </div>
                        </div>
                    </div>

                    <div v-show="showSelectBallot" class="am-form-group">
                        <div style="margin:10px 0;width:auto;height:50px">
                            <label class="am-u-sm-3 am-form-label">投票截止时间</label>
                            <div class="am-u-sm-8 am-u-end">
                                <input id="voteDeadline" type="text" placeholder="请选择投票截止时间" readonly>
                            </div>
                        </div>
                        <div v-for="(item, index) in pvItems" :key="index" style="width:auto;height:80px;">
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-text-sm" style="text-align:right;padding-top:8px;">选项 {{ index + 1 }}.</label>
                                <div class="am-u-sm-3">
                                    <input type="text" v-model="item.ballot_name" readonly>
                                </div>
                                <label class="am-u-sm-2 am-text-sm" style="text-align:right;padding-top:8px;">虚假票数</label>
                                <div class="am-u-sm-3 am-u-end">
                                    <input type="number" v-model="item.cheat_ballot" placeholder="请填写虚假票数">
                                    <small style="color:#b56b6b;">可设置虚假选票数量 如不需要可不填写</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-show="showSelectAudio" class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">音频文件</label>
                        <div class="am-u-sm-9" style="margin-top:4px;font-size:12px;">
                            <audio id="audioFile" :src="postData.voice" controls="controls" style="width:88%;">
                                您的浏览器不支持标签。
                            </audio>
                            <input type="file" width="20%" @change="fileUpload(0, $event)">
                        </div>
                    </div>
                    <div v-show="showSelectVideo">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">视频类型</label>
                            <div class="am-u-sm-8 am-u-end">
                                <select v-model="postData.videoType" @change="changeVideoType">
                                    <option value="0" <?php if($list['video_type']==0): ?>selected<?php endif; ?>>本地视频</option>
                                    <option value="1" <?php if($list['video_type']==1): ?>selected<?php endif; ?>>腾讯视频</option>
                                    <?php if($videoParsePluginKey): ?>
                                    <option value="2" <?php if($list['video_type']==2): ?>selected<?php endif; ?>>视频解析</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">{{ thirdPartyVideoName }}</label>
                            <div class="am-u-sm-8 am-u-end" style="display: flex;">
                                <input type="text" v-model="postData.tencentVideoVid">
                                <button type="button" class="am-btn am-btn-default am-btn-xs" @click="loadThirdPartyVideo">加载视频</button>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">视频文件</label>
                            <div class="am-u-sm-9" style="margin-top:4px;font-size:12px;">
                                <video id="videoFile" :src="postData.video" controls="controls" crossorigin="anonymous" style="width:88%;height:362px;background:#9db3bf;" @loadeddata="captureImage($event.target)">
                                    您的浏览器不支持视频标签。
                                </video>
                                <div v-show="postData.videoType == 0">
                                    <input type="file" width="20%" accept="video/mp4" @change="fileUpload(1, $event)">
                                </div>
                                <div style="width: 88%;height: 362px;display: flex;justify-content: center;border:1px dashed #ccc;background: url('./static/wechat/image_vip_top.jpg') no-repeat;background-size:cover;margin-top: 10px;">
                                    <div style="width: 100%;height: 100%;cursor: pointer;text-align: center;" title="点击选择视频封面图" @click="openImageDialog(0)">
                                        <img id="videoCoverImage" :src="postData.videoImg" style="width: 100%;height: 362px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">图片类型</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select v-model="postData.imgShowType">
                                <option value="0" <?php if($list['img_show_type']==0): ?>selected<?php endif; ?>>默认样式</option>
                                <option value="1" <?php if($list['img_show_type']==1): ?>selected<?php endif; ?>>九宫格</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group" v-if="postData.patype == 6">
                        <label class="am-u-sm-3 am-form-label">feed-token</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" v-model="postData.feedToken">
                            <small style="color: indianred;font-weight: bold;">登陆MP平台，在「设置-基本设置-隐私与安全」找到「获取视频号视频ID权限」，并将开关打开<br>在移动端找到想要内嵌的视频号视频，并复制该视频的feed-token</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">发布时间</label>
                        <div class="am-u-sm-8 am-u-end">
                            <el-date-picker style="width: 100%;" v-model="adapterTime" type="datetime" value-format="timestamp" placeholder="选择日期时间"></el-date-picker>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">帖子内容</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div id="detailBase64" style="display: none;"><?php echo base64_encode(rawurlencode($richTextCompatible($list['study_content']))); ?></div>
                            <div id="detail" style="min-height:600px;"></div>
                            <span id="customizeGallery" style="display:none;" @click="openImageDialog(1)"></span>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <div class="am-u-sm-6 am-u-sm-push-6">
                            <span style="padding:10px 20px;background:#479fcd;color:white;cursor:pointer;border-radius:10px;" @click="submitForm">保 存 帖 子</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="am-modal am-modal-prompt" tabindex="-1" id="network-article" style="z-index:10005;">
        <div class="am-modal-dialog">
            <div class="am-modal-hd">微信公众号文章地址</div>
            <div class="am-modal-bd">
                <input v-model="modalArticleUrl" type="text" class="am-modal-prompt-input">
            </div>
            <div class="am-modal-footer">
                <span class="am-modal-btn" data-am-modal-confirm>确定</span>
                <span class="am-modal-btn" data-am-modal-cancel>取消</span>
            </div>
        </div>
    </div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<?php if($much_role=='founder'): ?>
<script>
    var updateFix = function () {
        layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("<?php echo url('index/repairMissing'); ?>", function (data) {
                if (data !== false) {
                    layer.msg('数据库缺失字段修复完成', {time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('修复失败', {time: 1600}, function () {
                        location.reload();
                    });
                }
            });
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }
</script>
<?php endif; ?>
<script>
    new Vue({
        el: '#menu',
        data() {
            return {
                menuActive: '<?php echo $menuActive; ?>',
                menuList: []
            };
        },
        created() {
            const menuList = '<?php echo $menuList; ?>';
            if (menuList !== '') {
                const menuData = JSON.parse(decodeURIComponent(atob(menuList)));
                for (let i = 0; i < menuData.length; i++) {
                    if (menuData[i].pid === 0) {
                        menuData[i].childMenuItems = [];
                        this.menuList.push(menuData[i]);
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        if (menuData[i].pid === this.menuList[j].id) {
                            const childItem = JSON.parse(JSON.stringify(menuData[i]));
                            childItem.grandsonList = [];
                            this.menuList[j].childMenuItems.push(childItem);
                            break;
                        }
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        for (let k = 0; k < this.menuList[j].childMenuItems.length; k++) {
                            if (menuData[i].pid === this.menuList[j].childMenuItems[k].id) {
                                this.menuList[j].childMenuItems[k].grandsonList.push(menuData[i]);
                                break;
                            }
                        }
                    }
                }
            }
        }
    });

    $(function () {
        setInterval(reballot, 15000);
    });

    var reballot = function () {
        $.getJSON("<?php echo url('ordinary'); ?>", function (data) {
            var i = 0;
            if (data.notice > 0 || data.vacant > 0) {
                i++;
            }
            $('#notice-0,#notice-1').text(data.notice);
            $('#vacant-0,#vacant-1').text(data.vacant);
            if ((data.notice + data.vacant) <= data.preCount) {
                i = 0;
            } else {
                $.post("<?php echo url('receipt'); ?>", {'multiply': (data.notice + data.vacant)});
            }
            if (i > 0) {
                var player = $("#backPlayer")[0];
                player.play();
            }
        });
    }

    var retakeCache = function () {
        $.get("<?php echo url('index/purgeCache'); ?>", function () {
            layer.msg('缓存清理完成', {time: 1000}, function () {
                location.reload();
            });
        });
    }
</script>
<script>
// 添加全局变量
window.EDITOR_MODE = false;  // 标记是否在编辑器模式

// AJAX 拦截器 - 自动还原代理 URL 为原始 URL
(function() {
    // 代理 URL 的匹配模式
    var proxyUrlPattern = /[?&]url=([^&]+)/;
    var proxyBaseUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>";

    // URL 还原函数
    function restoreOriginalUrl(url) {
        try {
            // 检查是否是代理 URL
            if (typeof url === 'string' && url.indexOf(proxyBaseUrl) !== -1) {
                var match = url.match(proxyUrlPattern);
                if (match && match[1]) {
                    // 解码并返回原始 URL
                    return decodeURIComponent(match[1]);
                }
            }
            return url;
        } catch (e) {
            // 出错时返回原始值，确保不影响正常功能
            console.warn('URL restore failed:', e);
            return url;
        }
    }

    // 增强版 URL 还原函数 - 支持从 DOM 元素的 data 属性获取原始 URL
    window.getOriginalSrc = function(element) {
        try {
            if (element && element.getAttribute) {
                // 优先从 data-original-src 获取
                var originalSrc = element.getAttribute('data-original-src');
                if (originalSrc) {
                    return originalSrc;
                }
                // 其次尝试从当前 src 还原
                var currentSrc = element.src || element.getAttribute('src');
                return restoreOriginalUrl(currentSrc);
            }
            return element;
        } catch (e) {
            console.warn('getOriginalSrc failed:', e);
            return element;
        }
    };

    // 递归处理数据对象，还原其中的代理 URL
    function restoreDataUrls(data) {
        try {
            if (typeof data === 'string') {
                return restoreOriginalUrl(data);
            } else if (Array.isArray(data)) {
                return data.map(restoreDataUrls);
            } else if (data && typeof data === 'object') {
                var restored = {};
                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        restored[key] = restoreDataUrls(data[key]);
                    }
                }
                return restored;
            }
            return data;
        } catch (e) {
            // 出错时返回原始数据
            console.warn('Data restore failed:', e);
            return data;
        }
    }

    // 保存原始的 jQuery AJAX 方法
    var originalAjax = $.ajax;
    var originalPost = $.post;
    var originalGet = $.get;

    // 重写 $.ajax
    $.ajax = function(options) {
        try {
            if (options && options.data) {
                options.data = restoreDataUrls(options.data);
            }
        } catch (e) {
            console.warn('AJAX data restore failed:', e);
        }
        return originalAjax.apply(this, arguments);
    };

    // 重写 $.post
    $.post = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('POST data restore failed:', e);
        }
        return originalPost.call(this, url, data, success, dataType);
    };

    // 重写 $.get
    $.get = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('GET data restore failed:', e);
        }
        return originalGet.call(this, url, data, success, dataType);
    };
})();

// 修改检测函数
function checkResourceAccess() {
    var isHttps = window.location.protocol === 'https:';
    
    if (isHttps) {
        // 处理图片
        document.querySelectorAll('img[src^="http://"]').forEach(function(img) {
            // 如果是编辑器模式，所有图片都使用代理URL显示
            if(window.EDITOR_MODE) {
                var originalSrc = img.getAttribute('data-src') || img.src;
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
                return;
            }
            
            // 非编辑器模式下排除富文本编辑器内的图片
            if(img.closest('.w-e-text') || img.closest('.w-e-text-container')) {
                return;
            }
            
            var originalSrc = img.getAttribute('data-src') || img.src;
            var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);

            var tmpImg = new Image();
            tmpImg.onload = function() {
                // 原始图片可访问，不做处理
            }
            tmpImg.onerror = function() {
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = proxyUrl;
            }
            tmpImg.src = originalSrc;
        });
        
        // 处理视频
        document.querySelectorAll('video').forEach(function(video) {
            // 处理video的src属性
            if (video.src && video.src.startsWith('http://')) {
                handleResource(video, 'src');
            }
            
            // 处理source标签
            video.querySelectorAll('source[src^="http://"]').forEach(function(source) {
                handleResource(source, 'src');
            });
        });
    }
}

// 统一的资源处理函数
function handleResource(element, srcAttr) {
    var originalSrc = element[srcAttr];
    var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
    
    // 创建临时对象测试资源是否可访问
    var tmpObj = new Image(); // 用Image对象测试连接可用性
    tmpObj.onload = function() {
        // 原始资源可访问，不做处理
    }
    tmpObj.onerror = function() {
        // 原始资源不可访问，切换到代理
        // 存储原始 URL 到 data 属性
        element.setAttribute('data-original-src', originalSrc);
        element[srcAttr] = proxyUrl;

        // 同时更新父级a标签的href
        var parent = element.parentElement;
        if (parent && parent.tagName === 'A') {
            parent.setAttribute('data-original-href', parent.href);
            parent.href = proxyUrl;
        }
    }
    tmpObj.src = originalSrc;
}

// 页面加载完成后执行检查
window.addEventListener('load', checkResourceAccess);

// 动态加载的内容处理
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            checkResourceAccess();
        }
    });
});

// 监听DOM变化
observer.observe(document.body, {
    childList: true,
    subtree: true
});
</script>

<script src="static/datetime/laydate.js"></script>
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js?time=<?php echo time(); ?>"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css?time=<?php echo time(); ?>">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js?time=<?php echo time(); ?>"></script>
<script>
    // 全局函数，用于接收文件选择回调
    var filesRefrain = function (nid, fid, fileName, pid) {
        // 调用Vue实例中的方法
        if (vm) {
            vm.setFileInfo(nid, fid, fileName, pid);
        }
    }

    // 全局函数，用于接收图片选择回调
    var sutake = function (eurl, richText) {
        // 调用Vue实例中的方法
        if (vm) {
            if (richText == 1) {
                // 富文本编辑器中插入图片
                editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
            } else {
                // 视频封面图
                var videoCoverImage = $('#videoCoverImage');
                videoCoverImage.attr('src', eurl);
                videoCoverImage.css({'width':'auto'});
                videoCoverImage.parent().parent().css({'background': 'none'});
                vm.postData.videoImg = eurl;
            }
        }
    }
    var appendTextStealth = function () {
        var textContent = ($.trim(editor.txt.html())).replace(/<p><br><\/p>/ig, '');
        if (textContent === '') {
            editor.txt.html('<div class="stealth_module"></div>');
        } else {
            editor.txt.append('<div class="stealth_module"></div>');
        }
    }

    // 全局函数，用于选择表情
    var selectEmoji = function () {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['350px', '360px'],
            scrollbar: true,
            closeBtn: false,
            shadeClose: true,
            content: ["<?php echo url('tedious/emoji'); ?>&type=0", 'no'],
        });
    }

    // 全局函数，用于接收表情选择回调
    var complicatedCallFunc = function (type, data) {
        if (type === 0 && editor) {
            var textContent = ($.trim(editor.txt.html())).replace(/<p><br><\/p>/ig, '');
            if (textContent === '') {
                editor.txt.html('<span>' + data['content'] + '</span>');
            } else {
                editor.txt.append('<span>' + data['content'] + '</span>');
            }
        }
    }

    var E;
    var editor;
    var vm;

    vm = new Vue({
        el: '#app',
        data() {
            return {
                // 用户信息
                postData: {
                    userNickName: '<?php echo emoji_decode($list['user_nick_name']); ?>',
                    title: '<?php echo emoji_decode($list['study_title']); ?>',
                    titleColor: '<?php echo $list['study_title_color']; ?>',
                    patype: '<?php echo $list['study_type']; ?>',
                    isBuy: '<?php echo $list['is_buy']; ?>',
                    buyPriceType: '<?php echo $list['buy_price_type']; ?>',
                    buyPrice: '<?php echo $list['buy_price']; ?>',
                    toryid: '<?php echo $list['tory_id']; ?>',
                    gambit: '<?php echo $gambitInfo['gambit_name']; ?>',
                    imgShowType: '<?php echo $list['img_show_type']; ?>',
                    callPhone: '<?php echo $list['call_phone']; ?>',
                    
                    // 音频相关
                    voice: '<?php echo $list['study_voice']; ?>',
                    voiceTime: '<?php echo $list['study_voice_time']; ?>',
                    
                    // 视频相关
                    video: '<?php echo $list['study_video']; ?>',
                    videoType: '<?php echo $list['video_type']; ?>',
                    videoImg: '<?php if($list['study_type']==2): ?><?php echo athumbnail($list['image_part']); endif; ?>',
                    tencentVideoVid: '<?php if($list['study_video']): ?><?php echo $list['study_video']; else: ?><?php echo $list['third_part_vid']; endif; ?>',
                    
                    // 活动相关
                    briskApprove: '<?php echo $briskTeamInfo['is_approve']; ?>',
                    briskAddress: '<?php echo $briskTeamInfo['brisk_address']; ?>',
                    briskAddressLatitude: '<?php echo $briskTeamInfo['brisk_address_latitude']; ?>',
                    briskAddressLongitude: '<?php echo $briskTeamInfo['brisk_address_longitude']; ?>',
                    dateStartTime: '<?php if($briskTeamInfo['start_time']): ?><?php echo date('Y-m-d H:i:s',$briskTeamInfo['start_time']); endif; ?>',
                    dateEndTime: '<?php if($briskTeamInfo['end_time']): ?><?php echo date('Y-m-d H:i:s',$briskTeamInfo['end_time']); endif; ?>',
                    numberOfPeople: '<?php echo $briskTeamInfo['number_of_people']; ?>',
                    
                    // 付费文件相关
                    ncId: '<?php if($list['buyFilesInfo']['ncsInfo']): ?><?php echo $list['buyFilesInfo']['nc_id']; else: ?>0<?php endif; ?>',
                    fileId: '<?php if($list['buyFilesInfo']['ncsInfo']): ?><?php echo $list['buyFilesInfo']['id']; else: ?>0<?php endif; ?>',
                    filePid: '<?php if($list['buyFilesInfo']['ncsInfo']): ?><?php echo $list['buyFilesInfo']['parent_path_id']; else: ?>0<?php endif; ?>',
                    fileIsSell: '<?php if(!$list['buyFilesInfo']['ncsInfo'] || $list['buyFilesInfo']['ncsInfo']['is_sell']==1): ?>1<?php else: ?>0<?php endif; ?>',
                    
                    // 投票相关
                    voteDeadline: '<?php if($list['vote_deadline']): ?><?php echo date('Y-m-d H:i:s',$list['vote_deadline']); endif; ?>',

                    // 视频号视频
                    feedToken: '<?php echo $paperWechatChannelVideoInfo['feed_token']; ?>'
                },
                adapterTime: '<?php echo $list['adapter_time']*1000; ?>',
                fileNames: '<?php if($list['buyFilesInfo']['ncsInfo']): ?><?php echo $list['buyFilesInfo']['file_name']; if(!$list['buyFilesInfo']['is_dir']): ?>.<?php echo $list['buyFilesInfo']['ncInfo']['file_suffix']; endif; else: ?>点击选择文件信息<?php endif; ?>',
                
                // 投票信息
                pvItems: [],
                
                // 话题建议
                wordSuggestions: [],
                wordVisible: false,
                
                // 微信公众号文章采集
                modalArticleUrl: '',
                
                // 界面控制
                showPayHidden: Number('<?php echo $list['is_buy']; ?>') > 0,
                showPayFileHidden: Number('<?php echo $list['is_buy']; ?>') > 1,
                showSelectGraphic: false,
                showSelectAudio: false,
                showSelectVideo: false,
                showSelectBrisk: false,
                showSelectBallot: false,
                
                thirdPartyVideoName: '视频地址',
                isLock: false
            };
        },
        methods: {
            // 打开微信公众号文章采集模态框
            openArticleModal() {
                // 在Vue中使用layer或其他UI组件
                const that = this;
                setTimeout(function () {
                    $('.am-dimmer').css({'z-index': '10004'})
                }, 10);
                $('#network-article').modal({
                    relatedTarget: this,
                    onConfirm: function(e) {
                        if ($.trim(e.data) == '') {
                            layer.msg('微信公众号文章地址输入有误');
                        } else {
                            that.fetchArticle($.trim(e.data));
                        }
                        that.modalArticleUrl = '';
                    },
                    onCancel: function() {
                        that.modalArticleUrl = '';
                    }
                });
            },
            
            // 获取微信文章内容
            fetchArticle(url) {
                const that = this;
                $.getJSON("<?php echo url('unlawful/gathering'); ?>", {'url': url}, function(data) {
                    if (data.code === 0) {
                        layer.msg(data.msg || '微信公众号文章地址输入有误');
                    } else if (data.code === 1 || (!data.code && data.title)) {
                        that.postData.title = data.title;
                        editor.txt.html(data.content);
                    } else {
                        layer.msg('微信公众号文章地址输入有误');
                    }
                }).fail(function() {
                    layer.msg('请求失败，请检查网络连接');
                });
            },
            
            // 切换帖子类型
            changePostType() {
                this.showSelectGraphic = false;
                this.showSelectAudio = false;
                this.showSelectVideo = false;
                this.showSelectBrisk = false;
                this.showSelectBallot = false;

                switch(parseInt(this.postData.patype)) {
                    case 0: // 图文帖
                        this.showSelectGraphic = true;
                        break;
                    case 1: // 语音帖
                        this.showSelectAudio = true;
                        break;
                    case 2: // 视频帖
                        this.showSelectVideo = true;
                        break;
                    case 3: // 活动帖
                        this.showSelectBrisk = true;
                        break;
                    case 4: // 单选投票帖
                    case 5: // 多选投票帖
                        this.showSelectBallot = true;
                        break;
                }
            },
            
            // 变更付费状态
            changeBuyStatus() {
                this.showPayHidden = parseInt(this.postData.isBuy) > 0;
                this.showPayFileHidden = parseInt(this.postData.isBuy) > 1;
            },
            
            // 变更视频类型
            changeVideoType() {
                if (parseInt(this.postData.videoType) === 0 || parseInt(this.postData.videoType) === 2) {
                    this.thirdPartyVideoName = '视频地址';
                } else {
                    this.thirdPartyVideoName = '视频vid';
                }
            },
            
            // 加载话题建议
            loadGambitSuggestions() {
                const keywords = this.postData.gambit.trim();
                if (keywords === '') {
                    this.wordSuggestions = [];
                    this.wordVisible = false;
                    return;
                }
                const that = this;
                that.wordVisible = true;
                $('#word').width($('#gambit').width());
                $.getJSON('<?php echo url('unlawful/loadGambit'); ?>', {'wd': keywords}, function(data) {
                    that.wordSuggestions = data;
                    that.wordVisible = data.length > 0;
                });
            },
            
            // 选择话题
            selectGambit(word) {
                this.postData.gambit = word;
                this.wordVisible = false;
            },
            
            // 数字格式检查
            digitalCheck(value, limit) {
                if (limit === 2) {
                    value = value.replace(/[^\d.]/g, "");
                    value = value.replace(/^\./g, "");
                    value = value.replace(/\.{2,}/g, ".");
                    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
                    value = value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
                } else {
                    value = Number((value.match(/^\d+(?:\.\d{0})?/)));
                }
                return value;
            },
            
            // 文件上传
            fileUpload(type, event) {
                const file = event.target.files[0];
                if (!file) return;

                let fileQualified = true;
                const suffix = file.name.lastIndexOf('.');
                const ext = file.name.substring(suffix).toLowerCase();

                if (type === 0) { // 音频文件
                    if (ext !== '.wav' && ext !== '.mp3' && ext !== '.ogg' && ext !== '.mpeg') {
                        layer.msg('文件类型选择错误,请选择音频类型文件进行上传');
                        event.target.value = '';
                        return;
                    }
                } else if (type === 1) { // 视频文件
                    if (ext !== '.mp4') {
                        layer.msg('视频文件类型选择错误,请选择MP4类型文件进行上传');
                        event.target.value = '';
                        return;
                    }
                }

                if (fileQualified) {
                    layer.load();
                    const formData = new FormData();
                    formData.append('sngpic', file);

                    const that = this;
                    $.ajax({
                        type: "post",
                        url: "<?php echo url('upload/operate'); ?>",
                        async: false,
                        data: formData,
                        processData: false,
                        contentType: false,
                        dataType: 'json',
                        success: function(data) {
                            setTimeout(function() {
                                parent.layer.closeAll('loading');
                                if (data.status === 'success') {
                                    if (type === 0) { // 音频
                                        that.postData.voice = data.url;
                                        $('#audioFile').attr({'src': data.url, 'title': data.title});
                                        $('#audioFile').get(0).load();
                                        $('#audioFile').get(0).onloadedmetadata = function() {
                                            that.postData.voiceTime = parseInt($('#audioFile').get(0).duration);
                                        }
                                    } else if (type === 1) { // 视频
                                        that.postData.video = data.url;
                                        that.postData.tencentVideoVid = data.url;
                                        $('#videoFile').attr({'src': data.url, 'title': data.title});
                                        $('#videoFile').get(0).load();
                                    }
                                    event.target.value = '';
                                } else {
                                    layer.msg('上传失败，请检查上传配置');
                                    event.target.value = '';
                                }
                            }, 1600);
                        }
                    });
                }
            },
            
            // 捕获视频图像作为封面
            captureImage(video) {
                if (parseInt(this.postData.videoType) !== 0) {
                    return;
                }

                setTimeout(() => {
                    const canvas = document.createElement("canvas");
                    canvas.width = video.videoWidth * 0.8;
                    canvas.height = video.videoHeight * 0.8;
                    canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height);
                    const images = canvas.toDataURL("image/png");

                    const formData = new FormData();
                    formData.append('sngpic', this.base64ToBlob(images));

                    const that = this;
                    $.ajax({
                        type: "post",
                        url: "<?php echo url('upload/operate'); ?>",
                        async: false,
                        data: formData,
                        processData: false,
                        contentType: false,
                        dataType: 'json',
                        success: function(data) {
                            if (data.status === 'success') {
                                const videoUrl = $.trim(that.postData.video);
                                const videoWatchUrl = $.trim(that.postData.video); // 在原版中有videoWatch变量
                                const videoImg = $.trim(that.postData.videoImg);
                                if ((videoUrl !== videoWatchUrl) || videoImg === '') {
                                    that.postData.videoImg = data.url;
                                    const videoCoverImage = $('#videoCoverImage');
                                    videoCoverImage.attr('src', data.url);
                                    videoCoverImage.css({'width':'auto'});
                                    videoCoverImage.parent().parent().css({'background': 'none'});
                                }
                            }
                        }
                    });
                }, 500);
            },
            
            // Base64转Blob
            base64ToBlob(Base64) {
                const bytes = window.atob(Base64.split(',')[1]);
                const ab = new ArrayBuffer(bytes.length);
                const ia = new Uint8Array(ab);
                for (let i = 0; i < bytes.length; i++) {
                    ia[i] = bytes.charCodeAt(i);
                }
                return new Blob([ab], {type: 'image/png'});
            },
            
            // 加载第三方视频
            loadThirdPartyVideo() {
                const thirdPartyVideoUrl = this.postData.tencentVideoVid.trim();
                const videoType = parseInt(this.postData.videoType);

                if (thirdPartyVideoUrl === '') {
                    switch (videoType) {
                        case 0:
                        case 2:
                            layer.msg('视频地址不能为空');
                            break;
                        case 1:
                            layer.msg('视频vid不能为空');
                            break;
                    }
                    return;
                }

                switch (videoType) {
                    case 0:
                        this.postData.video = thirdPartyVideoUrl;
                        $('#videoFile').attr({'src': thirdPartyVideoUrl});
                        break;
                    case 1:
                        const urlPath = 'https://vv.video.qq.com/getinfo?platform=101001&charge=0&otype=json&defn=shd&vids=' + thirdPartyVideoUrl;
                        const that = this;
                        $.ajax({
                            url: urlPath,
                            dataType: "jsonp",
                            jsonp: "callback",
                            success: function(data) {
                                try {
                                    const fileName = data['vl']['vi'][0]['fn'];
                                    const fvkey = data['vl']['vi'][0]['fvkey'];
                                    const host = data['vl']['vi'][0]['ul']['ui'][0]['url'];
                                    $('#videoFile').attr({'src': host + fileName + '?vkey=' + fvkey});
                                    that.postData.videoImg = 'http://shp.qpic.cn/qqvideo/0/' + data['vl']['vi'][0]['vid'] + '/0';
                                } catch (e) {
                                    layer.msg('视频vid填写错误');
                                }
                            }
                        });
                        break;
                    case 2:
                        /* 插件相关 */
                        /*<?php if($videoParsePluginKey): ?>*/
                        $.post("<?php echo url('resolve/parse_url'); ?>", {'url': thirdPartyVideoUrl, 'isCover': 1}, function(data) {
                            if (data.code > 0) {
                                that.postData.title = data.title;
                                $('#videoFile').attr({'src': data.video});
                                that.postData.videoImg = data.cover;
                                $('#videoCoverImage').attr({'src': data.cover});
                            } else {
                                layer.msg(data.msg);
                            }
                        });
                        /*<?php endif; ?>*/
                        break;
                }
            },
            
            // 打开图片选择器
            openImageDialog(richText) {
                let dynamicUrl = "<?php echo url('images/dialogImages'); ?>&gclasid=0";
                if (richText === 1) {
                    dynamicUrl += "&dynamicStyle=richText";
                }
                layer.open({
                    type: 2,
                    anim: 2,
                    scrollbar: true,
                    area: ['900px', '600px'],
                    title: false,
                    closeBtn: 0,
                    shadeClose: true,
                    content: [dynamicUrl, 'no']
                });
            },
            
            // 选择文件
            selectFiles() {
                layer.open({
                    type: 2,
                    anim: 2,
                    title: false,
                    area: ['650px', '640px'],
                    scrollbar: false,
                    closeBtn: true,
                    shadeClose: true,
                    content: ["<?php echo url('cloud/steal'); ?>&uid=<?php echo $list['user_id']; ?>&pid=" + this.postData.filePid + "&type=1", 'no']
                });
            },
            
            // 设置文件信息
            setFileInfo(nid, fid, fileName, pid) {
                this.postData.ncId = nid;
                this.postData.fileId = fid;
                this.postData.filePid = pid || 0;
                this.fileNames = fileName;
            },
            
            // 表单提交
            submitForm() {
                const postData = {
                    prid: '<?php echo $list['id']; ?>',
                    title: this.postData.title.trim(),
                    titleColor: this.postData.titleColor,
                    patype: Number(this.postData.patype),
                    toryid: this.postData.toryid,
                    gambit: this.postData.gambit,
                    imgShowType: Number(this.postData.imgShowType),
                    content: ($.trim(editor.txt.html())).replace(/<p><\/p>/ig, '').replace(/<p><br><\/p>/ig, ''),
                    isBuy: Number(this.postData.isBuy),
                    buyPriceType: Number(this.postData.buyPriceType),
                    buyPrice: this.postData.buyPrice,
                    callPhone: this.postData.callPhone,
                    voice: this.postData.voice,
                    voiceTime: this.postData.voiceTime,
                    video: this.postData.video,
                    videoType: Number(this.postData.videoType),
                    videoImg: this.postData.videoImg,
                    tencentVideoVid: this.postData.tencentVideoVid,
                    adapterTime: this.adapterTime,
                    multipleImg: [],
                    feedToken: this.postData.feedToken
                };

                // 处理话题格式
                if (postData.gambit !== '') {
                    postData.gambit = postData.gambit.replace('#', '');
                    postData.gambit = '#' + postData.gambit + '#';
                }

                // 活动帖特殊处理
                if (postData.patype === 3) {
                    postData.briskApprove = this.postData.briskApprove;
                    postData.briskAddress = this.postData.briskAddress.trim();
                    postData.briskAddressLatitude = this.postData.briskAddressLatitude.trim();
                    postData.briskAddressLongitude = this.postData.briskAddressLongitude.trim();
                    postData.dateStartTime = this.postData.dateStartTime;
                    postData.dateEndTime = this.postData.dateEndTime;
                    postData.numberOfPeople = this.postData.numberOfPeople;

                    // 活动帖验证
                    if (postData.briskAddress === '') {
                        layer.msg('活动地址不能为空');
                        return;
                    }
                    if (postData.briskAddressLatitude === '') {
                        layer.msg('坐标纬度不能为空');
                        return;
                    }
                    if (postData.briskAddressLongitude === '') {
                        layer.msg('坐标经度不能为空');
                        return;
                    }
                    if (postData.dateStartTime === '') {
                        layer.msg('活动开始时间不能为空');
                        return;
                    }
                    if (postData.dateEndTime === '') {
                        layer.msg('活动结束时间不能为空');
                        return;
                    }
                    if (postData.numberOfPeople === '') {
                        layer.msg('活动人数不能为空');
                        return;
                    }
                }

                // 付费文件处理
                if (postData.isBuy > 1) {
                    postData.ncId = Number(this.postData.ncId);
                    postData.fileId = Number(this.postData.fileId);
                    postData.fileIsSell = Number(this.postData.fileIsSell);

                    if (postData.fileId === 0) {
                        layer.msg('请选择付费文件！');
                        return;
                    }
                }

                // 基本验证
                if (postData.title === '' && postData.content === '') {
                    layer.msg('帖子标题或内容必须填写一项');
                    return;
                }

                if (postData.isBuy > 0 && postData.buyPrice <= 0) {
                    layer.msg('付费帖子金额不能小于0.01');
                    return;
                }

                // 语音帖验证
                if (postData.patype === 1 && postData.voice === '') {
                    layer.msg('语音帖必须上传音频文件');
                    return;
                }

                // 视频帖验证
                if ((postData.patype === 2 && postData.videoType === 0 && postData.video === '') || (postData.patype === 2 && (postData.videoType === 1 || postData.videoType === 2) && postData.tencentVideoVid === '')) {
                    if (postData.videoImg === '' && postData.tencentVideoVid !== '') {
                        layer.msg('视频vid未加载，请点击"加载视频"');
                        return;
                    }

                    switch (postData.videoType) {
                        case 0:
                            layer.msg('请上传本地视频');
                            break;
                        case 1:
                            layer.msg('视频vid不能为空');
                            break;
                        case 2:
                            layer.msg('视频地址不能为空');
                            break;
                    }
                    return;
                }

                // 收集多图
                $('.multiple-img').each(function() {
                    postData.multipleImg.push($.trim($(this).attr('data-multiple-img')));
                });

                // 投票帖特殊处理
                let shocked = {};
                if (postData.patype === 4 || postData.patype === 5) {
                    shocked.pretendsKey = this.pvItems.map(item => item.id);
                    shocked.pretendsValue = this.pvItems.map(item => Number(item.cheat_ballot));
                    postData.voteDeadline = this.postData.voteDeadline.trim();
                }

                postData['feedToken'] = $.trim(this.postData.feedToken);
                if (postData.patype === 6 && postData['feedToken'] === '') {
                    layer.msg('请填写feed-token');
                    return;
                }

                // 发布时间验证
                if (postData.adapterTime === '') {
                    layer.msg('请选择发布时间');
                    return;
                }

                // 提交表单
                if (!this.isLock) {
                    this.isLock = true;
                    const loadIndex = layer.load(2, {
                        shadeClose: false,
                        shade: [0.5, '#000']
                    });

                    const that = this;
                    $.post("<?php echo url('essay/editWritings'); ?>", {postData, shocked}, function(data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function() {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function() {
                                that.isLock = false;
                                layer.close(loadIndex);
                            });
                        }
                    }, 'json');
                }
            }
        },
        mounted() {
            // 初始化富文本编辑器
            E = window.wangEditor;
            editor = new E('#detail');
            editor.customConfig.uploadImgServer = true;
            editor.create();
            E.secretSpace.init('#detail');
            E.fullscreen.init('#detail');
            editor.txt.html(decodeURIComponent(atob($('#detailBase64').html())));
            
            // 初始化界面状态
            this.changePostType();
            this.changeVideoType();
            
            // 初始化日期控件
            const that = this;
            laydate.render({
                elem: '#dateStartTime',
                value: this.postData.dateStartTime,
                type: 'datetime',
                done: function(value) {
                    that.postData.dateStartTime = value;
                }
            });
            laydate.render({
                elem: '#dateEndTime',
                value: this.postData.dateEndTime,
                type: 'datetime',
                done: function(value) {
                    that.postData.dateEndTime = value;
                }
            });
            laydate.render({
                elem: '#voteDeadline',
                value: this.postData.voteDeadline,
                type: 'datetime',
                done: function(value) {
                    that.postData.voteDeadline = value;
                }
            });
            
            // 初始化投票数据
            this.pvItems = [];
            /*<?php if($pvInfo): if(is_array($pvInfo) || $pvInfo instanceof \think\Collection || $pvInfo instanceof \think\Paginator): $i = 0; $__LIST__ = $pvInfo;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>*/
            this.pvItems.push({ id: '<?php echo $vo['id']; ?>', ballot_name: '<?php echo $vo['ballot_name']; ?>', cheat_ballot: '<?php echo $vo['cheat_ballot']; ?>' });
            /*<?php endforeach; endif; else: echo "" ;endif; endif; ?>*/
        }
    });
</script>

</body>
</html>