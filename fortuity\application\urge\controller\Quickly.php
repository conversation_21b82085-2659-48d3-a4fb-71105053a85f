<?php

namespace app\urge\controller;

use app\common\Playful;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

defined('IN_IA') or exit('Access Denied');

class Quickly extends Base
{
    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, array(), 'view'));
        }
        // ============================== 授权检测 End ==============================
    }


    /*
     * 首页悬浮图标配置
     */
    public function floating_module()
    {
        if (request()->isPost() && request()->isAjax()) {
            // 获取并验证输入参数
            $is_enable = input('post.is_enable');
            $icon_url = input('post.icon_url');
            $position_fixed = input('post.position_fixed');
            $bottom_offset = input('post.bottom_offset');
            $target_type = input('post.target_type');
            $target_path = input('post.target_path');
            $target_appid = input('post.target_appid');

            // 检查必填参数
            if ($is_enable === null || $is_enable === '') {
                return json(['code' => 0, 'msg' => '是否开启不能为空']);
            }

            if ($position_fixed === null || $position_fixed === '') {
                return json(['code' => 0, 'msg' => '图片位置不能为空']);
            }

            if ($bottom_offset === null || $bottom_offset === '') {
                return json(['code' => 0, 'msg' => '底部偏移距离不能为空']);
            }

            if ($target_type === null || $target_type === '') {
                return json(['code' => 0, 'msg' => '跳转类型不能为空']);
            }

            $data['is_enable'] = intval($is_enable);
            $data['icon_url'] = trim($icon_url);
            $data['position_fixed'] = intval($position_fixed);
            $data['bottom_offset'] = intval($bottom_offset);
            $data['target_type'] = intval($target_type);
            $data['target_path'] = trim($target_path);
            $data['target_appid'] = trim($target_appid);

            // 验证是否开启设置
            if (!in_array($data['is_enable'], [0, 1])) {
                return json(['code' => 0, 'msg' => '是否开启参数错误']);
            }

            // 验证图片位置设置
            if (!in_array($data['position_fixed'], [0, 1])) {
                return json(['code' => 0, 'msg' => '图片位置参数错误']);
            }

            // 验证底部偏移距离
            if ($data['bottom_offset'] < 0) {
                return json(['code' => 0, 'msg' => '底部偏移距离不能小于0']);
            }

            if ($data['bottom_offset'] > 9999) {
                return json(['code' => 0, 'msg' => '底部偏移距离不能超过9999px']);
            }

            // 验证跳转类型
            if (!in_array($data['target_type'], [0, 1, 2, 3])) {
                return json(['code' => 0, 'msg' => '跳转类型参数错误']);
            }

            // 验证图标URL长度（如果不为空）
            if (strlen($data['icon_url']) > 255) {
                return json(['code' => 0, 'msg' => '图标地址长度不能超过255个字符']);
            }

            // 根据跳转类型验证相关参数
            if ($data['target_type'] == 1 || $data['target_type'] == 2) {
                // 内部链接或外部链接需要链接地址
                if (empty($data['target_path'])) {
                    return json(['code' => 0, 'msg' => '跳转链接不能为空']);
                }

                // 验证链接长度
                if (strlen($data['target_path']) > 255) {
                    return json(['code' => 0, 'msg' => '跳转链接长度不能超过255个字符']);
                }

                // 外部链接需要验证URL格式
                if ($data['target_type'] == 2) {
                    if (!filter_var($data['target_path'], FILTER_VALIDATE_URL)) {
                        return json(['code' => 0, 'msg' => '外部链接格式不正确']);
                    }
                }
            }

            if ($data['target_type'] == 3) {
                // 小程序跳转需要appid
                if (empty($data['target_appid'])) {
                    return json(['code' => 0, 'msg' => '小程序appid不能为空']);
                }

                // 验证AppID长度
                if (strlen($data['target_appid']) > 255) {
                    return json(['code' => 0, 'msg' => '小程序AppID长度不能超过255个字符']);
                }
            }

            // 获取或创建配置，然后更新
            try {
                $config = $this->getFloatingConfig();
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => 'Failed to initialize configuration: ' . $e->getMessage()]);
            }

            Db::startTrans();
            try {
                if ($config && isset($config['id'])) {
                    // 更新配置
                    Db::name('home_floating_icon')
                        ->where('much_id', $this->much_id)
                        ->update($data);
                } else {
                    // 创建新配置
                    $data['much_id'] = $this->much_id;
                    Db::name('home_floating_icon')->insert($data);
                }

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }

            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            // 获取配置，如果不存在会自动创建
            try {
                $config = $this->getFloatingConfig();
            } catch (\Exception $e) {
                $this->error('Failed to initialize configuration: ' . $e->getMessage());
            }

            $this->assign('config', $config);
            return $this->fetch();
        }
    }

    /*
     * 获取首页悬浮图标配置（统一方法）
     * 如果没有配置则自动创建默认配置
     */
    private function getFloatingConfig()
    {
        // 获取当前配置
        $config = Db::name('home_floating_icon')
            ->where('much_id', $this->much_id)
            ->find();

        // 如果没有配置，创建默认配置
        if (!$config) {
            $defaultConfig = array(
                'is_enable' => 0,                                          // 默认关闭
                'icon_url' => null,                                        // 默认为null，API会自动处理
                'position_fixed' => 1,                                     // 默认右侧
                'bottom_offset' => 200,                                    // 默认距离底部200px
                'target_type' => 0,                                        // 默认客服
                'target_path' => '',                                       // 默认空链接
                'target_appid' => '',                                      // 默认空appid
                'much_id' => $this->much_id
            );

            Db::startTrans();
            try {
                Db::name('home_floating_icon')->insert($defaultConfig);
                Db::commit();

                // 重新获取插入的配置
                $config = Db::name('home_floating_icon')
                    ->where('much_id', $this->much_id)
                    ->find();
            } catch (\Exception $e) {
                Db::rollback();
                // 插入失败时抛出异常，让调用方处理
                throw new \Exception('Failed to initialize floating icon configuration: ' . $e->getMessage());
            }
        }

        return $config;
    }
}