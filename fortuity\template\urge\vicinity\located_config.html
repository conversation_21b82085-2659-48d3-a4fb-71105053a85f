{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        padding: 24px;
        margin: 0;
    }

    /* 标题区域 */
    .portlet-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 16px;
        margin-bottom: 24px;
        border-bottom: 1px solid #eee;
    }

    .caption {
        font-size: 18px;
        color: #333;
        font-weight: 500;
        display: flex;
        align-items: center;
    }

    .caption .am-icon-cog {
        margin-right: 8px;
        color: #666;
    }

    /* 表单样式 */
    .am-form-horizontal .am-form-group {
        margin-bottom: 24px;
    }

    .am-form-horizontal .am-form-label {
        text-align: right;
        font-weight: 500;
        padding-top: 15px;
        font-size: 14px;
        color: #333;
        line-height: 16px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    .am-form-horizontal .am-form-group .am-u-sm-9 {
        padding-left: 20px;
    }

    .am-form-horizontal input[type='text'],
    .am-form-horizontal input[type='number'] {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 8px 12px;
        height: 40px;
        width: 100%;
        font-size: 14px;
        transition: border-color 0.3s;
        box-sizing: border-box;
    }

    .am-form-horizontal input:focus {
        border-color: #1890ff;
        outline: none;
        box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
    }

    /* 单选框样式 */
    .radio-group {
        display: flex;
        gap: 12px;
        align-items: center;
        padding-top: 8px;
    }

    .radio-item {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 10px 16px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        background: #fafafa;
        transition: all 0.3s ease;
        min-width: 60px;
        justify-content: center;
        height: 36px;
        box-sizing: border-box;
    }

    .radio-item:hover {
        border-color: #1890ff;
        background: #f0f8ff;
    }

    .radio-item input[type="radio"] {
        margin: 0 6px 0 0;
        padding: 0;
        width: 16px;
        height: 16px;
        accent-color: #1890ff;
        flex-shrink: 0;
        position: relative;
        top: 0;
    }

    .radio-item input[type="radio"]:checked {
        accent-color: #1890ff;
    }

    .radio-item span {
        cursor: pointer;
        font-size: 14px;
        color: #333;
        margin: 0;
        font-weight: 500;
        line-height: 16px;
        height: 16px;
        display: flex;
        align-items: center;
    }

    /* 选中状态的单选框项 */
    .radio-item:has(input:checked) {
        border-color: #1890ff;
        background: #e6f7ff;
        box-shadow: 0 2px 4px rgba(24,144,255,0.1);
    }

    .radio-item:has(input:checked) span {
        color: #1890ff;
    }

    /* 帮助文本 */
    .help-text {
        color: #666;
        font-size: 13px;
        margin-top: 8px;
        padding: 6px 0;
        line-height: 1.4;
    }

    /* 按钮样式 */
    .am-btn {
        padding: 8px 24px;
        font-size: 14px;
        border-radius: 4px;
        transition: all 0.3s;
        margin-right: 12px;
    }

    .am-btn-primary {
        background: #1890ff;
        color: white;
        border: 1px solid #1890ff;
        min-width: 100px;
    }

    .am-btn-primary:hover {
        background: #40a9ff;
        border-color: #40a9ff;
    }

    .am-btn-default {
        background: #f8f9fa;
        color: #666;
        border: 1px solid #d9d9d9;
        min-width: 80px;
    }

    .am-btn-default:hover {
        background: #e9ecef;
        border-color: #ccc;
    }

    /* 按钮组间距 */
    .am-form-group:last-child {
        margin-top: 40px;
        padding-top: 24px;
        border-top: 1px solid #f0f0f0;
    }

    /* 禁用状态 */
    .am-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* 错误信息 */
    .error-message {
        color: #ff4d4f;
        font-size: 13px;
        margin-top: 8px;
        padding: 8px 12px;
        background-color: #fff2f0;
        border: 1px solid #ffccc7;
        border-radius: 4px;
        display: flex;
        align-items: center;
    }

    .error-message::before {
        content: "⚠";
        margin-right: 6px;
        font-weight: bold;
    }

    .has-error input {
        border-color: #ff4d4f;
        box-shadow: 0 0 0 2px rgba(255,77,79,0.2);
    }

    /* 禁用状态 */
    .am-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* 错误信息 */
    .error-message {
        color: #ff4d4f;
        font-size: 13px;
        margin-top: 8px;
        padding: 8px 12px;
        background-color: #fff2f0;
        border: 1px solid #ffccc7;
        border-radius: 4px;
        display: flex;
        align-items: center;
    }

    .error-message::before {
        content: "⚠";
        margin-right: 6px;
        font-weight: bold;
    }

    .has-error input {
        border-color: #ff4d4f;
        box-shadow: 0 0 0 2px rgba(255,77,79,0.2);
    }
</style>

<div id="app" class="tpl-portlet-components" v-cloak>
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-cog"></span> 附近圈子配置
        </div>
    </div>
    
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-8 am-u-md-offset-2">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            距离半径
                        </label>
                        <div class="am-u-sm-9" :class="{'has-error': errors.distance_radius}">
                            <input 
                                type="number" 
                                v-model="config.distance_radius" 
                                placeholder="请输入距离半径（单位：米）"
                                min="1"
                                max="6371000"
                                step="0.01">
                            <div class="help-text">设置用户搜索附近圈子的距离范围，单位为米。可设置从本地（500米）到全球（6371000米，地球半径）的任意范围</div>
                            <div v-if="errors.distance_radius" class="error-message">{{errors.distance_radius}}</div>
                        </div>
                    </div>
                    
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            首页展示
                        </label>
                        <div class="am-u-sm-9">
                            <div class="radio-group">
                                <label class="radio-item" for="show_yes">
                                    <input type="radio" id="show_yes" v-model="config.is_home_show" :value="1">
                                    <span>是</span>
                                </label>
                                <label class="radio-item" for="show_no">
                                    <input type="radio" id="show_no" v-model="config.is_home_show" :value="0">
                                    <span>否</span>
                                </label>
                            </div>
                            <div class="help-text">设置是否在首页展示附近圈子功能</div>
                        </div>
                    </div>
                    
                    <div class="am-form-group">
                        <div class="am-u-sm-8 am-u-sm-offset-4">
                            <button
                                type="button"
                                class="am-btn am-btn-primary"
                                @click="saveConfig"
                                :disabled="isLoading">
                                {{isLoading ? '保存中...' : '保存配置'}}
                            </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    var vm = new Vue({
        el: '#app',
        data() {
            return {
                config: {
                    distance_radius: '{$config.distance_radius|default=1000}',
                    is_home_show: '{$config.is_home_show|default=1}'
                },
                isLoading: false,
                errors: {
                    distance_radius: ''
                }
            }
        },
        methods: {
            // 清除错误信息
            clearErrors: function() {
                this.errors = {
                    distance_radius: ''
                };
            },
            
            // 表单验证
            validateForm: function() {
                this.clearErrors();
                let isValid = true;
                
                // 验证距离半径
                const radius = parseFloat(this.config.distance_radius);
                if (!radius || radius <= 0) {
                    this.errors.distance_radius = '距离半径必须大于0';
                    isValid = false;
                } else if (radius > 6371000) {
                    this.errors.distance_radius = '距离半径不能超过6371000米（地球半径约6371公里）';
                    isValid = false;
                }
                
                return isValid;
            },
            
            // 保存配置
            saveConfig: function () {
                if (!this.validateForm()) {
                    return;
                }
                
                if (this.isLoading) {
                    return;
                }
                
                this.isLoading = true;
                
                $.post("{:url('vicinity/located_config')}", {
                    distance_radius: this.config.distance_radius,
                    is_home_show: this.config.is_home_show
                }, (data) => {
                    this.isLoading = false;
                    if (data.code > 0) {
                        layer.msg(data.msg, { icon: 1, time: 1500 }, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, { icon: 5, time: 2500 });
                    }
                }, 'json').fail(() => {
                    this.isLoading = false;
                    layer.msg('网络错误，请稍后重试', {icon: 5, time: 2500});
                });
            }
        }
    });
</script>
{/block}
