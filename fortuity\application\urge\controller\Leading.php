<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\common\Playful;
use app\common\Remotely;
use think\Cache;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

#导航栏
class Leading extends Base
{

    //首页导航
    public function traction()
    {
        $url = $this->defaultQuery();
        $list = Db::name('polling')->where('ad_type', 2)->where('much_id', $this->much_id)->order('scores', 'asc')->paginate(10, false, ['query' => ['s' => $url]]);
        $this->assign('list', $list);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //添加导航
    public function rutraction()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            $data['ad_type'] = 2;
            if ($data['practice_type'] == 1) {
                $data['url'] = str_replace('?', '&', $data['url']);
            }
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('polling')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        return $this->fetch();
    }

    //编辑导航
    public function uptraction()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            $usid = $data['usid'];
            unset($data['usid']);
            if ($data['practice_type'] == 1) {
                $data['url'] = str_replace('?', '&', $data['url']);
            }
            Db::startTrans();
            try {
                Db::name('polling')->where('id', $usid)->where('much_id', $this->much_id)->update($data);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result === true) {
                return json(['code' => 1, 'msg' => '保存成功']);
            }
        }
        $uplid = request()->get('uplid', '');
        if ($uplid) {
            $poList = Db::name('polling')->where('id', $uplid)->where('much_id', $this->much_id)->find();
            if ($poList) {
                $this->assign('list', $poList);
                return $this->fetch();
            } else {
                $this->redirect('systems/symbol');
            }
        } else {
            $this->redirect('systems/symbol');
        }
    }

    //圈子禁言
    public function muted()
    {
        if (request()->isPost() && request()->isAjax()) {
            $ubid = request()->post('ubid');
            $ubInfo = Db::name('user_banned')->where('id', $ubid)->where('much_id', $this->much_id)->find();
            if ($ubInfo) {
                return trim($ubInfo['beget']) != '' ? $ubInfo['beget'] : '未填写禁言原因';
            } else {
                return 'error';
            }
        } else {
            $url = $this->defaultQuery();
            $tyid = request()->get('tyid', 0);
            $toryInfo = Db::name('territory')->where('id', $tyid)->where('much_id', $this->much_id)->find();
            if ($toryInfo) {
                $hazy_name = request()->get('hazy_name', '');
                $list = Db::name('user_banned')
                    ->alias('ub')
                    ->join('user us', 'ub.user_id=us.id', 'left')
                    ->where('us.user_nick_name|us.user_wechat_open_id', 'like', "%{$hazy_name}%")
                    ->where('ub.tory_id', $tyid)
                    ->where('ub.refer_time', '>', time())
                    ->where('ub.much_id', $this->much_id)
                    ->field('ub.*,us.user_head_sculpture,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
                    ->order('ub.id', 'asc')
                    ->paginate(10, false, ['query' => ['s' => $url, 'tyid' => $tyid, 'hazy_name' => $hazy_name]]);
                $this->assign('toryInfo', $toryInfo);
                $this->assign('list', $list);
                $this->assign('hazy_name', $hazy_name);
                $this->assign('tyid', $tyid);
                $page = request()->get('page', 1);
                $this->assign('page', $page);
                return $this->fetch();
            } else {
                $this->error('参数错误', 'compass/fence');
            }
        }
    }

    //圈子禁言操作
    public function commute()
    {
        if (request()->isPost() && request()->isAjax()) {
            $exponent = request()->post('exponent', 0);
            if ($exponent != 1 || $exponent != 2) {
                switch (intval($exponent)) {
                    case 1:
                        $openid = trim(request()->post('openid'));
                        $userInfo = Db::name('user')->where('user_wechat_open_id', $openid)->where('much_id', $this->much_id)->find();
                        if (!$userInfo) {
                            return json(['code' => 0, 'msg' => '用户openid填写错误']);
                        }
                        $tyid = request()->post('tyid');
                        $toryInfo = Db::name('territory')->where('id', $tyid)->where('much_id', $this->much_id)->find();
                        if (!$toryInfo) {
                            return json(['code' => 0, 'msg' => '参数错误']);
                        }
                        $tell = request()->post('tell');
                        $decideTime = intval(request()->post('containTime'));
                        $containTime = time() + ($decideTime * 86400);
                        break;
                    case 2:
                        $ubid = request()->post('ubid', 0);
                        break;
                }
                Db::startTrans();
                try {
                    switch (intval($exponent)) {
                        case 1:
                            Db::name('user_banned')->where('tory_id', $tyid)->where('user_id', $userInfo['id'])->where('much_id', $this->much_id)->delete();
                            Db::name('user_banned')->insert(['tory_id' => $tyid, 'refer_id' => 0, 'refer_type' => 0, 'user_id' => $userInfo['id'], 'beget' => $tell, 'refer_time' => $containTime, 'much_id' => $this->much_id]);
                            break;
                        case 2:
                            Db::name('user_banned')->where('id', $ubid)->where('much_id', $this->much_id)->delete();
                            break;
                    }
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
                return json(['code' => 1, 'msg' => '操作成功']);
            } else {
                $this->error('参数错误', 'compass/fence');
            }
        } else {
            $this->error('参数错误', 'compass/fence');
        }
    }

    //投票详情
    public function votingPlenary()
    {
        $url = $this->defaultQuery();
        $pvid = request()->get('pvid');
        $pvInfo = Db::name('paper_vote')->where('id', $pvid)->where('much_id', $this->much_id)->find();
        if (!$pvInfo) {
            $this->redirect('essay/index');
        }
        $hazy_name = trim(request()->get('hazy_name', ''));
        $hazy_name != '' && $hazy_name = emoji_encode($hazy_name);
        $utList = Db::name('user_vote')
            ->alias('uv')
            ->join('user us', 'uv.user_id = us.id')
            ->where('uv.pv_id', $pvid)
            ->where('us.user_nick_name', 'like', "%{$hazy_name}%")
            ->where('us.tourist', 0)
            ->where('uv.much_id&us.much_id', $this->much_id)
            ->field('us.user_head_sculpture,us.user_nick_name,us.user_wechat_open_id,uv.decide_time')
            ->paginate(10, false, ['query' => ['s' => $url, 'pvid' => $pvid, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $utList);
        $this->assign('pvInfo', $pvInfo);
        $this->assign('hazy_name', emoji_decode($hazy_name));
        $this->assign('pvid', $pvid);
        $page = request()->get('page', 1);
        $pageCount = $utList->count();
        if ($pageCount < 1 && $page != 1) {
            $this->emptyDataRedirect(['hazy_name' => $hazy_name]);
        }
        $this->assign('page', $page);
        return $this->fetch();
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {

        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, array(), 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    /**
     * ams s3
     * @return mixed
     */
    public function compliant()
    {
        if (!Remotely::isUnLockProperty(base64_decode('6YCa55So5a2Y5YKo'))) {
            abort(404);
        }
        //  从数据库中查询远程附件信息
        $awsFollowInfo = Db::name('outlying')->where('much_id', $this->much_id)->value('aws_follow');
        //  将远程附件信息解码为数组
        $awsFollowInfo = $awsFollowInfo ? json_decode($awsFollowInfo, true) : array();
        //  将远程附件信息解码为数组
        if (is_array($awsFollowInfo)) {
            foreach ($awsFollowInfo as $key => $value) {
                $awsFollowInfo[$key] = authcode($value, 'DECODE', 'YuluoNetwork');
            }
        }
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            $fieldsMap = [
                'aws_key' => 'key',
                'aws_secret' => 'secret',
                'aws_bucket' => 'bucket',
                'aws_region' => 'region',
                'aws_endpoint' => 'endpoint',
                'aws_force_path_style' => 'forcePathStyle',
                'aws_url' => 'url',
            ];
            //  定义在 POST 为空时不从数据库回填的字段
            $excludedFromDbFallback = ['region'];
            //  将远程附件信息编码为数组
            $quickData = array();
            //  遍历映射关系，从 POST 请求中动态获取所有开关的数据
            foreach ($fieldsMap as $dbKey => $postKey) {
                //  如果 POST 请求中没有该字段，并且该字段不在 $excludedFromDbFallback 中，则从远程附件信息中获取
                $value = trim(request()->post($postKey)) ?: (!in_array($postKey, $excludedFromDbFallback) && isset($awsFollowInfo[$dbKey]) ? $awsFollowInfo[$dbKey] : '');
                //  将值编码为加密字符串
                $quickData[$dbKey] = authcode($value, 'ENCODE', 'YuluoNetwork');
            }
            //  将远程附件信息编码为 JSON 字符串
            $quickData = ['aws_follow' => json_encode($quickData, 320)];
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 outlying 表中对应 much_id 的数据，并清除相关缓存
                Db::name('outlying')->where('much_id', $this->much_id)->update($quickData);
                // 提交事务
                Db::commit();
                // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            } finally {
                // 清除缓存
                Cache::rm("outlying_0_{$this->much_id}");
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            // 将远程附件信息分配到视图
            $this->assign('list', $awsFollowInfo);
            // 渲染并返回视图
            return $this->fetch();
        }
    }

    public function pan123()
    {
        if (!Remotely::isUnLockProperty(base64_decode('MTIz5LqR55uY'))) {
            abort(404);
        }

        //  从数据库中查询远程附件信息
        $pan123FollowInfo = Db::name('outlying')->where('much_id', $this->much_id)->value('pan123_follow');
        //  将远程附件信息解码为数组
        $pan123FollowInfo = $pan123FollowInfo ? json_decode($pan123FollowInfo, true) : array();
        //  将远程附件信息解码为数组
        if (is_array($pan123FollowInfo)) {
            foreach ($pan123FollowInfo as $key => $value) {
                $pan123FollowInfo[$key] = authcode($value, 'DECODE', 'YuluoNetwork');
            }
        }
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            $fieldsMap = [
                'pan123_client_id' => 'clientId',
                'pan123_client_secret' => 'clientSecret',
            ];
            //  定义在 POST 为空时不从数据库回填的字段
            $excludedFromDbFallback = array();
            //  将远程附件信息编码为数组
            $quickData = array();
            //  遍历映射关系，从 POST 请求中动态获取所有开关的数据
            foreach ($fieldsMap as $dbKey => $postKey) {
                //  如果 POST 请求中没有该字段，并且该字段不在 $excludedFromDbFallback 中，则从远程附件信息中获取
                $value = trim(request()->post($postKey)) ?: (!in_array($postKey, $excludedFromDbFallback) && isset($pan123FollowInfo[$dbKey]) ? $pan123FollowInfo[$dbKey] : '');
                //  将值编码为加密字符串
                $quickData[$dbKey] = authcode($value, 'ENCODE', 'YuluoNetwork');
            }
            //  将远程附件信息编码为 JSON 字符串
            $quickData = ['pan123_follow' => json_encode($quickData, 320)];
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 outlying 表中对应 much_id 的数据，并清除相关缓存
                Db::name('outlying')->where('much_id', $this->much_id)->update($quickData);
                // 提交事务
                Db::commit();
                // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            } finally {
                // 清除缓存
                Cache::rm("outlying_0_{$this->much_id}");
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            // 将远程附件信息分配到视图
            $this->assign('list', $pan123FollowInfo);
            // 渲染并返回视图
            return $this->fetch();
        }
    }
}
