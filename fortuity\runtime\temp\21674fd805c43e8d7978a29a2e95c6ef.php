<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:87:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/user/theoretic.html";i:1742463553;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755516152;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a></li>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>.tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}.tpl-portlet-components:after {content: "";display: table;clear: both;}.am-form {position: relative;overflow: visible;}.portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}.caption {font-size: 16px;color: #23b7e5;font-weight: 500;}.caption .am-icon-user {margin-right: 5px;color: #23b7e5;}.tpl-portlet-input {position: relative;}.tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}.tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}.tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}.customize-span {display: inline-block;padding: 7px 15px;background: #23b7e5;color: white;border-radius: 3px;font-size: 13px;cursor: pointer;transition: all 0.3s;}.customize-span:hover {background: #1a9fd4;box-shadow: 0 2px 5px rgba(26,159,212,0.2);}.customize-span .am-icon-adn {margin-right: 5px;}.am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}.am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}.am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}.am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}.am-table > tbody > tr:hover > td {background-color: #f5fafd;}.user-avatar {width: 36px;height: 36px;border-radius: 50%;object-fit: cover;border: 1px solid #eee;}.action-btn {display: inline-block;padding: 4px 8px;background: #fff;border: 1px solid #ddd;color: #23b7e5;border-radius: 3px;font-size: 12px;cursor: pointer;transition: all 0.3s;}.action-btn:hover {border-color: #23b7e5;color: #23b7e5;background-color: #f5fafd;}.action-btn .am-icon-pencil-square-o {margin-right: 3px;}.am-btn-group {display: inline-flex;border-radius: 3px;overflow: visible;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}.am-btn-group > .am-btn:first-child {margin-top: 0;border-top-right-radius: 0;border-bottom-right-radius: 0;}.am-btn-group > .am-btn {padding: 4px 10px;background: #fafafa;border: 1px solid #e8e8e8;color: #666;font-size: 12px;position: relative;height: 28px;line-height: 20px;}.am-dropdown {position: static;}.am-dropdown-toggle {padding: 4px 8px !important;background: #fafafa;border: 1px solid #e8e8e8;border-left: none;height: 28px;font-size: 12px;color: #666;}.am-dropdown-toggle:hover, .am-btn-group > .am-btn:hover {background: #f5f5f5;}.am-dropdown-content {position: absolute;top: 100%;left: 0;z-index: 1020 !important;display: none;min-width: 120px;padding: 0;margin: 0;margin-top: 5px;text-align: left;background-color: #fff;border: 1px solid #eee;border-radius: 3px;box-shadow: 0 3px 8px rgba(0,0,0,0.1);}.am-dropdown.am-active .am-dropdown-content {display: block !important;visibility: visible !important;opacity: 1 !important;}.am-dropdown-content li {border-bottom: 1px solid #f3f3f3;list-style: none;}.am-dropdown-content li:last-child {border-bottom: none;}.am-dropdown-content li a {padding: 8px 10px !important;text-align: center;font-size: 12px;color: #666 !important;transition: all 0.2s;display: block;text-decoration: none;}.am-dropdown-content li a:hover {background-color: #f5fafd;color: #23b7e5 !important;}.am-pagination {margin: 10px 0;}.am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}.am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}.am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}.am-modal-dialog {border-radius: 4px;overflow: hidden;box-shadow: 0 5px 15px rgba(0,0,0,0.2);}.am-modal-hd {background: #f9f9f9;padding: 10px 15px;border-bottom: 1px solid #eee;}.am-modal-bd {padding: 15px;}.am-form-group {margin-bottom: 15px;}.am-form-label {font-size: 13px;color: #666;font-weight: normal;}.tpl-form-input {height: 32px;padding: 6px 10px;font-size: 13px;border: 1px solid #e8e8e8;border-radius: 3px;transition: all 0.3s;}.tpl-form-input:focus {border-color: #23b7e5;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}.confirm-btn {background: #23b7e5;color: white;border: none;border-radius: 3px;padding: 6px 15px;font-size: 13px;cursor: pointer;transition: all 0.3s;}.confirm-btn:hover {background: #1a9fd4;box-shadow: 0 2px 5px rgba(26,159,212,0.2);}.am-btn-group.am-btn-group-sm {position: relative;}.am-btn-group.am-btn-group-sm .am-dropdown-content {position: absolute;top: 100%;left: auto;right: 0;}.tpl-block {position: relative;overflow: visible;}body {padding-bottom: 50px;}.am-u-sm-12 {overflow: visible;}.am-table tr:last-child .am-dropdown-content {bottom: auto;top: auto;margin-top: -120px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-user"></span> 虚拟用户
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="<?php echo $hazy_name; ?>" placeholder="搜索用户名...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <span class="customize-span" onclick="saloof();">
                        <span class="am-icon-adn"></span> 新增虚拟用户
                    </span>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped">
                        <thead>
                        <tr>
                            <th width="6%">UID</th>
                            <th width="6%">用户头像</th>
                            <th width="11%">用户昵称</th>
                            <th width="10%">用户性别</th>
                            <th width="10%">用户等级</th>
                            <th width="12%">随机码 ( 部分场景可替代openid使用 )</th>
                            <th width="15%">社交功能</th>
                            <th width="10%">添加时间</th>
                            <th width="10%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): $i = 0; $__LIST__ = $list;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <tr>
                            <td><?php echo $vo['id']; ?></td>
                            <td>
                                <img src="<?php echo $vo['user_head_sculpture']; ?>" class="user-avatar">
                            </td>
                            <td>
                                <span title="<?php echo emoji_decode($vo['user_nick_name']); ?>">
                                    <?php echo subtext(emoji_decode($vo['user_nick_name']),10); ?>
                                </span>
                            </td>
                            <td><?php if($vo['gender']==2): ?> 女 <?php else: ?> 男 <?php endif; ?></td>
                            <td>Lv.<?php echo $vo['level']; ?></td>
                            <td><?php echo $vo['user_wechat_open_id']; ?></td>
                            <td>
                                <div class="am-btn-group am-btn-group-sm" style="display: inline-flex;justify-content: center;position: relative;">
                                    <button class="am-btn" style="z-index: 1;">功能列表</button>
                                    <div class="am-dropdown" data-am-dropdown>
                                        <button class="am-btn am-dropdown-toggle" data-am-dropdown-toggle style="z-index: 1;">
                                            <span class="am-icon-caret-down"></span>
                                        </button>
                                        <ul class="am-dropdown-content" style="width: 120px;min-width: 120px;z-index: 1021;">
                                            <li>
                                                <a href="javascript:void(0);" onclick="urelease('0','<?php echo $vo['id']; ?>');">
                                                    发布帖子
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" onclick="urelease('3','<?php echo $vo['id']; ?>');">
                                                    回复帖子
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" onclick="urelease('4','<?php echo $vo['id']; ?>');">
                                                    发布秘密
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" onclick="urelease('5','<?php echo $vo['id']; ?>');">
                                                    回复秘密
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" class="euModalOpen" data-euid="<?php echo $vo['id']; ?>" data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 400, height: 295}">
                                                    赠送礼物
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                            <td><?php echo date('Y-m-d H:i:s',$vo['user_reg_time']); ?></td>
                            <td>
                                <span class="action-btn" onclick="uploof('<?php echo $vo['id']; ?>');">
                                    <span class="am-icon-pencil-square-o"></span> 编辑
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            <?php echo $list->render(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
        <div class="am-modal-dialog">
            <div class="am-modal-hd">
                <span>赠送礼物</span>
                <a id="euModalClose" href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <input id="virtual-user" type="hidden" value="0"/>
            <div class="am-modal-bd am-form">
                <div class="am-form-group">
                    <label class="am-u-sm-4 am-form-label">受赠用户</label>
                    <div class="am-u-sm-8">
                        <input type="text" id="user-openid" oninput="extolled(this);" class="tpl-form-input" placeholder="请输入用户openid">
                        <span id="sehred" style="position: absolute;left: 0px; color: blue;font-size: 12px;">　</span>
                    </div>
                </div>
                <div class="am-form-group">
                    <label class="am-u-sm-4 am-form-label">礼物列表</label>
                    <div class="am-u-sm-8">
                        <select id="tribute-number" class="tpl-form-input">
                            <?php if(is_array($tribute) || $tribute instanceof \think\Collection || $tribute instanceof \think\Paginator): $i = 0; $__LIST__ = $tribute;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo $vo['id']; ?>"><?php echo $vo['tr_name']; ?> <?php echo $vo['tr_conch']; ?> ( <?php echo $defaultNavigate['currency']; ?> )</option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div>
                <div class="am-form-group">
                    <label class="am-u-sm-4 am-form-label">礼物数量</label>
                    <div class="am-u-sm-8">
                        <input type="text" id="tribute-quantity" oninput="digitalCheck(this);" class="tpl-form-input" placeholder="请输入赠送礼物数量">
                    </div>
                </div>
                <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top:15px;">
                    <button type="button" class="confirm-btn" onclick="sendGifts();">确定赠送</button>
                </div>
            </div>
        </div>
    </div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<?php if($much_role=='founder'): ?>
<script>
    var updateFix = function () {
        layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("<?php echo url('index/repairMissing'); ?>", function (data) {
                if (data !== false) {
                    layer.msg('数据库缺失字段修复完成', {time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('修复失败', {time: 1600}, function () {
                        location.reload();
                    });
                }
            });
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }
</script>
<?php endif; ?>
<script>
    new Vue({
        el: '#menu',
        data() {
            return {
                menuActive: '<?php echo $menuActive; ?>',
                menuList: []
            };
        },
        created() {
            const menuList = '<?php echo $menuList; ?>';
            if (menuList !== '') {
                const menuData = JSON.parse(decodeURIComponent(atob(menuList)));
                for (let i = 0; i < menuData.length; i++) {
                    if (menuData[i].pid === 0) {
                        menuData[i].childMenuItems = [];
                        this.menuList.push(menuData[i]);
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        if (menuData[i].pid === this.menuList[j].id) {
                            const childItem = JSON.parse(JSON.stringify(menuData[i]));
                            childItem.grandsonList = [];
                            this.menuList[j].childMenuItems.push(childItem);
                            break;
                        }
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        for (let k = 0; k < this.menuList[j].childMenuItems.length; k++) {
                            if (menuData[i].pid === this.menuList[j].childMenuItems[k].id) {
                                this.menuList[j].childMenuItems[k].grandsonList.push(menuData[i]);
                                break;
                            }
                        }
                    }
                }
            }
        }
    });

    $(function () {
        setInterval(reballot, 15000);
    });

    var reballot = function () {
        $.getJSON("<?php echo url('ordinary'); ?>", function (data) {
            var i = 0;
            if (data.notice > 0 || data.vacant > 0) {
                i++;
            }
            $('#notice-0,#notice-1').text(data.notice);
            $('#vacant-0,#vacant-1').text(data.vacant);
            if ((data.notice + data.vacant) <= data.preCount) {
                i = 0;
            } else {
                $.post("<?php echo url('receipt'); ?>", {'multiply': (data.notice + data.vacant)});
            }
            if (i > 0) {
                var player = $("#backPlayer")[0];
                player.play();
            }
        });
    }

    var retakeCache = function () {
        $.get("<?php echo url('index/purgeCache'); ?>", function () {
            layer.msg('缓存清理完成', {time: 1000}, function () {
                location.reload();
            });
        });
    }
</script>
<script>
// 添加全局变量
window.EDITOR_MODE = false;  // 标记是否在编辑器模式

// AJAX 拦截器 - 自动还原代理 URL 为原始 URL
(function() {
    // 代理 URL 的匹配模式
    var proxyUrlPattern = /[?&]url=([^&]+)/;
    var proxyBaseUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>";

    // URL 还原函数
    function restoreOriginalUrl(url) {
        try {
            // 检查是否是代理 URL
            if (typeof url === 'string' && url.indexOf(proxyBaseUrl) !== -1) {
                var match = url.match(proxyUrlPattern);
                if (match && match[1]) {
                    // 解码并返回原始 URL
                    return decodeURIComponent(match[1]);
                }
            }
            return url;
        } catch (e) {
            // 出错时返回原始值，确保不影响正常功能
            console.warn('URL restore failed:', e);
            return url;
        }
    }

    // 增强版 URL 还原函数 - 支持从 DOM 元素的 data 属性获取原始 URL
    window.getOriginalSrc = function(element) {
        try {
            if (element && element.getAttribute) {
                // 优先从 data-original-src 获取
                var originalSrc = element.getAttribute('data-original-src');
                if (originalSrc) {
                    return originalSrc;
                }
                // 其次尝试从当前 src 还原
                var currentSrc = element.src || element.getAttribute('src');
                return restoreOriginalUrl(currentSrc);
            }
            return element;
        } catch (e) {
            console.warn('getOriginalSrc failed:', e);
            return element;
        }
    };

    // 递归处理数据对象，还原其中的代理 URL
    function restoreDataUrls(data) {
        try {
            if (typeof data === 'string') {
                return restoreOriginalUrl(data);
            } else if (Array.isArray(data)) {
                return data.map(restoreDataUrls);
            } else if (data && typeof data === 'object') {
                var restored = {};
                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        restored[key] = restoreDataUrls(data[key]);
                    }
                }
                return restored;
            }
            return data;
        } catch (e) {
            // 出错时返回原始数据
            console.warn('Data restore failed:', e);
            return data;
        }
    }

    // 保存原始的 jQuery AJAX 方法
    var originalAjax = $.ajax;
    var originalPost = $.post;
    var originalGet = $.get;

    // 重写 $.ajax
    $.ajax = function(options) {
        try {
            if (options && options.data) {
                options.data = restoreDataUrls(options.data);
            }
        } catch (e) {
            console.warn('AJAX data restore failed:', e);
        }
        return originalAjax.apply(this, arguments);
    };

    // 重写 $.post
    $.post = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('POST data restore failed:', e);
        }
        return originalPost.call(this, url, data, success, dataType);
    };

    // 重写 $.get
    $.get = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('GET data restore failed:', e);
        }
        return originalGet.call(this, url, data, success, dataType);
    };
})();

// 修改检测函数
function checkResourceAccess() {
    var isHttps = window.location.protocol === 'https:';
    
    if (isHttps) {
        // 处理图片
        document.querySelectorAll('img[src^="http://"]').forEach(function(img) {
            // 如果是编辑器模式，所有图片都使用代理URL显示
            if(window.EDITOR_MODE) {
                var originalSrc = img.getAttribute('data-src') || img.src;
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
                return;
            }
            
            // 非编辑器模式下排除富文本编辑器内的图片
            if(img.closest('.w-e-text') || img.closest('.w-e-text-container')) {
                return;
            }
            
            var originalSrc = img.getAttribute('data-src') || img.src;
            var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);

            var tmpImg = new Image();
            tmpImg.onload = function() {
                // 原始图片可访问，不做处理
            }
            tmpImg.onerror = function() {
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = proxyUrl;
            }
            tmpImg.src = originalSrc;
        });
        
        // 处理视频
        document.querySelectorAll('video').forEach(function(video) {
            // 处理video的src属性
            if (video.src && video.src.startsWith('http://')) {
                handleResource(video, 'src');
            }
            
            // 处理source标签
            video.querySelectorAll('source[src^="http://"]').forEach(function(source) {
                handleResource(source, 'src');
            });
        });
    }
}

// 统一的资源处理函数
function handleResource(element, srcAttr) {
    var originalSrc = element[srcAttr];
    var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
    
    // 创建临时对象测试资源是否可访问
    var tmpObj = new Image(); // 用Image对象测试连接可用性
    tmpObj.onload = function() {
        // 原始资源可访问，不做处理
    }
    tmpObj.onerror = function() {
        // 原始资源不可访问，切换到代理
        // 存储原始 URL 到 data 属性
        element.setAttribute('data-original-src', originalSrc);
        element[srcAttr] = proxyUrl;

        // 同时更新父级a标签的href
        var parent = element.parentElement;
        if (parent && parent.tagName === 'A') {
            parent.setAttribute('data-original-href', parent.href);
            parent.href = proxyUrl;
        }
    }
    tmpObj.src = originalSrc;
}

// 页面加载完成后执行检查
window.addEventListener('load', checkResourceAccess);

// 动态加载的内容处理
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            checkResourceAccess();
        }
    });
});

// 监听DOM变化
observer.observe(document.body, {
    childList: true,
    subtree: true
});
</script>

<script>
    !function () {
        $('.euModalOpen').click(function () {
            var euid = $(this).attr('data-euid');
            $('#virtual-user').val(euid);
        });
        $('#euModalClose').click(function () {
            $('#virtual-user').val('0');
        });
        
        // 为最后几行的下拉菜单添加向上显示功能
        $('.am-table tr:nth-last-child(-n+3) .am-dropdown').each(function() {
            $(this).on('open.dropdown.amui', function() {
                $(this).find('.am-dropdown-content').css({
                    'bottom': '100%',
                    'top': 'auto',
                    'margin-bottom': '5px',
                    'margin-top': '0'
                });
            });
        });
    }();

    var saloof = function () {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "<?php echo url('user/rutheoretic'); ?>");
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    var uploof =function (uid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "<?php echo url('user/editMaterial'); ?>&uid=" + uid);
        dynamicUrl.attr('target', '_blank');
        dynamicUrl.get(0).click();
    }

    var urelease = function (ecosa, usid) {
        switch (ecosa) {
            case '0':
                location.href = "<?php echo url('user/reticraphic'); ?>&usid=" + usid;
                break;
            case '3':
                location.href = "<?php echo url('user/reticrpaper'); ?>&usid=" + usid;
                break;
            case '4':
                location.href = "<?php echo url('resolve/release_mini_secrets'); ?>&fid=" + usid;
                break;
            case '5':
                location.href = "<?php echo url('resolve/reply_mini_secrets'); ?>&fid=" + usid;
                break;
        }
    }

    var extolled = function (obj) {
        obj.value = $.trim(obj.value);
        if (obj.value != '') {
            $.getJSON("<?php echo url('compass/getopenid'); ?>&virtual=1", {"openid": obj.value}, function (data) {
                if (data.name != '') {
                    $('#sehred').css('color', 'blue');
                    $('#sehred').text(data.name);
                } else {
                    $('#sehred').css('color', 'red');
                    $('#sehred').text('\u53d7\u8d60\u7528\u6237\u0020\u006f\u0070\u0065\u006e\u0069\u0064\u0020\u586b\u5199\u9519\u8bef');
                }
            });
        }
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var sendGifts = function () {
        var sehredInfo = $.trim($('#sehred').text());
        if (sehredInfo == '\u53d7\u8d60\u7528\u6237\u0020\u006f\u0070\u0065\u006e\u0069\u0064\u0020\u586b\u5199\u9519\u8bef') {
            layer.msg('\u53d7\u8d60\u7528\u6237\u0020\u006f\u0070\u0065\u006e\u0069\u0064\u0020\u586b\u5199\u9519\u8bef');
            return;
        }
        layer.confirm('您确定要赠送礼物吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            var virtualUser = $.trim($('#virtual-user').val());
            var userOpenid = $.trim($('#user-openid').val());
            var tributeNumber = $.trim($('#tribute-number').val());
            var tributeQuantity = $.trim($('#tribute-quantity').val());
            $.ajaxSettings.async = false;
            $.post("<?php echo url('user/virtualSendGifts'); ?>", {
                'virtualUser': virtualUser,
                'userOpenid': userOpenid,
                'tributeNumber': tributeNumber,
                'tributeQuantity': tributeQuantity
            }, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    function fuzzy() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "<?php echo url('user/theoretic'); ?>&hazy_name=" + fz_name + "&page=<?php echo $page; ?>";
        } else {
            location.href = "<?php echo url('user/theoretic'); ?>&page=<?php echo $page; ?>";
        }
    }
</script>

</body>
</html>