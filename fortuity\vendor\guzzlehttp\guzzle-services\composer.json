{"name": "guzzlehttp/guzzle-services", "description": "Provides an implementation of the Guzzle Command library that uses Guzzle service descriptions to describe web services, serialize requests, and parse responses into easy to use model structures.", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/konafets"}], "require": {"php": ">=5.5", "guzzlehttp/guzzle": "^6.2", "guzzlehttp/command": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "autoload": {"psr-4": {"GuzzleHttp\\Command\\Guzzle\\": "src/"}}, "autoload-dev": {"psr-4": {"GuzzleHttp\\Tests\\Command\\Guzzle\\": "tests/"}}, "suggest": {"gimler/guzzle-description-loader": "^0.0.4"}, "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}