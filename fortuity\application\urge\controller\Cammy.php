<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\common\Assembly;
use app\common\Playful;
use app\common\Remotely;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

#卡密
class Cammy extends Base
{
    /*
     * 商品卡密
     */
    public function shopAnomaly()
    {
        $egon = request()->get('egon', 0);
        $sid = intval(request()->get('sid', 0));
        $isSell = request()->get('isSell', 0);
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $where = array();
        if ($sid !== 0) {
            $where['shop_id'] = $sid;
        }
        switch ($egon) {
            case 1:
                $where['financial_type'] = 0;
                break;
            case 2:
                $where['financial_type'] = 1;
                break;
            case 3:
                $where['financial_type'] = 2;
                break;
            case 4:
                $where['financial_type'] = 3;
                break;
        }
        switch ($isSell) {
            case 1:
                $where['is_sell'] = 0;
                break;
            case 2:
                $where['is_sell'] = 1;
                break;
        }
        $list = Db::name('mucilage')
            ->where('card_code', 'like', "%{$hazy_name}%")
            ->where($where)
            ->where('card_type', 1)
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate(15, false, ['query' => ['s' => $this->defaultQuery(), 'egon' => $egon, 'sid' => $sid, 'isSell' => $isSell, 'hazy_name' => $hazy_name]])
            ->each(function ($item) {
                $item['product_name'] = Db::name('shop')->where('id', $item['shop_id'])->where('much_id', $this->much_id)->value('product_name');
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('egon', $egon);
        $this->assign('sid', $sid);
        $this->assign('isSell', $isSell);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 新增商品卡密
     */
    public function newShopAnomaly()
    {
        if (request()->isPost() && request()->isAjax()) {
            $setData = input('post.setData/a', array());
            $cardCodeList = explode(PHP_EOL, $setData['cardCode']);
            $data = array();
            for ($i = 0; $i < count($cardCodeList); $i++) {
                if (trim($cardCodeList[$i]) !== '') {
                    $data[] = [
                        'card_code' => trim($cardCodeList[$i]),
                        'card_type' => 1,
                        'financial_type' => 0,
                        'face_value' => floatval($setData['faceValue']),
                        'is_sell' => 0,
                        'is_use' => 0,
                        'status' => intval($setData['status']),
                        'shop_id' => intval($setData['sid']),
                        'create_time' => time(),
                        'is_del' => 0,
                        'much_id' => $this->much_id
                    ];
                }
            }
            Db::startTrans();
            try {
                Db::name('mucilage')->insertAll($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '添加成功']);
        } else {
            return $this->fetch();
        }
    }


    /*
     * 货币卡密
     */
    public function bankAnomaly()
    {
        $egon = request()->get('egon', 0);
        $isUse = request()->get('isUse', 0);
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $where = array();
        switch ($egon) {
            case 1:
                $where['financial_type'] = 1;
                break;
            case 2:
                $where['financial_type'] = 2;
                break;
            case 3:
                $where['financial_type'] = 3;
                break;
        }
        switch ($isUse) {
            case 1:
                $where['is_use'] = 0;
                break;
            case 2:
                $where['is_use'] = 1;
                break;
        }
        $list = Db::name('mucilage')
            ->where('card_code', 'like', "%{$hazy_name}%")
            ->where($where)
            ->where('card_type', 0)
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate(15, false, ['query' => ['s' => $this->defaultQuery(), 'egon' => $egon, 'isUse' => $isUse, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('egon', $egon);
        $this->assign('isUse', $isUse);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
        if (!Remotely::isUnLockProperty(base64_decode('5Y2h5a+G5YiX6KGo'))) {
            abort(404);
        }
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, array(), 'view'));
        }
        // ============================== 授权检测 End ==============================
    }


    /*
     * 生成卡密
     */
    public function newAnomaly()
    {
        if (request()->isPost() && request()->isAjax()) {
            $financeType = intval(input('post.financeType', 0));
            if ($financeType === 2) {
                $faceValue = intval(input('post.faceValue', 0));
            } else {
                $faceValue = floatval(input('post.faceValue', 0.00));
            }
            $cardNum = intval(input('post.cardNum', 0));
            $status = intval(input('post.status', 0));
            $data = array();
            for ($i = 0; $i < $cardNum; $i++) {
                $data[] = [
                    'card_code' => strtoupper(Assembly::uuid()),
                    'card_type' => 0,
                    'financial_type' => $financeType,
                    'face_value' => $faceValue,
                    'is_sell' => 0,
                    'is_use' => 0,
                    'status' => $status,
                    'shop_id' => 0,
                    'create_time' => time(),
                    'is_del' => 0,
                    'much_id' => $this->much_id
                ];
            }
            if ($cardNum > 0) {
                Db::startTrans();
                try {
                    Db::name('mucilage')->insertAll($data);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 绑定商品
     */
    public function bindShop()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = input('post.fid/a', array());
            $sid = intval(input('post.sid', 0));
            Db::startTrans();
            try {
                Db::name('mucilage')->whereIn('id', $fid)->update(['card_type' => 1, 'shop_id' => $sid]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '绑定商品成功']);
        } else {
            $list = Db::name('shop')
                ->where('auto_delivery', 1)
                ->where('trash', 0)
                ->where('much_id', $this->much_id)
                ->order(['scores' => 'asc'])
                ->field('id,product_name,product_img')
                ->paginate(5, false, ['query' => ['s' => $this->defaultQuery()]]);
            $this->assign('list', $list);
            return $this->fetch();
        }
    }

    /*
     * 更改卡密状态
     */
    public function updateAnomalyStatus()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            $status = intval(input('post.status', 0));
            Db::startTrans();
            try {
                Db::name('mucilage')->where('id', $fid)->where('much_id', $this->much_id)->update(['status' => $status]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 删除卡密
     */
    public function delAnomaly()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = input('post.fid/a', array());
            Db::startTrans();
            try {
                Db::name('mucilage')->whereIn('id', $fid)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 兑换记录
     */
    public function exchangeAnomaly()
    {
        $egon = request()->get('egon', 0);
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $where = array();
        switch ($egon) {
            case 1:
                $where['mu.financial_type'] = 1;
                break;
            case 2:
                $where['mu.financial_type'] = 2;
                break;
            case 3:
                $where['mu.financial_type'] = 3;
                break;
        }
        $list = Db::name('mucilage_use_annaly')
            ->alias('mua')
            ->join('mucilage mu', 'mua.mu_id = mu.id', 'left')
            ->where('mu.card_code', 'like', "%{$hazy_name}%")
            ->where($where)
            ->where('mua.much_id&mu.much_id', $this->much_id)
            ->order(['mua.id' => 'desc'])
            ->field('mua.*,mu.card_code,mu.financial_type,mu.face_value')
            ->paginate(15, false, ['query' => ['s' => $this->defaultQuery(), 'egon' => $egon, 'hazy_name' => $hazy_name]])
            ->each(function ($item) {
                $item['user'] = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('egon', $egon);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }
}