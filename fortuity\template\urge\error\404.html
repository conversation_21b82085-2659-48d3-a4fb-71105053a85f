<!DOCTYPE html>
<html>
<head><title>404 Not Found</title></head>
<body>
<center><h1>404 Not Found</h1></center>
<hr>
<center>{php}
$serverSoftware = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
if (stripos($serverSoftware, 'nginx') !== false) {
    echo 'nginx';
} elseif (stripos($serverSoftware, 'apache') !== false) {
    echo 'Apache';
} elseif (stripos($serverSoftware, 'iis') !== false) {
    echo 'Microsoft-IIS';
} elseif (stripos($serverSoftware, 'lighttpd') !== false) {
    echo 'lighttpd';
} else {
    $parts = explode('/', $serverSoftware);
    echo htmlspecialchars($parts[0] ?: 'Unknown');
}
{/php}</center>
</body>
</html>
<!-- a padding to disable MSIE and Chrome friendly error page -->
<!-- a padding to disable MSIE and Chrome friendly error page -->
<!-- a padding to disable MSIE and Chrome friendly error page -->
<!-- a padding to disable MSIE and Chrome friendly error page -->
<!-- a padding to disable MSIE and Chrome friendly error page -->
<!-- a padding to disable MSIE and Chrome friendly error page -->
