<?php
/**
 * Created by PhpStorm.
 * User: 王创世
 * Date: 2019-05-23
 * Time: 14:05
 */

namespace app\api\controller;


use app\api\service\Util;
use think\Db;

class Circle extends Base
{
    /**
     * 获取该圈子置顶帖子
     */
    public function get_placement_top()
    {
        $data = input('param.');
        $where = [];
        if ($this->version == 1) {
            $where['p.study_type'] = ['in', ['0', '1']];
        }
        $check = Db::name('paper')->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->where('p.tory_id', $data['tory_id'])
            ->where('p.much_id', $data['much_id'])
            ->where('p.study_status', 1)
            ->where('p.whether_delete', 0)
            ->where('p.topping_time<>0')
            ->order('p.topping_time desc')
            ->limit(5)
            ->where($where)
            ->field('p.*,u.gender,u.user_nick_name,u.user_head_sculpture')
            ->select();
        $count_len = count($check);
        $len_style = ['height' => 60, 'bottom' => 10, 'pos' => 0];
        if ($count_len == 1) {
            $len_style['height'] = 75;
            $len_style['bottom'] = 30;
            $len_style['pos'] = 17;
        }
        if ($count_len == 2) {
            $len_style['height'] = 100;
            $len_style['bottom'] = 50;
            $len_style['pos'] = 30;
        }
        if ($count_len == 3) {
            $len_style['height'] = 120;
            $len_style['bottom'] = 70;
            $len_style['pos'] = 30;
        }
        if ($count_len == 4) {
            $len_style['height'] = 140;
            $len_style['bottom'] = 95;
            $len_style['pos'] = 37;
        }
        if ($count_len == 5) {
            $len_style['height'] = 160;
            $len_style['bottom'] = 110;
            $len_style['pos'] = 42;
        }
        foreach ($check as $k => $v) {
            $util = new Util();
            $check[$k]['image_part'] = json_decode($v['image_part'], true);
            $check[$k]['study_title_color'] = emoji_decode($v['study_title_color']);
            $check[$k]['study_content'] = emoji_decode($v['study_content']);

            $check[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            $check[$k]['study_title'] = emoji_decode($v['study_title']);
            $check[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
//            $check[$k]['study_content'] = emoji_decode($v['study_content']);
            $ling = count(json_decode($v['image_part'], true));
            $check[$k]['study_heat'] = formatNumber($v['study_heat']);
            $check[$k]['study_laud'] = formatNumber($v['study_laud']);
            $check[$k]['study_repount'] = formatNumber($v['study_repount']);
            $check[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
            if ($ling == 1) {
                $check[$k]['image_length'] = '97.5';
            } else if ($ling == 2) {
                $check[$k]['image_length'] = '48';
            } else {
                $check[$k]['image_length'] = '31.5';
            }
            $check[$k]['adapter_time'] = formatTime($v['adapter_time']);

            $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
            $check[$k]['is_info_zan'] = $sc == 0 ? false : true;
            $count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])->count();
            $check[$k]['info_zan_count'] = formatNumber($count);
            //查询当前帖子是否是红包贴
            $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
            $check[$k]['red'] = $red;
        }
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $rs['info'] = $check;
        $rs['style'] = $len_style;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取全部圈子
     */
    public function get_left_needle()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $needle = Db::name('needle')
            ->where('status', 1)
            ->where('much_id', $data['much_id'])
            ->order('scores')
            ->select();
        $rs['info'] = $needle;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取右边圈子
     */
    public function get_right_needle()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $page = $data['page'];
        $util = new Util();
        if ($data['get_id'] > 0) {
            $needle = Db::name('territory')
                ->where('status', 1)
                ->where('is_del', 0)
                ->where('much_id', $data['much_id'])
                ->where('needle_id', $data['get_id'])
                ->page($page, '15')
                ->order('scores')
                ->select();
        }
        //我关注的
        if ($data['get_id'] == -1) {
            $needle = Db::name('user_trailing')->alias('t')
                ->join('territory e', 'e.id=t.tory_id')
                ->where('t.user_id', $data['uid'])
                ->where('e.status', 1)
                ->where('e.is_del', 0)
                ->order('t.ling_time desc')
                ->where('e.much_id', $data['much_id'])
                ->field('e.*')
                ->page($page, '15')
                ->select();

            //圈主
            foreach ($needle as $k => $v) {

                $needle[$k]['da_or_xiao'] = $util->check_qq($data['openid'], $v['id']);
            }


        }
        if ($data['get_id'] == -2) {
            $needle = Db::name('territory')
                ->where('status', 1)
                ->where('is_del', 0)
                ->order('concern desc')
                ->where('much_id', $data['much_id'])
                ->limit(50)
                ->select();
            foreach ($needle as $k => $v) {
                $needle_top = Db::name('needle')
                    ->where('id', $v['needle_id'])
                    ->where('status', 0)
                    ->where('much_id', $data['much_id'])
                    ->find();
                if (!empty($needle_top)) {
                    array_splice($needle, $k, 1);
                }
            }
        }
        if ($data['get_id'] == -3) {
            $needle = Db::name('territory')
                ->where('status', 1)
                ->where('is_del', 0)
                ->order('rising_time desc')
                ->where('much_id', $data['much_id'])
                ->limit(50)
                ->select();
        }
        if ($data['get_id'] == -4) {
            $where['realm_name'] = ['like', '%' . $data['search_name'] . '%'];
            $needle = Db::name('territory')
                ->where('status', 1)
                ->where('is_del', 0)
                ->order('rising_time desc')
                ->where($where)
                ->where('much_id', $data['much_id'])
                ->limit(50)
                ->select();
        }
        $rs['set_null'] = 0;
        if (isset($data['cat_add']) && empty($needle) && $data['page'] == 1) {

            $needle = Db::name('territory')
                ->where('status', 1)
                ->where('is_del', 0)
                ->order('concern desc')
                ->where('much_id', $data['much_id'])
                ->where('attention', 0)
                ->limit(10)
                ->select();
            $rs['set_null'] = 1;
        }
        $util = new Util();
        foreach ($needle as $k => $v) {
            $needle[$k]['concern'] = formatNumber($v['concern']);
            $needle[$k]['is_trailing'] = $util->get_user_trailing($data['uid'], $v['id']);
            $needle[$k]['is_paper_count'] = formatNumber($util->get_territory_papo_count($v['id']));
            $needle[$k]['realm_name'] = emoji_decode($v['realm_name']);
            $needle_top = Db::name('needle')
                ->where('id', $v['needle_id'])
                ->where('status', 0)
                ->where('much_id', $data['much_id'])
                ->find();
            if ($needle_top) {
                unset($needle[$k]);
            }
        }

        // PHP 8.4兼容性修复：确保 $needle 是数组
        $rs['info'] = is_array($needle) ? (is_array($needle) ? array_values($needle) : array()) : array();

        return $this->json_rewrite($rs);
    }

    /**
     * 加入圈子
     */
    public function set_user_trailing()
    {
        $rs = ['status' => 'success', 'msg' => '加入成功！'];
        $data = input('param.');
        $tory_info = Db::name('territory')->where('id', $data['tory_id'])->where('much_id', $data['much_id'])->find();
        $ckeck = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('tory_id', $data['tory_id'])->find();
        //查询是否已经申请过
        $tory_check = Db::name('territory_interest')->where('tory_id', $data['tory_id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        if ($data['is_trailing'] == 1) {
            $util = new Util();
            //判断是不是管理员或者圈主
            $territory_learned = $util->check_qq($data['openid'], $data['tory_id']);
            if ($territory_learned != 'no') {
                $rs = ['status' => 'error', 'msg' => '管理员无法取消关注！'];
                return $this->json_rewrite($rs);
            }
            Db::name('territory')->where('id', $data['tory_id'])->where('much_id', $data['much_id'])->setDec('concern');
            Db::name('user_trailing')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('tory_id', $data['tory_id'])->delete();
            if ($tory_check) {
                Db::name('territory_interest')->where('id', $tory_check['id'])->where('much_id', $data['much_id'])->delete();
            }
            $rs = ['status' => 'success', 'msg' => '退出成功！'];
            return $this->json_rewrite($rs);
        }

        if ($ckeck) {
            $rs = ['status' => 'error', 'msg' => '已经加入该' . $this->design['landgrave']];
            return $this->json_rewrite($rs);
        }

        if ($tory_info['attention'] == 1) {
            if ($data['trailing_type'] == 0) {
                if ($tory_check) {
                    if ($tory_check['status'] == 0) {
                        $rs = ['status' => 'success', 'msg' => '已经申请加入' . $this->design['landgrave']];
                        return $this->json_rewrite($rs);
                    }
                    if ($tory_check['status'] == 1) {
                        $rs = ['status' => 'error', 'msg' => '您已经加入该' . $this->design['landgrave']];
                        return $this->json_rewrite($rs);
                    }
                    if ($tory_check['status'] == 2) {
                        $jia = Db::name('territory_interest')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['reason' => emoji_encode(preg_replace('# #', '', $data['trailing_text'])), 'status' => 0]);
                        if ($jia) {
                            $rs = ['status' => 'success', 'msg' => '申请加入' . $this->design['landgrave'] . "成功！"];
                            return $this->json_rewrite($rs);
                        } else {
                            $rs = ['status' => 'error', 'msg' => '加入失败！'];
                            return $this->json_rewrite($rs);
                        }
                    }
                }
                //申请加入
                $interest = Db::name('territory_interest')->insert(['reason' => emoji_encode(preg_replace('# #', '', $data['trailing_text'])), 'user_id' => $this->user_info['id'], 'tory_id' => $data['tory_id'], 'sult_time' => time(), 'rest_time' => 0, 'much_id' => $data['much_id']]);
                if ($interest) {
                    $rs = ['status' => 'success', 'msg' => '申请加入' . $this->design['landgrave'] . "成功！"];

                    $rett = "用户：" . $this->user_info['user_nick_name'] . "申请关注了" . $this->design['landgrave'] . "[" . $tory_info['realm_name'] . "]";
                    Db::name('prompt_msg')->insert(['capriole' => 2, 'tyid' => $data['tory_id'], 'msg_time' => time(), 'type' => 0, 'retter' => $rett, 'status' => 0, 'much_id' => $data['much_id']]);
                    $notices = Db::name('prompt_msg')
                        ->where('status', 0)
                        ->where('type', 0)
                        ->where('much_id', $data['much_id'])
                        ->count('*');
                    cache('notices_' . $data['much_id'], $notices);

                    $util = new Util();
                    //获取当前圈的圈主
                    $quan = Db::name('territory_learned')->where('much_id', $data['much_id'])->where('tory_id', $data['tory_id'])->find();
                    if (!empty($quan) && !empty($quan['bulord'])) {
                        $bulord = json_decode($quan['bulord']);
                        // PHP 8.4兼容性修复：确保 $bulord 是数组
                        if (is_array($bulord)) {
                            foreach ($bulord as $k => $v) {
                                $user_info = Db::name('user')->where('user_wechat_open_id', $v)->field('id')->find();

                                //发送模版
                                $util->add_template(['much_id' => $data['much_id'],
                                    'at_id' => 'YL0005',
                                    'user_id' => $user_info['id'],
                                    'page' => 'yl_welore/pages/packageA/territory_interest/index?id=' . $data['tory_id'],
                                    'keyword1' => $tory_info['realm_name'],
                                    'keyword2' => emoji_decode($this->user_info['user_nick_name']),
                                    'keyword3' => '请求说明:' . preg_replace('# #', '', $data['trailing_text']),
                                    'keyword4' => date('Y年m月d日 H:i:s', time()),
                                ]);
                            }
                        } // 结束 is_array 检查
                    }


                    return $this->json_rewrite($rs);
                } else {
                    $rs = ['status' => 'error', 'msg' => '申请失败'];
                    return $this->json_rewrite($rs);
                }
            } else {
                //暗号加入
                if ($tory_info['atcipher'] == emoji_encode(preg_replace('# #', '', $data['trailing_text']))) {
                    $real = Db::name('user_trailing')->insert(['user_id' => $this->user_info['id'], 'tory_id' => $data['tory_id'], 'ling_time' => time(), 'much_id' => $data['much_id']]);
                    if ($real) {
                        Db::name('territory')->where('id', $data['tory_id'])->where('much_id', $data['much_id'])->setInc('concern');
                        $rs = ['status' => 'success', 'msg' => '加入成功！'];
                        if ($tory_check) {
                            Db::name('territory_interest')->where('id', $tory_check['id'])->where('much_id', $data['much_id'])->update(['status' => 1, 'rest_time' => time()]);
                        }
                        return $this->json_rewrite($rs);
                    } else {
                        $rs = ['status' => 'error', 'msg' => '加入失败！'];
                        return $this->json_rewrite($rs);
                    }
                } else {
                    $rs = ['status' => 'error', 'msg' => '暗号不正确，加入失败！'];
                    return $this->json_rewrite($rs);
                }
            }
        } else {
            $util = new Util();
            if ($tory_info['attention'] == 2) {
                $is_vip = $util->get_user_vip($this->user_info['id']);
                if ($is_vip == 0) {
                    return $this->json_rewrite(['status' => 'error', 'msg' => '只有会员才能加入！']);
                }
            }

            $real = Db::name('user_trailing')->insert(['user_id' => $this->user_info['id'], 'tory_id' => $data['tory_id'], 'ling_time' => time(), 'much_id' => $data['much_id']]);
            if ($real) {
                Db::name('territory')->where('id', $data['tory_id'])->where('much_id', $data['much_id'])->setInc('concern');
            } else {
                $rs = ['status' => 'error', 'msg' => '加入失败！'];
                return $this->json_rewrite($rs);
            }

        }
        return $this->json_rewrite($rs);
    }

    /**
     * 圈子详情
     */
    public function get_tory_info()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $info = Db::name('territory')->where('status', 1)->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        if (!$info) {
            $rs = ['status' => 'error', 'msg' => $this->design['landgrave'] . '不见了！'];
            return $this->json_rewrite($rs);
        }
        $util = new Util();
        $info['is_trailing'] = $util->get_user_trailing($this->user_info['id'], $data['id']);
        $info['is_paper_count'] = formatNumber($util->get_territory_papo_count($data['id']));
        $info['concern'] = formatNumber($info['concern']);
        $info['realm_name'] = emoji_decode($info['realm_name']);
        $rs['info'] = $info;
        return $this->json_rewrite($rs);
    }

    /**
     * 申请圈子
     */
    public function add_territory_petition()
    {
        $rs = ['status' => 'success', 'msg' => '申请成功,请等待审核！'];
        $data = input('param.');

        if (empty($data['realm_icon'])) {
            $rs = ['status' => 'error', 'msg' => '请上传一张头像！'];
            return $this->json_rewrite($rs);
        }
        if (empty($data['realm_name'])) {
            $rs = ['status' => 'error', 'msg' => '请填写名称！'];
            return $this->json_rewrite($rs);
        }
        if (empty($data['realm_synopsis'])) {
            $rs = ['status' => 'error', 'msg' => '请填写简介！'];
            return $this->json_rewrite($rs);
        }
        if (empty($data['solicit_origin'])) {
            $rs = ['status' => 'error', 'msg' => '请填写申请原因！'];
            return $this->json_rewrite($rs);
        }
        if ($this->user_info['tourist'] == 1) {
            $rs = ['status' => 'error', 'msg' => '游客不能申请' . $this->design['landgrave']];
            return $this->json_rewrite($rs);
        }
        //判断是否关闭创建圈子
        $tory_arbor = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($tory_arbor['tory_arbor'] == 0) {
            $rs = ['status' => 'error', 'msg' => '用户暂不能不能申请' . $this->design['landgrave']];
            return $this->json_rewrite($rs);
        }

        //判断是否重名
        $check_name = Db::name('territory_petition')->where('realm_name', emoji_encode($data['realm_name']))->where('much_id', $data['much_id'])->find();
        if ($check_name) {
            //Db::name('territory_petition')->where('id',$check_name['id'])->where('much_id',$data['much_id'])->setInc('solicit_rate');
            $rs = ['status' => 'error', 'msg' => $this->design['landgrave'] . '已经存在'];
            return $this->json_rewrite($rs);
        }


        $ins['realm_icon'] = $data['realm_icon'];
        $ins['realm_name'] = emoji_encode($data['realm_name']);
        $ins['needle_id'] = $data['needle_id'];
        $ins['realm_synopsis'] = emoji_encode($data['realm_synopsis']);
        $ins['solicit_origin'] = emoji_encode($data['solicit_origin']);
        $ins['user_id'] = $this->user_info['id'];
        $ins['is_gnaw_qulord'] = $data['is_gnaw_qulord'];
        $ins['attention'] = $data['attention'];
        $ins['found_lasting'] = time();
        $ins['much_id'] = $data['much_id'];
        $red = Db::name('territory_petition')->insert($ins);
        if ($red) {
            $mgs = '用户：' . $this->user_info['user_nick_name'] . '申请了一个' . $this->design['landgrave'] . '，名称：' . emoji_encode($data['realm_name']) . '，需要审核！';
            Db::name('prompt_msg')->insert(['capriole' => 3, 'tyid' => 0, 'msg_time' => time(), 'type' => 0, 'retter' => $mgs, 'status' => 0, 'much_id' => $data['much_id']]);
            $notices = Db::name('prompt_msg')
                ->where('status', 0)
                ->where('type', 0)
                ->where('much_id', $data['much_id'])
                ->count('*');
            cache('notices_' . $data['much_id'], $notices);
            return $this->json_rewrite($rs);
        } else {
            $rs = ['status' => 'error', 'msg' => '申请失败！'];
            return $this->json_rewrite($rs);
        }
    }

    /**
     * 获取申请加入圈子信息
     */
    public function get_user_territory_interest()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $list = Db::name('territory_interest')->alias('t')
            ->join('user u', 't.user_id=u.id')
            ->where('t.much_id', $data['much_id'])
            ->where('t.status', 0)
            ->where('t.tory_id', $data['id'])
            ->page($data['page'], '30')
            ->field('t.*,u.user_head_sculpture,u.user_nick_name,u.gender')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                $list[$k]['sult_time'] = date('Y-m-d H:i:s', $v['sult_time']);
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['reason'] = emoji_decode($v['reason']);
            }
        }
        $rs['info'] = $list;
        return $this->json_rewrite($rs);
    }

    /**
     * 同意加入圈子
     */
    public function add_territory_interest()
    {
        $data = input('param.');
        // 启动事务
        $util = new Util();
        //查询详情
        $territory_interest = Db::name('territory_interest')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        //圈子详情
        $tory_info = Db::name('territory')->where('id', $territory_interest['tory_id'])->where('much_id', $data['much_id'])->find();
        //先判断
        if ($territory_interest['status'] > 0) {
            $rs = ['status' => 'error', 'msg' => '该用户已经审核！'];
            return $this->json_rewrite($rs);
        }
        //检测权限
        $c = $util->get_user_check($data['openid'], $territory_interest['tory_id']);
        //return $c;
        if ($c == 0) {
            $rs = ['status' => 'error', 'msg' => '您没有权限！'];
            return $this->json_rewrite($rs);
        }
        Db::startTrans();
        try {
            //修改申请状态
            $territory_up = Db::name('territory_interest')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['rest_time' => time(), 'status' => $data['status']]);
            if (!$territory_up) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '操作失败！'];
                return $this->json_rewrite($rs);
            }
            //用户关注表增加数据
            if ($data['status'] == 1) {
                $territory_user = Db::name('user_trailing')->insert(['user_id' => $territory_interest['user_id'], 'tory_id' => $territory_interest['tory_id'], 'much_id' => $data['much_id'], 'ling_time' => time()]);
                if (!$territory_user) {
                    Db::rollback();
                    $rs = ['status' => 'error', 'msg' => '操作失败！'];
                    return $this->json_rewrite($rs);
                }
            }
            Db::name('territory')->where('id', $territory_interest['tory_id'])->where('much_id', $data['much_id'])->setInc('concern');
            Db::commit();

            //发送模版
            $util->add_template([
                'much_id' => $data['much_id'],
                'at_id' => 'YL0008',
                'user_id' => $territory_interest['user_id'],
                'page' => 'yl_welore/pages/packageA/circle_info/index?id=' . $territory_interest['tory_id'],
                'keyword1' => '申请加入:' . emoji_decode($tory_info['realm_name']),
                'keyword2' => $data['status'] == 1 ? '申请通过' : '申请拒绝',
                'keyword3' => date('Y年m月d日 H:i:s', time())
            ]);
            $rs = ['status' => 'success', 'msg' => '操作成功！'];
            return $this->json_rewrite($rs);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '操作失败！'];
            return $this->json_rewrite($rs);
        }

    }

    /**
     * 申请圈主或者管理员
     */
    public function add_territory_learned()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        if (empty($data['id'])) {
            $rs = ['status' => 'error', 'msg' => '申请失败，请稍候重试！'];
            return $this->json_rewrite($rs);
        }
        $user_info = Db::name('user')->where('much_id', $data['much_id'])->where('user_wechat_open_id', $data['openid'])->find();
        $tory_info = Db::name('territory')->where('much_id', $data['much_id'])->where('id', $data['id'])->find();
        if (empty($tory_info)) {
            $rs = ['status' => 'error', 'msg' => '申请失败，请稍候重试！'];
            return $this->json_rewrite($rs);
        }
        $shenqing_name = '圈主';
        if ($data['shenqing_type'] == 'da') {
            $shenqing_name = '圈主';
        } else {
            $shenqing_name = '管理员';
        }


        $learned = Db::name('territory_learned')->where('much_id', $data['much_id'])->where('tory_id', $data['id'])->find();
        if ($learned) {


            if ($data['shenqing_type'] == 'da') {

                if (count(json_decode($learned['bulord'])) == 3) {
                    $rs = ['status' => 'error', 'msg' => '圈主人数已达上限！'];
                    return $this->json_rewrite($rs);
                }
                $json_to_arr = json_decode($learned['snvite_bulord'], true);
                // PHP 8.4兼容性修复：确保 $json_to_arr 是数组
                if (is_array($json_to_arr) && !empty($json_to_arr)) {
                    foreach ($json_to_arr as $k => $v) {
                        if ($v['openid'] == $data['openid']) {
                            $rs = ['status' => 'error', 'msg' => '您已经申请圈主，请耐心等待！'];
                            return $this->json_rewrite($rs);
                        }
                    }
                    array_push($json_to_arr, ['openid' => $data['openid'], 'upshot' => emoji_encode($data['upshot']), 'time' => time()]);
                } else {
                    $json_to_arr[] = ['openid' => $data['openid'], 'upshot' => emoji_encode($data['upshot']), 'time' => time()];
                }

                $update = Db::name('territory_learned')->where('much_id', $data['much_id'])->where('tory_id', $data['id'])->update(['snvite_bulord' => json_encode($json_to_arr)]);
            } else {
                if (count(json_decode($learned['sulord'])) == 10) {
                    $rs = ['status' => 'error', 'msg' => '管理员人数已达上限！'];
                    return $this->json_rewrite($rs);
                }
                if (empty($learned['envite_sulord'])) {
                    $json_to_arr[] = ['openid' => $data['openid'], 'upshot' => emoji_encode($data['upshot']), 'time' => time()];
                } else {
                    $json_to_arr = json_decode($learned['envite_sulord'], true);
                    // PHP 8.4兼容性修复：确保 $json_to_arr 是数组
                    if (is_array($json_to_arr)) {
                        foreach ($json_to_arr as $k => $v) {
                            if ($v['openid'] == $data['openid']) {
                                $rs = ['status' => 'error', 'msg' => '您已经申请管理员，请耐心等待！'];
                                return $this->json_rewrite($rs);
                            }
                        }
                    } // 结束 is_array 检查
                    array_push($json_to_arr, ['openid' => $data['openid'], 'upshot' => emoji_encode($data['upshot']), 'time' => time()]);
                }

                $update = Db::name('territory_learned')->where('much_id', $data['much_id'])->where('tory_id', $data['id'])->update(['envite_sulord' => json_encode($json_to_arr)]);
            }
            if ($update) {
                $rs = ['status' => 'success', 'msg' => '申请成功！'];

                Db::name('prompt_msg')->insert(['capriole' => 1, 'tyid' => $data['id'], 'msg_time' => time(), 'type' => 0, 'retter' => '用户：' . $user_info['user_nick_name'] . '，申请 [' . $tory_info['realm_name'] . ']<' . $shenqing_name . '>，申请理由：' . $data['upshot'] . '。', 'status' => 0, 'much_id' => $data['much_id']]);
                $notices = Db::name('prompt_msg')
                    ->where('status', 0)
                    ->where('type', 0)
                    ->where('much_id', $data['much_id'])
                    ->count('*');
                cache('notices_' . $data['much_id'], $notices);
                return $this->json_rewrite($rs);
            } else {
                $rs = ['status' => 'error', 'msg' => '申请失败！'];
                return $this->json_rewrite($rs);
            }
        } else {
            $arrr[] = ['openid' => $data['openid'], 'upshot' => emoji_encode($data['upshot'])];
            $ins['tory_id'] = $data['id'];
            $ins['snvite_bulord'] = json_encode($arrr); //大
            $ins['envite_sulord'] = '';//小
            $ins['bulord'] = '';
            $ins['sulord'] = '';
            $ins['much_id'] = $data['much_id'];
            $add = Db::name('territory_learned')->insert($ins);
            if ($add) {
                $rs = ['status' => 'success', 'msg' => '申请成功！'];
                Db::name('prompt_msg')->insert(['capriole' => 1, 'tyid' => $data['id'], 'msg_time' => time(), 'type' => 0, 'retter' => '用户：' . $user_info['user_nick_name'] . '，申请 [' . $tory_info['realm_name'] . ']<' . $shenqing_name . '>，申请理由：' . $data['upshot'] . '。', 'status' => 0, 'much_id' => $data['much_id']]);
                $notices = Db::name('prompt_msg')
                    ->where('status', 0)
                    ->where('type', 0)
                    ->where('much_id', $data['much_id'])
                    ->count('*');
                cache('notices_' . $data['much_id'], $notices);
                return $this->json_rewrite($rs);
            } else {
                $rs = ['status' => 'error', 'msg' => '申请失败！'];
                return $this->json_rewrite($rs);
            }
        }

    }

    /**
     * 获取圈子资料，圈主，小圈住
     */
    public function get_qq_info()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');


        $info = Db::name('territory')->where('status', 1)->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        if (!$info) {
            $rs = ['status' => 'error', 'msg' => $this->design['landgrave'] . '不见了！'];
            return $this->json_rewrite($rs);
        }
        $util = new Util();
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        $info['admin'] = $cg;
        //查询圈主，管理
        $learned = Db::name('territory_learned')->where('much_id', $data['much_id'])->where('tory_id', $data['id'])->find();
        //查询是否关注了圈子
        $is_trailing = $util->get_user_trailing($this->user_info['id'], $data['id']);
        //查询当前用户是否是VIP
        $vip = $util->get_user_vip($this->user_info['id']);
        $da_xiao = $util->check_qq($data['openid'], $data['id']);
        $key = 0;//无权限
        if ($info['attention'] == 1) {
            if (!$is_trailing) {//没有关注
                $key = 1;//需要加入后浏览内容
            }

        }
        if ($info['attention'] == 2) {
            if ($vip == 0) {//不是vip
                $key = 2;//会员浏览
            }

        }
        if ($info['visit_level'] != 0) {
            //圈子有访问等级限制
            if ($this->user_info['level'] < $info['visit_level']) {
                $key = 3;//访问等级限制
            }
        }
        //判断是否是管理员
        if ($da_xiao == 'da' || $da_xiao == 'xiao') {
            $key = 0;//访问等级限制
        }
        //超管
        if ($cg == 1) {
            $key = 0;//访问等级限制
        }
        $info['msg_key'] = $key;
        $da_qq = [];
        $this_da_qq = 0;
        //$util=new Util();
        if (!empty($learned['bulord'])) {
            $arr = json_decode($learned['bulord']);
            if (is_array($arr)) {
                foreach ($arr as $k => $v) {
                    if ($v == $data['openid']) {
                        $this_da_qq = 1;
                    }
                    $da_qq[$k] = $util->get_openId_user($v, $data['much_id']);
                }
            }
        }
        $xiao_qq = [];
        $this_xiao_qq = 0;
        //$util=new Util();
        if (!empty($learned['sulord'])) {
            $arr = json_decode($learned['sulord']);
            if (is_array($arr)) {
                foreach ($arr as $k => $v) {
                    if ($v == $data['openid']) {
                        $this_xiao_qq = 1;
                    }
                    $xiao_qq[$k] = $util->get_openId_user($v, $data['much_id']);
                }
            }
        }
        $info['da_qq'] = $da_qq;
        $info['xiao_qq'] = $xiao_qq;
        $info['this_da_qq'] = $this_da_qq;
        $info['this_xiao_qq'] = $this_xiao_qq;
        $info['is_trailing'] = $is_trailing;
        $info['realm_synopsis'] = emoji_decode($info['realm_synopsis']);
        $info['atcipher'] = emoji_decode($info['atcipher']);
        $info['is_paper_count'] = formatNumber($util->get_territory_papo_count($data['id']));
        $info['concern'] = formatNumber($info['concern']);
        $info['realm_name'] = emoji_decode($info['realm_name']);
        $info['user_info'] = $this->user_info;
        $info['version'] = $this->version;
        $info['vip'] = $util->get_user_vip($this->user_info['id']);
        $info['level'] = $util->get_user_level($info['visit_level'], $data['much_id']);
        //查询今天发的帖子数量
        $check_today_terr = Db::name('paper')->where('tory_id', $data['id'])->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->whereTime('adapter_time', 'today')->count();
        $info['check_today_terr'] = $check_today_terr;
        $rs['info'] = $info;
        return $this->json_rewrite($rs);
    } // 结束 get_qq_info() 函数

    /**
     * 获取我管理的圈子
     */
    public function user_mastert()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        //圈主
        $da = Db::name('territory_learned')->alias('l')
            ->join('territory t', 'l.tory_id=t.id')
            ->where('l.bulord', 'like', '%' . $data['openid'] . '%')
            ->field('t.*')
            ->where('t.status', 1)
            ->where('t.is_del', 0)
            ->where('l.much_id', $data['much_id'])
            ->where('t.much_id', $data['much_id'])
            ->select();
        foreach ($da as $k => $v) {
            $da[$k]['is_type'] = 'da';
        }
        //管理员
        $xiao = Db::name('territory_learned')->alias('l')
            ->join('territory t', 'l.tory_id=t.id')
            ->where('l.sulord', 'like', '%' . $data['openid'] . '%')
            ->field('t.*')
            ->where('t.status', 1)
            ->where('t.is_del', 0)
            ->where('l.much_id', $data['much_id'])
            ->where('t.much_id', $data['much_id'])
            ->select();
        foreach ($xiao as $k => $v) {
            $xiao[$k]['is_type'] = 'xiao';
        }
        $rs['info'] = array_merge($da, $xiao);
        $util = new Util();
        $rs['user_info'] = $util->get_openId_user($data['openid'], $data['much_id']);
        return $this->json_rewrite($rs);
    }

    /**
     * 获取圈子申请管理员
     */
    public function get_envite_sulord()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('territory_learned')->where('tory_id', $data['id'])->where('much_id', $data['much_id'])->find();
        $json = json_decode($list['envite_sulord'], true);
        $user = array();
        $util = new Util();
        // PHP 8.4兼容性修复：确保 $json 是数组
        if (is_array($json)) {
            foreach ($json as $k => $v) {
                $user[$k]['upshot'] = emoji_decode($v['upshot']);
                $user[$k]['user_info'] = $util->get_openId_user($v['openid'], $data['much_id']);
            }
        }
        $rs['info'] = $user;
        return $this->json_rewrite($rs);
    }


    /**
     * 添加管理员
     */
    public function add_envite_sulord()
    {
        $rs = ['status' => 'success', 'msg' => '任命成功！'];
        $data = input('param.');
        $util = new Util();
        //检测是否有权限
        $c = 0;// 0 没有权限
        //查询是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg > 0) {
            $c = 1;

        }
        //查询是否是圈主
        $da = $util->check_qq($data['openid'], $data['id']);
        if ($da == 'da') {
            $c = 1;
        }

        if ($c == 0) {
            $rs = ['status' => 'error', 'msg' => '您没有权限！'];
            return $this->json_rewrite($rs);
        }

        $check = Db::name('territory_learned')->where('tory_id', $data['id'])->where('much_id', $data['much_id'])->find();
        $json = json_decode($check['sulord'], true);

        if ((is_array($json) ? count($json) : 0) >= 10) {
            $rs = ['status' => 'error', 'msg' => '管理员最多10位！'];
            return $this->json_rewrite($rs);
        }
        //删除申请列表的数据
        $json_s = json_decode($check['envite_sulord'], true);

        if (is_array($json_s)) {
            foreach ($json_s as $k => $v) {
                if ($v['openid'] == $data['user_openid']) {
                    unset($json_s[$k]);
                }
            }
        }

        //向管理员中添加
        if (empty($json)) {
            $json = [$data['user_openid']];
        } else {
            array_push($json, $data['user_openid']);
        }
        // 启动事务
        Db::startTrans();
        try {
            //更新数据
            $up_s = Db::name('territory_learned')->where('id', $check['id'])->update(['envite_sulord' => json_encode((is_array($json_s) ? array_values($json_s) : array()))]);
            //更新数据
            $up_d = Db::name('territory_learned')->where('id', $check['id'])->update(['sulord' => json_encode($json)]);
            if (!$up_s || !$up_d) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '任命失败！'];
                return $this->json_rewrite($rs);
            }
            // 提交事务
            Db::commit();
            return $this->json_rewrite($rs);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '任命失败！'];
            return $this->json_rewrite($rs);
        }


    }

    /**
     * 投诉管理员，圈主，圈子，用户
     */
    public function add_tc_submit()
    {
        $rs = ['status' => 'success', 'msg' => '投诉成功！'];
        $data = input('param.');
        if (empty(preg_replace('# #', '', $data['get_tc_text']))) {
            $rs = ['status' => 'error', 'msg' => '请填写投诉理由'];
            return $this->json_rewrite($rs);
        }
        $msg = '';
        if ($data['user_type'] == 0) {
            if (empty($data['id']) || empty($data['user_id'])) {
                $rs = ['status' => 'error', 'msg' => '投诉失败，请稍后重试！1'];
                return $this->json_rewrite($rs);
            }
            $msg = "(" . $this->design['landgrave'] . ")";
            //检查是否已经投诉了该圈子
            $check2 = Db::name('lament')
                ->where('ment_type', 0)
                ->where('proof_id', $this->user_info['id'])
                ->where('tory_id', $data['id'])
                ->where('much_id', $data['much_id'])
                ->find();
            if ($check2) {
                $rs = ['status' => 'error', 'msg' => '已经投诉过该' . $this->design['landgrave'] . '了！'];
                return $this->json_rewrite($rs);
            }
        }
        if ($data['user_type'] == 1 || $data['user_type'] == 2) {
            $msg = "(用户)";
            if (empty($data['user_id']) || empty($data['id'])) {
                $rs = ['status' => 'error', 'msg' => '投诉失败，请稍后重试！2'];
                return $this->json_rewrite($rs);
            }
            //检查是否已经投诉了该用户
            $check1 = Db::name('lament')
                ->where('proof_id', $this->user_info['id'])
                ->where('user_id', $data['user_id'])
                ->where('much_id', $data['much_id'])
                ->find();
            if ($check1) {
                $rs = ['status' => 'error', 'msg' => '已经投诉过该用户了！'];
                return $this->json_rewrite($rs);
            }
        }
        // 启动事务
        Db::startTrans();
        try {
            if ($data['user_type'] == 0) {
                $capriole = 8;
            }
            if ($data['user_type'] == 1) {
                $capriole = 9;
            }
            if ($data['user_type'] == 2) {
                $capriole = 10;
            }
            //msg插入信息
            $up_msg = Db::name('prompt_msg')->insertGetId(['capriole' => $capriole, 'tyid' => 0, 'msg_time' => time(), 'type' => 1, 'retter' => '新的投诉' . $msg, 'status' => 0, 'much_id' => $data['much_id']]);

            $dd['proof_id'] = $this->user_info['id'];
            $dd['mopt_id'] = $up_msg; //消息提示编号
            $dd['ment_type'] = $data['user_type'];
            $dd['tory_id'] = $data['id'];
            $dd['labor'] = $data['labor'];
            $dd['user_id'] = $data['user_id'];
            $dd['ment_caption'] = emoji_encode($data['get_tc_text']);
            $dd['status'] = 0;
            $dd['ment_time'] = time();
            $dd['much_id'] = $data['much_id'];
            $ins = Db::name('lament')->insert($dd);
            if (!$ins) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '投诉失败，请稍候重试！3'];
                return $this->json_rewrite($rs);
            }

            $vacants = Db::name('prompt_msg')
                ->where('status', 0)
                ->where('type', 1)
                ->where('much_id', $data['much_id'])
                ->count('*');
            cache('vacants_' . $data['much_id'], $vacants);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '投诉失败，请稍候重试！4'];
            return $this->json_rewrite($rs);
        }
        return $this->json_rewrite($rs);
    }


    /**
     * 打开或关闭暗号
     */
    public function open_atence()
    {
        $data = input('param.');
        $info = Db::name('territory')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        //==0想要开启暗号
        if ($data['atcipher_type'] == 0) {
            $up_data['atence'] = 1;
            if (empty($info['atcipher'])) {
                $up_data['atcipher'] = $this->get_ah_random();
            }
            $up = Db::name('territory')->where('id', $data['id'])->where('much_id', $data['much_id'])->update($up_data);
            if ($up) {
                $rs = ['status' => 'success', 'msg' => '开启成功！'];
                return $this->json_rewrite($rs);
            } else {
                $rs = ['status' => 'error', 'msg' => '开启失败！'];
                return $this->json_rewrite($rs);
            }
        } else {
            $up = Db::name('territory')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['atence' => 0]);
            if ($up) {
                $rs = ['status' => 'success', 'msg' => '关闭成功！'];
                return $this->json_rewrite($rs);
            } else {
                $rs = ['status' => 'error', 'msg' => '关闭失败！'];
                return $this->json_rewrite($rs);
            }
        }
    }

    /**
     * 修改暗号
     */
    public function update_atcipher()
    {
        $data = input('param.');
        $info = Db::name('territory')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        if ($info['atcipher'] == emoji_encode($data['this_atcipher'])) {
            $rs = ['status' => 'error', 'msg' => '没有做任何更改！'];
            return $this->json_rewrite($rs);
        }
        $up = Db::name('territory')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['atcipher' => emoji_encode(preg_replace('# #', '', $data['this_atcipher']))]);
        if ($up) {
            $rs = ['status' => 'success', 'msg' => '更改成功！'];
            return $this->json_rewrite($rs);
        } else {
            $rs = ['status' => 'error', 'msg' => '更改失败！'];
            return $this->json_rewrite($rs);
        }
    }

    /**
     * 获取随机字符
     */
    public function get_ah_random()
    {
        $len = 8;
        $chars = array(
            "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R",
            "S", "T", "U", "V", "W", "X", "Y", "Z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"
        );

        $charsLen = count($chars) - 1;
        shuffle($chars);                            //打乱数组顺序
        $str = '';
        for ($i = 0; $i < $len; $i++) {
            $str .= $chars[mt_rand(0, $charsLen)];    //随机取出一位
        }
        return $str;
    }

    /**
     * 获取随机字符
     */
    public function get_q_random()
    {
        $len = 8;
        $chars = array(
            "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R",
            "S", "T", "U", "V", "W", "X", "Y", "Z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"
        );

        $charsLen = count($chars) - 1;
        shuffle($chars);                            //打乱数组顺序
        $str = '';
        for ($i = 0; $i < $len; $i++) {
            $str .= $chars[mt_rand(0, $charsLen)];    //随机取出一位
        }
        return $this->json_rewrite(['code' => $str]);
    }

}