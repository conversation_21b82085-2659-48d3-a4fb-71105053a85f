<?php

namespace app\api\controller;


use app\api\service\Alternative;
use app\api\service\Util;
use think\Db;

class Order extends Base
{

    /**
     * 获取商品类型
     */
    public function get_shop_type()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $update = Db::name('shop_type')->where('status', 1)->where('much_id', $data['much_id'])->order('scores')->select();
        $rs['info'] = $update;
        $rs['user'] = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->field('conch')->find();
        return $this->json_rewrite($rs);
    }

    /**
     * 获取商品列表
     */
    public function get_shop_list()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('shop')
            ->where('status', 1)
            ->where('much_id', $data['much_id'])
            ->where('product_type', $data['type_id'])
            ->order('scores')
            ->page($data['page'], '15')
            ->select();
        if (!empty($list)) {
            foreach ($list as $k => $v) {
                //查询SKU
                $sku = Alternative::GetShopSku($v['id'], $data['much_id']);
                if ($sku) {
                    $list[$k]['product_price'] = bcadd($sku['list'][0]['price'], 0, 2);
                }
                $list[$k]['product_img'] = json_decode($v['product_img']);
            }
        }
        $rs['info'] = $list;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取商品详情(提交订单)
     */
    public function get_goods_sku()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('shop')
            ->where('status', 1)
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->find();
        if (!empty($list)) {
            $list['product_img'] = json_decode($list['product_img']);
            //获取是不是会员
            $util = new Util();
            $list['is_vip'] = $util->get_user_vip($this->user_info['id']);
            //$list['product_detail'] = preg_replace('/style=[\"|\'].*?[\"|\']/i', '', $list['product_detail']);
            //$list['product_detail'] = $list['product_detail'];
            $list['easy'] = '';
            if ($list['is_offline'] == 1) { //商家订单
                //查询商家信息
                $shop = Db::name('easy_info_shop_products')->where('much_id', $data['much_id'])->where('product_id', $list['id'])->find();
                if (!$shop) {
                    $list['easy'] = '';
                } else {
                    $easy_info = Db::name('easy_info_list')->where('much_id', $data['much_id'])->where('id', $shop['eil_id'])->find();
                    $easy_info['merchant_icon_carousel'] = json_decode($easy_info['merchant_icon_carousel']);
                    $list['easy'] = $easy_info;
                }
            }
            $plug = new Plugunit();
            if (!$plug->check_plug('7ccde9e6-dd67-915d-30a5-b1f63fbdd926', $data['much_id'])) {
                $list['easy'] = '';
            }
            //获取SKU
            $sku = Alternative::GetShopSku($list['id'], $data['much_id']);
            if ($sku) {
                $sku_info = $sku['list'][$data['sa_id']];
                $list['product_price'] = bcadd($sku_info['price'], 0, 2);
                $sku_info['sku_name'] = $sku['sa_name'];
                $list['sku_info'] = $sku_info;
            }
            $rs['info'] = $list;
        } else {
            $rs = ['status' => 'error', 'msg' => '商品已下架！'];
        }
        return $this->json_rewrite($rs);
    }

    /**
     * 获取商品详情
     */
    public function get_goods()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('shop')
            ->where('status', 1)
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->find();
        if (!empty($list)) {
            $list['product_img'] = json_decode($list['product_img']);
            //获取是不是会员
            $util = new Util();
            $list['is_vip'] = $util->get_user_vip($this->user_info['id']);
            //$list['product_detail'] = preg_replace('/style=[\"|\'].*?[\"|\']/i', '', $list['product_detail']);
            //$list['product_detail'] = $list['product_detail'];
            $list['easy'] = '';
            if ($list['is_offline'] == 1) { //商家订单
                //查询商家信息
                $shop = Db::name('easy_info_shop_products')->where('much_id', $data['much_id'])->where('product_id', $list['id'])->find();
                if (!$shop) {
                    $list['easy'] = '';
                } else {
                    $easy_info = Db::name('easy_info_list')->where('much_id', $data['much_id'])->where('id', $shop['eil_id'])->find();
                    $easy_info['merchant_icon_carousel'] = json_decode($easy_info['merchant_icon_carousel']);
                    $list['easy'] = $easy_info;
                }
            }
            $plug = new Plugunit();
            if (!$plug->check_plug('7ccde9e6-dd67-915d-30a5-b1f63fbdd926', $data['much_id'])) {
                $list['easy'] = '';
            }
            //获取SKU
            $sku = Alternative::GetShopSku($list['id'], $data['much_id']);
            if ($sku) {
                $list['product_price'] = bcadd($sku['list'][0]['price'], 0, 2);
                $count = 0;
                foreach ($sku['list'] as $k => $v) {
                    $count += $v['inventory_count'];
                }
                $list['product_inventory'] = $count;
            }
            $list['sku'] = $sku;
            $rs['info'] = $list;
        } else {
            $rs = ['status' => 'error', 'msg' => '商品已下架！'];
        }
        return $this->json_rewrite($rs);
    }

    /**
     * 兑换物品检测
     */
    public function exchange_goods()
    {
        $data = input('param.');
        return $this->json_rewrite($this->exchange_goods_gg($data));
    }

    /**
     * 兑换物品检测(公共方法)
     */
    public function exchange_goods_gg($data)
    {
        //获取商品详细
        $info = Db::name('shop')
            ->where('status', 1)
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->find();
        //检测sku
        $sku = Alternative::GetShopSku($info['id'], $data['much_id']);
        if ($sku) {
            $sku_info = $sku['list'][$data['sa_id']];
            $info['product_price'] = $sku_info['price'];
        }
        //获取用户详情
        $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        if ($user_info['tourist'] == 1) {
            return ['status' => 'error', 'code' => '1', 'msg' => '请您登录后兑换！'];
        }
        $util = new Util();
        $user_money = 0;
        $msg = '';
        //0贝壳
        if ($info['pay_type'] == 0) {
            $user_money = $user_info['conch'];
            $msg = $this->design['currency'];
        }
        //1贝壳
        if ($info['pay_type'] == 1) {
            $user_money = $user_info['fraction'];
            $msg = $this->design['confer'];
        }
        if ($info['pay_type'] == 2) {
            return ['status' => 'success', 'msg' => '成功！'];
        }
        if ($info['open_discount'] == 0) {
            //两个数相等时返回 0； num1 比 num2 大时返回 1； 其他则返回 -1。
            if (bccomp($info['product_price'], $user_money, 2) == 1) {
                return ['status' => 'error', 'code' => '2', 'msg' => $msg . '不足！'];
            }
        } else {
            $c_money = bcmul($info['noble_discount'], $info['product_price'], 2);
            if (bccomp($c_money, $user_money, 2) == 1) {
                return ['status' => 'error', 'code' => '2', 'msg' => $msg . '不足！'];
            }
        }

        //会员商品
        if ($info['noble_exclusive'] == 1) {
            $is_vip = $util->get_user_vip($this->user_info['id']);
            if ($is_vip == 0) {
                return ['status' => 'error', 'code' => '1', 'msg' => '您还不是会员！'];
            } else {
                //查询是否有会员折扣
                if ($info['open_discount'] == 1) {
                    $c_money = bcmul($info['noble_discount'], $info['product_price'], 2);
                } else {
                    $c_money = $info['product_price'];
                }
                if (bccomp($c_money, $user_money, 2) == 1) {
                    return ['status' => 'error', 'code' => '2', 'msg' => $msg . '不足！'];
                }
            }
        }

        //是否有限购
        if ($info['product_restrict'] > 0) {
            $check_user_order = Db::name('shop_order')
                ->where('user_id', $this->user_info['id'])
                ->where('product_id', $data['id'])
                ->where('much_id', $data['much_id'])
                ->count();
            if ($info['product_restrict'] <= $check_user_order) {
                return ['status' => 'error', 'code' => '1', 'msg' => '您已经兑换超限！'];
            }
        }
        return ['status' => 'success', 'msg' => '成功！'];
    }

    /**
     * 兑换物品
     */
    public function exchange_goods_do()
    {
        $data = input('param.');
//        //获取用户详情
        $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();

        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($authority['receipt_arbor'] == 1) {
            //判断收货地址
            if (empty($data['real_name'])) {
                $rs = ['status' => 'error', 'msg' => '收货人不能为空！'];
                return $this->json_rewrite($rs);
            }
            if (empty($data['phone'])) {
                $rs = ['status' => 'error', 'msg' => '联系电话不能为空！'];
                return $this->json_rewrite($rs);
            }
            if (empty($data['address'])) {
                $rs = ['status' => 'error', 'msg' => '收货地址不能为空！'];
                return $this->json_rewrite($rs);
            }
        }
        $util = new Util();
        $is_vip = $util->get_user_vip($this->user_info['id']);
        //获取商品详情
        $good_info = Db::name('shop')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->find();
        if ($good_info['status'] == 0 || $good_info['trash'] == 1) {
            $rs = ['status' => 'error', 'msg' => '商品已下架！'];
            return $this->json_rewrite($rs);
        }
        //检测sku
        $sku = Alternative::GetShopSku($good_info['id'], $data['much_id']);
        if ($sku) {
            $sku_info = $sku['list'][$data['sa_id']];
            $good_info['product_price'] = $sku_info['price'];
            $order['vested_attribute'] = json_encode([$sku['sa_name'], $sku_info['at_name']], JSON_UNESCAPED_UNICODE);
        } else {
            $order['vested_attribute'] = '';
        }
        //兑换物品检测
        $check = $this->exchange_goods_gg($data);
        if ($check['status'] == 'error') {
            return $this->json_rewrite($check);
        }
        //开启了会员折扣，并且当前用户是会员
        if ($good_info['open_discount'] == 1 && $is_vip == 1) {
            $c_money = bcmul($good_info['noble_discount'], $good_info['product_price'], 2);
        } else {
            $c_money = $good_info['product_price'];
        }
        //组成订单
        $order['order_number'] = time() . rand(99999, 1000000);
        $order['user_id'] = $this->user_info['id'];
        $order['product_id'] = $data['id'];
        $order['is_offline'] = $good_info['is_offline'];
        $order['pay_type'] = $good_info['pay_type'];
        $order['product_img'] = athumbnail($good_info['product_img']);
        $order['product_name'] = $good_info['product_name'];
        $order['product_price'] = $good_info['product_price'];
        $order['remark'] = emoji_encode($data['remark']);
        $order['buy_time'] = time();
        $order['buyer_name'] = $data['real_name'];
        $order['buyer_address'] = $data['address'];
        $order['buyer_phone'] = $data['phone'];
        $order['actual_price'] = $c_money;
        $order['is_noble'] = $is_vip;
        if ($good_info['open_discount'] == 1) {
            $order['product_discount'] = $good_info['noble_discount'];
        }
        if ($good_info['noble_rebate'] > 0 && $is_vip == 1) {
            $order['product_rebate'] = $good_info['noble_rebate'];
        }
        $order['pay_status'] = 1;
        if ($good_info['pay_type'] == 2) {
            $order['pay_status'] = 0;
        }
        $order['much_id'] = $data['much_id'];
        Db::startTrans();
        try {
            //查询是否是自动发货
            if ($good_info['auto_delivery'] == 1) {
                //查询卡密
                $card = Db::name('mucilage')
                    ->where('is_sell', 0)
                    ->where('is_use', 0)
                    ->where('status', 1)
                    ->where('is_del', 0)
                    ->where('much_id', $data['much_id'])
                    ->where('card_type', 1)
                    ->where('shop_id', $good_info['id'])
                    ->find();
                if (empty($card)) {
                    $rs = ['status' => 'error', 'msg' => '购买失败，库存不足！'];
                    return $this->json_rewrite($rs);
                }
                if ($good_info['pay_type'] != 2) {
                    //更新卡密状态
                    $up_card = Db::name('mucilage')->where('id', $card['id'])->update(['is_sell' => 1]);
                    if (!$up_card) {
                        Db::rollback();
                        return $this->json_rewrite(['status' => 'error', 'msg' => '购买失败，请稍候重试！']);
                    }
                    //更新订单状态
                    $order['shipment'] = $card['card_code'];
                    //发货状态
                    $order['status'] = 1;
                    //发货时间
                    $order['ship_time'] = time();
                }
            }
            //订单表增加数据
            $ins_order = Db::name('shop_order')->insertGetId($order);
            if (!$ins_order) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！'];
                return $this->json_rewrite($rs);
            }
            if ($good_info['pay_type'] == 2) {
                Db::commit();
                $pay_item['card_id'] = empty($card['id']) ? 0 : $card['id'];
                $pay_item['order_id'] = $ins_order;
                return $this->json_rewrite(['status' => 'wxpay', 'msg' => '请使用微信支付！', 'info' => $pay_item]);
            }
            //贝壳明细表增加数据
            $bk['user_id'] = $this->user_info['id'];
            $bk['category'] = 2;
            $bk['finance'] = -$c_money;
            if ($good_info['pay_type'] == 0) {
                $bk['poem_conch'] = $user_info['conch'];
                $bk['surplus_conch'] = bcsub($user_info['conch'], $c_money, 2);//落实贝壳
                $bk['poem_fraction'] = $user_info['fraction'];//初始积分
                $bk['surplus_fraction'] = $user_info['fraction'];//后积分
            }
            if ($good_info['pay_type'] == 1) {
                $bk['poem_conch'] = $user_info['conch'];//初始贝壳
                $bk['surplus_conch'] = $user_info['conch'];//落实贝壳
                $bk['poem_fraction'] = $user_info['fraction'];//初始积分
                $bk['surplus_fraction'] = bcsub($user_info['fraction'], $c_money, 2);//落实积分
            }
            $bk['ruins_time'] = time();
            $bk['solution'] = '兑换商品:' . $good_info['product_name'];
            $bk['evaluate'] = $good_info['pay_type'];
            $bk['much_id'] = $data['much_id'];
            $ins_bk = Db::name('user_amount')->insert($bk);
            if (!$ins_bk) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！2'];
                return $this->json_rewrite($rs);
            }
            //减少用户数据
            if ($good_info['pay_type'] == 0) {
                $u_up['conch'] = $bk['surplus_conch'];
            }
            if ($good_info['pay_type'] == 1) {
                $u_up['fraction'] = $bk['surplus_fraction'];
            }

            $user_update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update($u_up);
            if (!$user_update) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '兑换失败，请稍候重试！4']);
            }
            //物品库存减少
            if (!$sku) {
                $goods_up = Db::name('shop')
                    ->where('much_id', $data['much_id'])
                    ->where('id', $data['id'])
                    ->setDec('product_inventory');

            } else {
                foreach ($sku['list'] as $k => $v) {
                    if ($k == $data['sa_id']) {
                        $sku['list'][$k]['inventory_count'] = bcsub($v['inventory_count'], 1, 0);
                    }
                }
                $goods_up = Db::name('shop_vested')->where('id', $sku['id'])->update(['sa_list' => json_encode($sku['list'], JSON_UNESCAPED_UNICODE)]);
            }
            if (!$goods_up) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！2'];
                return $this->json_rewrite($rs);
            }
            //如果是商家订单
            if ($good_info['is_offline'] == 1) {
                $products = Db::name('easy_info_shop_products')->where('much_id', $data['much_id'])->where('product_id', $good_info['id'])->find();
                if ($products) {
                    $easy_info = Db::name('easy_info_list')->where('much_id', $data['much_id'])->where('id', $products['eil_id'])->find();
                    $easy_data['user_id'] = $this->user_info['id'];
                    $easy_data['product_id'] = $good_info['id'];
                    $easy_data['eil_id'] = $easy_info['id'];
                    $easy_data['so_id'] = $ins_order;
                    $easy_data['redemption_code'] = $util->create_uuid();
                    $easy_data['use_status'] = 0;
                    $easy_data['order_status'] = 1;
                    $easy_data['create_time'] = time();
                    $easy_data['much_id'] = $data['much_id'];
                    $easy_order = Db::name('easy_info_shop_order')->insertGetId($easy_data);
                    if (!$easy_order) {
                        Db::rollback();
                        $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！4'];
                        return $this->json_rewrite($rs);
                    }
                }
            }
            Db::commit();
            Db::name('prompt_msg')->insert(['capriole' => 11, 'tyid' => 0, 'msg_time' => time(), 'type' => 0, 'retter' => '用户：' . $user_info['user_nick_name'] . '购买了' . $good_info['product_name'], 'status' => 0, 'much_id' => $data['much_id']]);
            $notices = Db::name('prompt_msg')
                ->where('status', 0)
                ->where('type', 0)
                ->where('much_id', $data['much_id'])
                ->count('*');
            cache('notices_' . $data['much_id'], $notices);
            return $this->json_rewrite(['status' => 'success', 'msg' => '兑换成功！', 'auto_delivery' => $good_info['auto_delivery']]);
        } catch (\Exception $e) {
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！' . $e->getMessage()];
            return $this->json_rewrite($rs);
        }
    }

    /**
     * 取消订单
     */
    public function cancel_order_do()
    {
        $data = input('param.');
        //查询是否是我的订单
        $info = Db::name('shop_order')
            ->where('user_id', $this->user_info['id'])
            ->where('id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if (empty($info)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '取消失败，请稍候重试！']);
        }
        $res = Db::name('shop_order')->where('id', $data['id'])->update(['trash' => 1, 'status' => 3]);
        if (!$res) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '取消失败，请稍候重试！']);
        }
        return $this->json_rewrite(['status' => 'success', 'msg' => '取消成功！']);
    }

    /**
     * 获取订单列表
     */
    public function get_order_list()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        //查询已发货的订单，是否超过3天
        $order_my_list = Db::name('shop_order')->where('status', 1)->where('user_id', $this->user_info['id'])->select();
        foreach ($order_my_list as $k => $v) {
            //订单发货时间+3天
            $time = bcadd($v['ship_time'], 259200);
            //num1 比 num2 大时返回 1
            if (bccomp(time(), $time) == 1) {
                Db::name('shop_order')->where('id', $v['id'])->update(['status' => 4]);
            }
        }
        //tab0=未支付
        //tab1=待发货
        //tab2=已发货
        //tab3=申请退款
        //tab4=已退款
        //tab5=已完成
        $where = array();
        if ($data['order_type'] == 'tab0') {
            $where['pay_status'] = ['eq', '0'];
            $where['status'] = ['eq', '0'];
        }
        if ($data['order_type'] == 'tab1') {
            $where['status'] = ['eq', '0'];
            $where['pay_status'] = ['eq', '1'];
        }
        if ($data['order_type'] == 'tab2') {
            $where['status'] = ['eq', '1'];
        }
        if ($data['order_type'] == 'tab3') {
            $where['status'] = ['eq', '2'];
        }
        if ($data['order_type'] == 'tab4') {
            $where['status'] = ['eq', '3'];
            $where['pay_status'] = ['eq', '1'];
        }
        if ($data['order_type'] == 'tab5') {
            $where['status'] = ['eq', '4'];
        }
        $list = Db::name('shop_order')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->page($data['page'], '10')
            ->order('buy_time desc')
            ->where($where)
            ->select();
        if (!empty($list)) {
            foreach ($list as $k => $v) {
                $list[$k]['buy_time'] = date('Y-m-d H:i:s', $v['buy_time']);
            }
        }
        $rs['info'] = $list;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取我的订单详情
     */
    public function get_my_order()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $order_info = Db::name('shop_order')
            ->where('id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if ($order_info) {
            $order_info['remark'] = emoji_decode($order_info['remark']);
            $order_info['buy_time'] = date('Y-m-d H:i:s', $order_info['buy_time']);
            if (!empty($order_info['ship_time'])) {
                $order_info['ship_time'] = date('Y-m-d H:i:s', $order_info['ship_time']);
            }
        }
        $order_info['easy'] = '';
        //是否是商家订单
        if ($order_info['is_offline'] == 1) {
            $products = Db::name('easy_info_shop_order')->where('much_id', $data['much_id'])->where('so_id', $order_info['id'])->find();
            if ($products) {
                $easy_info = Db::name('easy_info_list')->where('much_id', $data['much_id'])->where('id', $products['eil_id'])->find();
                $order_info['easy'] = $easy_info;
            }
            //二维码参数
            $code = authcode(json_encode(['uid' => $this->user_info['id'], 'order' => $products['id']]), 'ENCODE', 'QrcodeDate');
            $order_info['code'] = $code;
            $order_info['card'] = $products['redemption_code'];
            $plug = new Plugunit();
            if (!$plug->check_plug('7ccde9e6-dd67-915d-30a5-b1f63fbdd926', $data['much_id'])) {
                $order_info['easy'] = '';
            }
        }
        $order_info['vested_attribute'] = json_decode($order_info['vested_attribute'], true);
        $rs['info'] = $order_info;
        return $this->json_rewrite($rs);
    }

    /**
     * 退款
     */
    public function order_refund()
    {
        $data = input('param.');
        //获取用户详情
        $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        //订单详情
        $order_info = Db::name('shop_order')
            ->where('id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if ($order_info['status'] == 3 || $order_info['status'] == 4) {
            $rs = ['status' => 'error', 'msg' => '当前订单已不能申请退款！'];
            return $this->json_rewrite($rs);
        }
        //赠送积分大于用户现在积分
//        if ($order_info['product_rebate'] > $user_info['fraction']) {
//            $rs = ['status' => 'error', 'msg' => '积分不足，退款失败！'];
//            return $this->json_rewrite($rs);
//        }
        Db::startTrans();
        try {
            $result = true;

            $o['status'] = 2;
            $o['reason_refund'] = emoji_encode($data['refund_text']);
            $o['refund_count'] = 1;

            $o_up = Db::name('shop_order')
                ->where('id', $data['id'])
                ->where('much_id', $data['much_id'])
                ->update($o);
            if (!$o_up) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '申请退货失败，请稍候重试'];
                return $this->json_rewrite($rs);
            }
            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '申请退货失败，请稍候重试'];
            return $this->json_rewrite($rs);
        }
        Db::name('prompt_msg')->insert(['capriole' => 11, 'tyid' => 0, 'msg_time' => time(), 'type' => 0, 'retter' => $user_info['user_nick_name'] . '发起了退款，订单号：' . $order_info['order_number'], 'status' => 0, 'much_id' => $data['much_id']]);
        $notices = Db::name('prompt_msg')
            ->where('status', 0)
            ->where('type', 0)
            ->where('much_id', $data['much_id'])
            ->count('*');
        cache('notices_' . $data['much_id'], $notices);
        if ($result == true) {
            $rs = ['status' => 'success', 'msg' => '申请成功！'];
            return $this->json_rewrite($rs);
        } else {
            $rs = ['status' => 'error', 'msg' => '申请退货失败，请稍候重试'];
            return $this->json_rewrite($rs);
        }
    }

    /**
     * 取消退款
     */
    public function refund_del_do()
    {
        $data = input('param.');
        //获取用户详情
        //$user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        //订单详情
        $order_info = Db::name('shop_order')
            ->where('id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        Db::startTrans();
        try {
            //判断是否已发货
            if (empty($order_info['ship_time'])) {
                $o['status'] = 0;
            } else {
                $o['status'] = 1;
            }
            $o_up = Db::name('shop_order')
                ->where('id', $data['id'])
                ->where('much_id', $data['much_id'])
                ->update($o);
            if (!$o_up) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '取消退货失败，请稍候重试'];
                return $this->json_rewrite($rs);
            }
            // 提交事务
            Db::commit();
            $rs = ['status' => 'success', 'msg' => '取消成功！'];
            return $this->json_rewrite($rs);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '取消退货失败，请稍候重试' . $e->getMessage()];
            return $this->json_rewrite($rs);
        }
    }

    /**
     * 确认收货
     */
    public function ok_mod_do()
    {
        $data = input('param.');
        $util = new Util();
        //获取用户详情
        $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        //订单详情
        $order_info = Db::name('shop_order')
            ->where('id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if ($order_info['status'] == 4) {
            $rs = ['status' => 'error', 'msg' => '当前订单已完成'];
            return $this->json_rewrite($rs);
        }
        // 启动事务
        Db::startTrans();
        try {
            //赠送积分不为0并且是会员
            if ($order_info['product_rebate'] != 0 && $order_info['is_noble'] == 1) {
                //积分表增加数据
                $jf['user_id'] = $this->user_info['id'];
                $jf['category'] = 3;
                $jf['finance'] = $order_info['product_rebate'];
                $jf['poem_fraction'] = $user_info['fraction'];
                $jf['surplus_fraction'] = $user_info['fraction'] + $order_info['product_rebate'];//落实积分
                $jf['ruins_time'] = time();
                $jf['solution'] = '确认收货赠送' . $this->design['confer'];
                $jf['evaluate'] = 1;
                $jf['much_id'] = $data['much_id'];
                $ins_jf = Db::name('user_amount')->insert($jf);
                if (!$ins_jf) {
                    Db::rollback();
                    $rs = ['status' => 'error', 'msg' => '确认收货失败，请稍候重试1'];
                    return $this->json_rewrite($rs);
                }
                //加用户积分
                $user_up = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['fraction' => $jf['surplus_fraction']]);
                if (!$user_up) {
                    Db::rollback();
                    $rs = ['status' => 'error', 'msg' => '确认收货失败，请稍候重试2'];
                    return $this->json_rewrite($rs);
                }
            }
            //修改订单表数据
            $o_up = Db::name('shop_order')
                ->where('id', $data['id'])
                ->where('much_id', $data['much_id'])
                ->update(['status' => 4]);
            if (!$o_up) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '确认收货失败，请稍候重试2'];
                return $this->json_rewrite($rs);
            }
            //用户提醒
            $smail = $util->add_user_smail($this->user_info['id'], '已完成收货，感谢您的惠顾，再见！', $data['much_id'], '2', '0');

            if (!$smail) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '确认收货失败，请稍候重试2'];
                return $this->json_rewrite($rs);
            }
            // 提交事务
            Db::commit();
            $rs = ['status' => 'success', 'msg' => '确认收货成功！'];
            return $this->json_rewrite($rs);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '确认收货失败，请稍候重试2'];
            return $this->json_rewrite($rs);
        }
    }

    public function get_user_take()
    {
        $data = input('param.');
        //查询店员权限
        $easy_info_shop_assistant = Db::name('easy_info_shop_assistant')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if (!$easy_info_shop_assistant || $easy_info_shop_assistant['status'] == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '您不是店员！']);
        }
        $order_list = Db::name('easy_info_shop_order')
            ->where('eil_id', $easy_info_shop_assistant['eil_id'])
            ->where('much_id', $data['much_id'])
            ->where('use_status', $data['type'])
            ->page($data['page'], 15)
            ->select();
        foreach ($order_list as $k => $v) {
            $order_list[$k]['create_time'] = date('Y年m月d日 H:i:s', $v['create_time']);
            //商家详情
            $easy = Db::name('easy_info_list')->where('id', $v['eil_id'])->where('much_id', $data['much_id'])->field('merchant_name,merchant_icon_carousel')->find();
            $easy['merchant_icon_carousel'] = json_decode($easy['merchant_icon_carousel'], true);
            $order_list[$k]['easy'] = $easy;
            //是否已经使用
            if ($v['use_status'] == 1) {
                $verify = Db::name('easy_info_shop_order_verify_log')->where('eiso_id', $v['id'])->where('much_id', $data['much_id'])->where('eil_id', $easy_info_shop_assistant['eil_id'])->value('verify_time');
                $verify = date('Y年m月d日 H:i:s', $verify);
                $order_list[$k]['verify'] = $verify;
            } else {
                $order_list[$k]['verify'] = 0;
            }
            //使用用户
            $user = Db::name('user')->where('id', $v['user_id'])->where('much_id', $data['much_id'])->field('user_head_sculpture,user_nick_name')->find();
            $user['user_nick_name'] = emoji_decode($user['user_nick_name']);
            $order_list[$k]['user_info'] = $user;
            //商品
            $goods = Db::name('shop_order')->where('id', $v['so_id'])->where('much_id', $data['much_id'])->field('product_name,product_price')->find();
            $order_list[$k]['goods'] = $goods;
        }
        return $this->json_rewrite($order_list);
    }
}