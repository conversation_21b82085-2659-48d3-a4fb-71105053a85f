<?php

namespace app\api\controller;


use app\api\service\Moderation;
use app\api\service\UserService;
use app\api\service\Util;
use app\common\Gyration;
use app\urge\controller\Upload;
use think\Db;

class Service extends Base
{
    /**
     * 禁言
     */
    public function do_banned_user()
    {
        $data = input('param.');
        $util = new Util();
        //判断是否是圈主或超管
        $check = $util->get_user_check($data['openid'], $data['tory_id']);
        if ($check == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '没有权限！']);
        }
        $chech = Db::name('user_banned')
            ->where('tory_id', $data['tory_id'])
            ->where('user_id', $data['user_id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if (!empty($chech) && $chech['refer_time'] > time()) {
            $rs = ['status' => 'error', 'msg' => '该用户已被禁言，解除时间：' . date('Y-m-d H:i:s', $chech['refer_time'])];
            return $this->json_rewrite($rs);
        }
        $d = array();
        if (empty($chech)) {
            $d['tory_id'] = $data['tory_id'];
            $d['refer_id'] = $this->user_info['id'];
            $d['refer_type'] = $data['refer_type'] == 'da' ? 1 : 2;
            $d['user_id'] = $data['user_id'];
            $d['beget'] = emoji_encode($data['beget']);
            $d['refer_time'] = time() + ($data['day'] * 86400);
            $d['much_id'] = $data['much_id'];

            $ins = Db::name('user_banned')->insert($d);
            if ($ins) {
                $rs = ['status' => 'success', 'msg' => '禁言成功'];
            } else {
                $rs = ['status' => 'error', 'msg' => '禁言失败，请稍候重试'];
            }
            return $this->json_rewrite($rs);
        }
        if (!empty($chech) && $chech['refer_time'] < time()) {
            $d['refer_id'] = $this->user_info['id'];
            $d['refer_type'] = $data['refer_type'] == 'da' ? 1 : 2;
            $d['beget'] = emoji_encode($data['beget']);
            $d['refer_time'] = time() + ($data['day'] * 86400);
            $d['much_id'] = $data['much_id'];

            $ins = Db::name('user_banned')->where('id', $chech['id'])->update($d);
            if ($ins) {
                $rs = ['status' => 'success', 'msg' => '禁言成功'];
            } else {
                $rs = ['status' => 'error', 'msg' => '禁言失败，请稍候重试'];
            }
            return $this->json_rewrite($rs);
        }

        $msg = '由于：' . emoji_encode($data['beget']) . "被管理员禁言，禁言解除时间" . date('Y-m-d H:i:s', $d['refer_time']) . "，如有疑问请到服务中心申诉";
        $util->add_user_smail($this->user_info['id'], $msg, $data['much_id'], '1', '0');
    }

    /**
     * 获取删除帖子/回复/打回
     */
    public function get_user_paper_del()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');

        $where = array();
        if ($data['del_type'] == 'tab1') {
            $where['p.whether_type'] = ['eq', 1];
        }
        if ($data['del_type'] == 'tab2') {
            $where['p.whether_type'] = ['eq', 2];
        }
        if ($data['del_type'] == 'tab3') {
            $where['p.whether_type'] = ['eq', 4];
        }
        if ($data['del_type'] == 'tab4') {
            $where['p.whether_type'] = ['eq', 3];
        }
        if ($data['del_type'] == 'tab5') {
            //获取打回帖子
            $reject_reason = Db::name('paper')->alias('p')
                ->join('territory t', 'p.tory_id=t.id')
                ->where('p.user_id', $this->user_info['id'])
                ->where('p.much_id', $data['much_id'])
                ->where('p.study_status', 2)
                ->field('p.*,t.realm_name')
                ->select();
            foreach ($reject_reason as $k => $v) {
                $reject_reason[$k]['is_reply'] = 3;
                $reject_reason[$k]['study_title'] = strip_tags(emoji_decode($v['study_title']));
                $reject_reason[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
                $reject_reason[$k]['reject_reason'] = emoji_decode($v['reject_reason']);
                $reject_reason[$k]['prove_time'] = date('Y-m-d H:i:s', $v['prove_time']);
            }
            $rs['info'] = $reject_reason;
            return $this->json_rewrite($rs);
        }
        //获取删除的帖子
        $paper = Db::name('paper')->alias('p')
            ->join('territory t', 'p.tory_id=t.id')
            ->where('p.user_id', $this->user_info['id'])
            ->where('p.much_id', $data['much_id'])
            ->where($where)
            ->field('p.*,t.realm_name')
            ->select();
        foreach ($paper as $k => $v) {
            $paper[$k]['is_reply'] = 0;
            $paper[$k]['study_title'] = strip_tags(emoji_decode($v['study_title']));
            $paper[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
            $paper[$k]['whether_reason'] = emoji_decode($v['whether_reason']);
            $paper[$k]['whetd_time'] = date('Y-m-d H:i:s', $v['whetd_time']);

            $paper[$k]['is_complaint'] = Db::name('paper_complaint')
                ->where('user_id', $this->user_info['id'])
                ->where('tale_type', 2)
                ->where('paper_id', $v['id'])
                ->where('tory_id', $v['tory_id'])
                ->where('acceptance_status', 0)
                ->count();
            $paper[$k]['is_complaint_list'] = Db::name('paper_complaint')
                ->where('user_id', $this->user_info['id'])
                ->where('tale_type', 2)
                ->where('paper_id', $v['id'])
                ->where('tory_id', $v['tory_id'])
                ->order('petition_time desc')
                ->select();
        }

        //获取删除的回复
        $paper_reply = Db::name('paper_reply')->alias('p')
            ->where('p.user_id', $this->user_info['id'])
            ->where('p.much_id', $data['much_id'])
            ->where($where)
            ->field('p.*')
            ->select();
        if (!empty($paper_reply)) {
            $util = new Util();
            foreach ($paper_reply as $k => $v) {
                $paper_reply[$k]['is_reply'] = 1;
                $paper_reply[$k]['study_title'] = strip_tags(emoji_decode($util->get_user_applaud($v['id'])['study_title']));
                $paper_reply[$k]['study_content'] = strip_tags(emoji_decode($util->get_user_applaud($v['id'])['study_content']));
                $paper_reply[$k]['tory_id'] = $util->get_user_applaud($v['id'])['tory_id'];
                $paper_reply[$k]['apter_time'] = date('Y-m-d H:i:s', $v['apter_time']);

                $paper_reply[$k]['is_complaint'] = Db::name('paper_complaint')
                    ->where('user_id', $this->user_info['id'])
                    ->where('tale_type', 3)
                    ->where('prely_id', $v['id'])
                    ->where('acceptance_status', 0)
                    ->count();
                $paper_reply[$k]['is_complaint_list'] = Db::name('paper_complaint')
                    ->where('user_id', $this->user_info['id'])
                    ->where('tale_type', 3)
                    ->where('prely_id', $v['id'])
                    ->order('petition_time desc')
                    ->select();


                $paper[] = $paper_reply[$k];
            }
        }
        $rs['info'] = $paper;
        return $this->json_rewrite($rs);
    }

    /**
     * 申诉帖子/回复
     */
    public function do_paper_mutter()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        if ($data['is_reply'] == 0) {//0帖子
            $check = Db::name('paper_complaint')
                ->where('tale_type', 2)
                ->where('paper_id', $data['id'])
                ->where('user_id', $this->user_info['id'])
                ->where('much_id', $data['much_id'])
                ->where('acceptance_status', 0)
                ->find();
            if ($check) {
                $rs = ['status' => 'error', 'msg' => '您已经申诉过，请耐心等待！'];
                return $this->json_rewrite($rs);
            }
            $ins['user_id'] = $this->user_info['id'];
            $ins['tale_type'] = 2;
            $ins['paper_id'] = $data['id'];
            $ins['tory_id'] = $data['tory_id'];
            $ins['tale_content'] = emoji_encode($data['tale_content']);
            $ins['acceptance_status'] = 0;
            $ins['is_strike'] = 1;
            $ins['petition_time'] = time();
            $ins['tale_instruct'] = '';
            $ins['much_id'] = $data['much_id'];
            $ins_data = Db::name('paper_complaint')->insert($ins);
            if ($ins_data) {
                $rett = "用户" . $this->user_info['user_nick_name'] . "申诉了帖子，请及时处理！";
                Db::name('prompt_msg')->insert(['capriole' => 7, 'tyid' => 0, 'msg_time' => time(), 'type' => 1, 'retter' => $rett, 'status' => 0, 'much_id' => $data['much_id']]);
                $notices = Db::name('prompt_msg')
                    ->where('status', 0)
                    ->where('type', 1)
                    ->where('much_id', $data['much_id'])
                    ->count('*');
                cache('vacants_' . $data['much_id'], $notices);
                $rs = ['status' => 'success', 'msg' => '申诉成功，请耐心等待！'];
                return $this->json_rewrite($rs);
            } else {
                $rs = ['status' => 'error', 'msg' => '申诉失败请稍候重试！'];
                return $this->json_rewrite($rs);
            }
        }
        if ($data['is_reply'] == 1) {
            $check = Db::name('paper_complaint')
                ->where('tale_type', 3)
                ->where('paper_id', $data['id'])
                ->where('user_id', $this->user_info['id'])
                ->where('much_id', $data['much_id'])
                ->where('acceptance_status', 0)
                ->find();
            if ($check) {
                $rs = ['status' => 'error', 'msg' => '您已经申诉过，请耐心等待！'];
                return $this->json_rewrite($rs);
            }
            $util = new Util();
            $ins['user_id'] = $this->user_info['id'];
            $ins['tale_type'] = 3;
            $ins['paper_id'] = '';
            $ins['prely_id'] = $data['id'];
            $ins['tory_id'] = $util->get_user_applaud($data['id'])['tory_id'];
            $ins['tale_content'] = emoji_encode($data['tale_content']);
            $ins['acceptance_status'] = 0;
            $ins['is_strike'] = 0;
            $ins['petition_time'] = time();
            $ins['tale_instruct'] = '';
            $ins['much_id'] = $data['much_id'];
            $ins_data = Db::name('paper_complaint')->insert($ins);
            if ($ins_data) {
                $rett = "用户" . $this->user_info['user_nick_name'] . "申诉了回复，请及时处理！";
                Db::name('prompt_msg')->insert(['capriole' => 7, 'tyid' => 0, 'msg_time' => time(), 'type' => 1, 'retter' => $rett, 'status' => 0, 'much_id' => $data['much_id']]);
                $notices = Db::name('prompt_msg')
                    ->where('status', 0)
                    ->where('type', 1)
                    ->where('much_id', $data['much_id'])
                    ->count('*');
                cache('vacants_' . $data['much_id'], $notices);
                $rs = ['status' => 'success', 'msg' => '申诉成功，请耐心等待！'];
                return $this->json_rewrite($rs);
            } else {
                $rs = ['status' => 'error', 'msg' => '申诉失败请稍候重试！'];
                return $this->json_rewrite($rs);
            }
        }
    }

    /**
     * 获取投诉记录
     */
    public function get_user_report()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $where = array();
        if ($data['is_type'] == 'tab1') {
            $where['c.tale_type'] = ['eq', 0];
        } else {
            $where['c.tale_type'] = ['eq', 1];
        }
        $list = Db::name('paper_complaint')->alias('c')
            ->join('territory t', 'c.tory_id=t.id')
            ->where('c.user_id', $this->user_info['id'])
            ->where('c.much_id', $data['much_id'])
            ->where($where)
            ->field('c.*,t.realm_name')
            ->select();
        $util = new Util();
        foreach ($list as $k => $v) {
            $list[$k]['tale_content'] = emoji_decode($v['tale_content']);
            $list[$k]['petition_time'] = date('Y-m-d H:i:s', $v['petition_time']);
            $list[$k]['realm_name'] = emoji_decode($v['realm_name']);
            if ($data['is_type'] == 'tab1') {
                $list[$k]['paper'] = $util->get_paper($v['paper_id']);
            } else {
                $list[$k]['paper'] = $util->get_paper($util->get_user_applaud($v['prely_id'])['id']);
                $list[$k]['paper_reply'] = $util->paper_reply($v['prely_id']);
            }

        }
        $rs['info'] = $list;
        return $this->json_rewrite($rs);
    }


    /**
     * 获取禁言列表(取消禁言用)
     */
    public function get_user_banned_qq()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('user_banned')->alias('b')
            ->join('user u', 'u.id=b.user_id')
            ->where('b.tory_id', $data['id'])
            ->where('b.much_id', $data['much_id'])
            ->field('b.id,b.refer_time,b.beget,u.user_head_sculpture,u.user_nick_name')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                $list[$k]['beget'] = emoji_decode($v['beget']);
                $list[$k]['refer_time'] = date('Y-m-d H:i:s', $v['refer_time']);
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                // 是否有申诉
                $mutter = Db::name('user_mutter')
                    ->where('much_id', $data['much_id'])
                    ->where('ban_id', $v['id'])
                    ->find();
                if ($mutter) {
                    $mutter['mute_time'] = date('Y-m-d H:i:s', $mutter['mute_time']);
                    $list[$k]['mutter'] = $mutter;
                } else {
                    $list[$k]['mutter'] = array();
                }

            }
        }
        $rs['info'] = $list;
        return $this->json_rewrite($rs);
    }

    /**
     * 解除禁言
     */
    public function jie_user_banned()
    {
        $data = input('param.');
        $util = new Util();
        $check = $util->get_user_check($data['openid'], $data['tory_id']);
        if ($check == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '解除失败，您没有权限1']);
        }
        // 启动事务
        Db::startTrans();
        try {
            //解除禁言
            $update = Db::name('user_banned')->where('id', $data['id'])->where('much_id', $data['much_id'])->delete();
            if (!$update) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '解除失败，请稍候重试-1！']);
            }
            //是否有申诉
            $mutter = Db::name('user_mutter')
                ->where('much_id', $data['much_id'])
                ->where('ban_id', $data['id'])
                ->update(['status' => 1, 'mute_type' => 1]);
            if (!$mutter == false) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '解除失败，请稍候重试-2！']);
            }
            Db::commit();
            return $this->json_rewrite(['status' => 'success', 'msg' => '解除成功！']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '解除失败，请稍候重试1-3！']);
        }
    }

    /**
     * 关进小黑屋
     */
    public function get_user_status()
    {

        $data = input('param.');
        $update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        return $update['status'];
    }

    /**
     * 绑定手机号
     */
    public function get_user_phone_new()
    {
        $data = input('param.');
        $we = new Util();
        $token = $we->getWchatAcctoken($data['much_id']);
        $url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" . $token;
        $res = Gyration::_requestPost($url, json_encode(['code' => $data['code']], true));
        $res = json_decode($res, true);
        if ($res['errcode'] != 0) {
            $rs = ['status' => 'error', 'msg' => '绑定失败，' . $res['errmsg']];
            return $this->json_rewrite($rs);
        }
        $user_service = new UserService();
        $userInfo = Db::name('user')->where('id', $data['uid'])->find();
        if (empty($this->user_info['user_phone'])) {
            //判断是否在黑名单
            $check = $user_service->BlockingPhone($res['phone_info']['phoneNumber'], $data['much_id']);
            if ($check == 1) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '很抱歉，您的手机号状态异常，请联系官方客服！']);
            }
            //如果是游客的话
            if ($userInfo['tourist'] == 1) {
                //生成随机名称
                $item['user_nick_name'] = $this->generateCuteNickname();
                $item['tourist'] = 0;
                $domain = explode(':', $_SERVER['HTTP_HOST']);
                $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
                $def = "https://{$domain[0]}{$absAddress[0]}static/applet_icon/default.png";
                $item['user_head_sculpture'] = $def;
            }
            $item['user_phone'] = $res['phone_info']['phoneNumber'];
            $user = Db::name('user')->where('id', $data['uid'])->update($item);
            if ($user) {
                $userInfo = Db::name('user')->where('id', $data['uid'])->find();
                $userInfo['openid'] = $userInfo['user_wechat_open_id'];
                $userInfo['uid'] = $userInfo['id'];
                $userInfo['user_nick_name'] = emoji_decode($userInfo['user_nick_name']);
                $rs = ['status' => 'success', 'msg' => '登陆成功！', 'phone' => $res['phone_info']['phoneNumber'], 'userInfo' => $userInfo];
                return $this->json_rewrite($rs);
            } else {
                $rs = ['status' => 'error', 'msg' => '登陆失败，code:-1'];
                return $this->json_rewrite($rs);
            }
        } else {
            $userInfo = Db::name('user')->where('id', $data['uid'])->find();
            $userInfo['openid'] = $userInfo['user_wechat_open_id'];
            $userInfo['uid'] = $userInfo['id'];
            $userInfo['user_nick_name'] = emoji_decode($userInfo['user_nick_name']);
            $rs = ['status' => 'success', 'msg' => '登陆成功！', 'phone' => ciphertext($res['phone_info']['phoneNumber']), 'userInfo' => $userInfo];
            return $this->json_rewrite($rs);
        }
    }

    public function create_uuid($prefix = "Q")
    {
        $str = md5(uniqid(mt_rand(), true));
        $uuid = substr($str, 0, 8) . '';
        $uuid .= substr($str, 8, 4) . '';
        $uuid .= substr($str, 12, 4) . '';
        $uuid .= substr($str, 16, 4) . '';
        $uuid .= substr($str, 20, 12);
        return $prefix . $uuid;
    }

    public function generateCuteNickname()  // 生成可爱风格的随机昵称
    {
        // 第一部分：可爱的形容词或状态词
        $prefixes = array('奶糖', '蜜饯', '软糖', '泡芙', '曲奇', '布丁', '奶霜', '果酱', '糯米', '芋圆',
            '萌爪', '绒球', '毛团', '软毛', '粉团', '糖豆', '蜜果', '甜橙', '莓莓', '果粒',
            '喵呜', '汪叽', '咕叽', '咿呀', '哼唧', '咕噜', '咩咩', '跳跳', '晃晃', '摇摇',
            '星星', '月亮', '云朵', '雨滴', '雪花', '暖阳', '清风', '露珠', '嫩芽', '花苞',
            '小呆', '小懵', '小乖', '小萌', '小憨', '小甜', '小软', '小糯', '小暖', '小乐');

        // 第二部分：可爱的名词或小动物
        $suffixes = array('小熊', '小兔', '小猫', '小狗', '小鹿', '小狐', '小羊', '小猪', '小熊猫', '小松鼠',
            '饼干', '蛋糕', '面包', '糖果', '巧克力', '冰淇淋', '甜甜圈', '棉花糖', '糖葫芦', '麦芽糖',
            '气球', '风筝', '玩偶', '积木', '绘本', '音符', '画笔', '彩虹', '流星', '阳光',
            '花朵', '叶子', '果实', '种子', '小草', '露珠', '小溪', '云朵', '雨滴', '雪花',
            '泡泡', '圈圈', '点点', '线线', '球球', '片片', '颗颗', '朵朵', '串串', '堆堆');

        // 随机组合两部分词语
        $nickname = $prefixes[rand(0, count($prefixes) - 1)] . $suffixes[rand(0, count($suffixes) - 1)];

        return $nickname; // 返回生成的可爱昵称
    }


    /**
     * 绑定手机号
     */
    public function get_user_phone()
    {
        $data = input('param.');
        $getConfig = cache('fatal_' . $data['much_id']);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $data['much_id'])->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $data['much_id'], $getConfig);
            }
        }
        $user_service = new UserService();
        $ok = $user_service->decryptData($data['encryptedData'], $data['sessionKey'], $getConfig['app_id'], $data['iv'], $dd);
        $ad = json_decode($dd, true);
        if (empty($this->user_info['user_phone'])) {
            //判断是否在黑名单
            $check = $user_service->BlockingPhone($ad['phoneNumber'], $data['much_id']);
            if ($check == 1) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '很抱歉，您的手机号状态异常，请联系官方客服！']);
            }
            $user = Db::name('user')->where('id', $data['uid'])->update(['user_phone' => $ad['phoneNumber']]);
            if ($user) {
                $rs = ['status' => 'success', 'msg' => '绑定成功！', 'phone' => $ad['phoneNumber']];
                return $this->json_rewrite($rs);
            } else {
                $rs = ['status' => 'error', 'msg' => '绑定失败，code:' . $ok];
                return $this->json_rewrite($rs);
            }
        } else {
            $rs = ['status' => 'success', 'msg' => '绑定成功！', 'phone' => ciphertext($this->user_info['user_phone'])];
            return $this->json_rewrite($rs);
        }
    }

    /**
     * 上传圈子背景图片
     */
    public function territory_img_upload()
    {

        $data = input('param.');
        //判断权限
        $util = new Util();
        $check = $util->check_qq($data['openid'], $data['tory_id']);
        if ($check == 'no' || $check == 'xiao') {
            return $this->json_rewrite(['status' => 'error', 'msg' => '没有权限！']);
        }
        $home = new Home();
        //查询是否开启网络验证
        $ins = Db::name('user_violation')->where('much_id', $data['much_id'])->find();
        if ($ins['open_network_images_offend'] == 1) {
            $res = $home->check_img($data['much_id']);
            if ($res['errcode'] == 87014) {
                $code['msg'] = '图片含有违法违规内容！-1';
                $code['status'] = 'error';
                return $this->json_rewrite($code);
            }
        }
        if ($ins['open_network_images_offend'] == 2) {
            $res = Moderation::Img($data['much_id']);
            if ($res['status'] == 'error') {
                $msg['msg'] = '图片含有违法违规内容！-2';
                $msg['status'] = 'error';
                return $this->json_rewrite($msg);
            }
        }
        $up = new Upload($data['much_id']);
        $code = $up->operate();
        if ($code['status'] == 'error') {
            return $this->json_rewrite(['status' => 'error', 'msg' => '上传失败，请检查上传配置！']);
        }
        if (!isset($data['key']) || $data['key'] == 1) {
//更新圈子背景
            $res = Db::name('territory')->where('id', $data['tory_id'])->where('much_id', $data['much_id'])->update(['realm_back_img' => $code['url']]);
        }
        if ($data['key'] == 2) {
            $res = Db::name('territory')->where('id', $data['tory_id'])->where('much_id', $data['much_id'])->update(['group_qrcode' => $code['url']]);
        }

        if (!$res) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '上传失败，请检查上传配置！']);
        }
        return $this->json_rewrite(['status' => 'success', 'msg' => '上传成功！', 'url' => $code['url']]);

    }

    /**
     * 截图监听
     */
    public function CaptureScreen()
    {
        $data = input('param.');
        $d['user_id'] = $this->user_info['id'];
        $d['scene_name'] = $this->ScreenType($data['page']);
        if (empty($data['activeId']) || $data['activeId'] == 'undefined') {
            $d['scene_path'] = $data['page'];
        } else {
            $d['scene_path'] = $data['page'] . '当前视频ID：' . $data['activeId'];
        }

        $d['location_ip'] = request()->ip();
        $d['add_time'] = time();
        $d['much_id'] = $data['much_id'];
        $res = Db::name('user_screenshot')->insert($d);
        return $this->json_rewrite(['msg' => '1']);
    }

    public function ScreenType($page)
    {
        $index = strpos($page, '?');
        if (!empty($index)) {
            $page = substr($page, 0, $index);
        }
        switch ($page) {
            case 'yl_welore/pages/index/index':
                $msg = '首页';
                break;
            case 'yl_welore/pages/circle/index':
                $msg = '广场';
                break;
            case 'yl_welore/pages/packageA/circle_info/index':
                $msg = '圈子';
                break;
            case 'yl_welore/pages/user_smail/index':
                $msg = '站内信';
                break;
            case 'yl_welore/pages/user/index':
                $msg = '我的';
                break;
            case 'yl_welore/pages/packageB/user_vip/index':
                $msg = '会员';
                break;
            case 'yl_welore/pages/packageC/user_details/index':
                $msg = '钱包';
                break;
            case 'yl_welore/pages/shell_mall/index':
                $msg = '商城';
                break;
            case 'yl_welore/pages/packageA/article/index':
                $msg = '帖子';
                break;
            case 'yl_welore/pages/packageA/add/index':
                $msg = '发布';
                break;
            case 'yl_welore/pages/packageD/whisper/index':
                $msg = '小秘密';
                break;
            case 'yl_welore/pages/packageE/notes/index':
                $msg = '小纸条';
                break;
            case 'yl_welore/pages/packageD/user_welfare/index':
                $msg = '抽奖';
                break;
            case 'yl_welore/pages/packageC/user_invitation/index':
                $msg = '邀请';
                break;
            case 'yl_welore/pages/packageB/my_home/index':
                $msg = '个人主页';
                break;
            case 'yl_welore/pages/packageD/user_task/index':
                $msg = '任务中心';
                break;
            case 'yl_welore/pages/packageF/netdisc/index':
                $msg = '我的网盘';
                break;
            case 'yl_welore/pages/packageC/service_centre/index':
                $msg = '服务中心';
                break;
            case 'yl_welore/pages/packageB/search/index':
                $msg = '搜索内容';
                break;
            case 'yl_welore/pages/packageE/certification_list/index':
                $msg = '认证列表';
                break;
            case 'yl_welore/pages/packageD/user_medal/index':
                $msg = '个性装扮';
                break;
            case 'yl_welore/pages/packageF/full_video/index':
                $msg = '抖音视频列表';
                break;
            default:
                $msg = "未知";
        };
        return $msg;
    }
}