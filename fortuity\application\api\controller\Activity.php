<?php

namespace app\api\controller;


use think\Db;

class Activity extends Base
{
    /**
     * 报名活动
     */
    public function participation_activities()
    {
        $data = input('param.');
        //自己报名自己的活动
        $paper = Db::name('paper')->where('id', $data['paper_id'])->where('much_id', $data['much_id'])->find();
        if ($paper['user_id'] == $this->user_info['id']) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '这是您自己的活动哦！']);
        }
        //活动详情
        $brisk_team = Db::name('brisk_team')->where('much_id', $data['much_id'])->where('id', $data['at_id'])->find();
        //查询是否已参加
        $check = Db::name('user_brisk_team')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->where('paper_id', $data['paper_id'])->where('brisk_id', $data['at_id'])->count();
        if ($check > 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '您已参加该活动！']);
        }
        if ($brisk_team['number_of_people'] != 0) {
            //查看报名人数
            $user_brisk_team = Db::name('user_brisk_team')->where('much_id', $data['much_id'])->where('paper_id', $data['paper_id'])->where('brisk_id', $data['at_id'])->count();
            if ($user_brisk_team >= $brisk_team['number_of_people']) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '来晚一步！']);
            }
        }
        $d['brisk_id'] = $data['at_id'];
        $d['paper_id'] = $data['paper_id'];
        $d['user_id'] = $this->user_info['id'];
        $d['rand_captcha'] = $this->getRandNumber($data['much_id']);
        $d['partake_time'] = time();
        $d['much_id'] = $data['much_id'];
        $ins = Db::name('user_brisk_team')->insert($d);
        if ($ins) {
            return $this->json_rewrite(['status' => 'success', 'msg' => '已收入卡包！']);
        } else {
            return $this->json_rewrite(['status' => 'error', 'msg' => '报名失败，请稍后重试！']);
        }
    }

    /**
     * 我的卡包
     */
    public function get_card_list()
    {
        $data = input('param.');
        $list = Db::name('user_brisk_team')->alias('b')
            ->join('user u', 'u.id=b.user_id')
            ->join('brisk_team t', 't.id=b.brisk_id')
            ->where('b.much_id', $data['much_id'])
            ->where('b.user_id', $this->user_info['id'])
            ->field('b.*,t.*')
            ->order('b.partake_time desc')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                //获取帖子详情
                $paper = Db::name('paper')->where('id', $v['paper_id'])->where('much_id', $data['much_id'])->find();
                //发布人信息
                $user = Db::name('user')->where('id', $paper['user_id'])->where('much_id', $data['much_id'])->find();
                $user['user_nick_name'] = emoji_decode($user['user_nick_name']);
                if ($v['end_time'] < time()) {
                    $list[$k]['overdue'] = 2;  //已过期
                } else {
                    $list[$k]['overdue'] = 1;  //未过期
                }
                $list[$k]['user'] = $user;
                $list[$k]['paper'] = $paper;
                $list[$k]['start_time'] = date('Y年m月d日 H:i', $v['start_time']);
                $list[$k]['end_time'] = date('Y年m月d日 H:i', $v['end_time']);
                $list[$k]['partake_time'] = date('Y年m月d日 H:i', $v['partake_time']);
                $list[$k]['write_off_time'] = date('Y年m月d日 H:i', $v['write_off_time']);
            }
        } else {
            $list = [];
        }
        return $this->json_rewrite($list);
    }

    /**
     * 我发布的活动
     */
    public function get_my_brisk_team()
    {
        $data = input('param.');
        $list = Db::name('paper')->alias('p')
            ->join('brisk_team b', 'b.paper_id=p.id')
            ->where('p.user_id', $this->user_info['id'])
            ->field('b.*')
            ->order('b.end_time desc')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                //获取帖子详情
                $paper = Db::name('paper')->where('id', $v['paper_id'])->where('much_id', $data['much_id'])->find();
                //发布人信息
                $user = Db::name('user')->where('id', $paper['user_id'])->where('much_id', $data['much_id'])->find();
                $user['user_nick_name'] = emoji_decode($user['user_nick_name']);
                if ($v['end_time'] < time()) {
                    $list[$k]['overdue'] = 2;  //已过期
                } else {
                    $list[$k]['overdue'] = 1;  //未过期
                }
                $list[$k]['user'] = $user;
                $list[$k]['paper'] = $paper;
                $list[$k]['start_time'] = date('Y年m月d日 H:i', $v['start_time']);
                $list[$k]['end_time'] = date('Y年m月d日 H:i', $v['end_time']);
                $list[$k]['partake_time'] = date('Y年m月d日 H:i', $v['partake_time']);
            }
        } else {
            $list = [];
        }
        return $this->json_rewrite($list);
    }

    /*
     * 验证验证码
     */
    public function ok_click()
    {
        $data = input('param.');
        //查询我发的活动贴
        $paper = Db::name('paper')->where('id', $data['paper_id'])->where('study_type', 3)->where('much_id', $data['much_id'])->find();
        if ($paper) {
            if ($paper['user_id'] != $this->user_info['id']) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '您无权验证！']);
            }
        } else {
            return $this->json_rewrite(['status' => 'error', 'msg' => '您无权验证！']);
        }
        //验证
        $brisk_team = Db::name('user_brisk_team')->where('rand_captcha', $data['check'])->where('paper_id', $data['paper_id'])->find();
        if ($brisk_team) {
            //活动详情
            $brisk_info = Db::name('brisk_team')->where('id', $brisk_team['brisk_id'])->where('much_id', $data['much_id'])->find();
            if ($brisk_team['is_write_off'] == 1) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '验证码已被验证！']);
            }
            if ($brisk_info['start_time'] > time()) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '活动未开始！']);
            }
            if ($brisk_info['end_time'] < time()) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '活动已结束！']);
            }


            $is = Db::name('user_brisk_team')->where('id', $brisk_team['id'])->update(['is_write_off' => 1, 'write_off_time' => time()]);
            if ($is) {
                return $this->json_rewrite(['status' => 'success', 'msg' => '验证成功！']);
            } else {
                return $this->json_rewrite(['status' => 'error', 'msg' => '验证码错误！']);
            }
        } else {
            return $this->json_rewrite(['status' => 'error', 'msg' => '验证码错误！']);
        }
    }

    /**
     * 获取已验证用户
     */
    public function get_verified()
    {
        $data = input('param.');
        $list = Db::name('user_brisk_team')->alias('b')
            ->join('user u', 'u.id=b.user_id')
            ->where('b.paper_id', $data['paper_id'])
            ->where('b.is_write_off', '1')
            ->where('b.much_id', $data['much_id'])
            ->field('u.user_head_sculpture,u.user_nick_name,b.*')
            ->order('b.write_off_time desc')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['write_off_time'] = date('Y年m月d日 H:i', $v['write_off_time']);
            }
        } else {
            $list = [];
        }
        return $this->json_rewrite($list);
    }

    public function get_user_time()
    {
        $data = input('param.');
        $list = Db::name('user_brisk_team')->alias('b')
            ->join('user u', 'u.id=b.user_id')
            ->where('b.paper_id', $data['paper_id'])
            ->where('b.much_id', $data['much_id'])
            ->field('u.user_head_sculpture,u.user_nick_name,b.partake_time')
            ->order('b.partake_time desc')
            ->page($data['time_page'], 10)
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['partake_time'] = date('Y年m月d日 H:i', $v['partake_time']);
            }
        } else {
            $list = [];
        }
        return $this->json_rewrite($list);
    }

    public function getRandNumber($much_id)
    {
        $start = 0;
        $end = 9;
        $length = 8;
        //初始化变量为0
        $connt = 0;
        //建一个新数组
        $temp = array();
        while ($connt < $length) {
            //在一定范围内随机生成一个数放入数组中
            $temp[] = mt_rand($start, $end);
            //$data = array_unique($temp);
            //去除数组中的重复值用了“翻翻法”，就是用array_flip()把数组的key和value交换两次。这种做法比用 array_unique() 快得多。
            $data = array_flip(array_flip($temp));
            //将数组的数量存入变量count中
            $connt = count($data);
        }
        //为数组赋予新的键名
        shuffle($data);
        //数组转字符串
        $str = implode(",", $data);
        //替换掉逗号
        $number = str_replace(',', '', $str);

        //保证不重复
        $check = Db::name('user_brisk_team')->where('much_id', $much_id)->where('rand_captcha', $number)->find();
        if ($check) {
            return $this->getRandNumber($much_id);
        } else {
            return $number;
        }

    }
}