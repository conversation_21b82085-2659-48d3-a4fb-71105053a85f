<?php

namespace app\api\controller;


use app\api\service\Util;
use app\common\RedisLock;
use think\Cache;
use think\Db;

class Subscribe extends Base
{
    /**
     * 获取所有模板
     */
    public function get_msg()
    {
        $data = input('param.');

        $info = Db::name('subscribe')->where('much_id', $data['much_id'])->field('parallelism_data')->find();
        if (empty($info)) {
            return '';
        }
        $info['parallelism_data'] = json_decode($info['parallelism_data']);
        return $this->json_rewrite($info);
    }

    /**
     * 获取抽奖信息
     */
    public function get_raffle()
    {
        $data = input('param.');
        $rs = ['status' => 'success', 'msg' => '成功！', 'error_code' => 0, 'info' => array(), 'list' => array(), 'count' => 0];
        $info = Db::name('event_raffle')->where('status', '1')->where('much_id', $data['much_id'])->find();
        if (empty($info)) {
            $rs = ['status' => 'error', 'msg' => '系统还没有设置奖品哦！', 'error_code' => 1];
            return $this->json_rewrite($rs);
        }
        //抽奖开始时间判断
        if ($info['start_time'] > time()) {
            $rs['error_code'] = 2;
        }
        //抽奖结束时间判断
        if ($info['end_time'] < time()) {
            $rs['error_code'] = 3;
        }
        $for = json_decode($info['prize_content'], true);
        $count = 0;
        if (is_array($for)) {
            foreach ($for as $k => $v) {
                if ($for[$k]['reserve'] > 0) {
                    $count = 1;
                }
                $for[$k]['opa'] = 0.9;
            }
        }
        $info['prize_content'] = $for;
        $info['prize_count'] = $count;
        //查询今天我抽了几次了
        $get_user_count = Db::name('user_raffle_records')->whereTime('join_time', 'today')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->count();
        //查询获奖人员
        $user_list = Db::name('user_raffle_records')->alias('r')
            ->join('user u', 'u.id=r.user_id')
            ->where('r.much_id', $data['much_id'])
            ->where('r.win_type', '<>', '0')
            ->field('r.join_time,r.prize_name,u.user_nick_name')
            ->limit(20)
            ->order('join_time desc')
            ->group('r.user_id')
            ->select();
        foreach ($user_list as $k => $v) {
            $user_list[$k]['join_time'] = date('H:i', $v['join_time']);
            $user_list[$k]['user_nick_name'] = substr_cut(emoji_decode($v['user_nick_name']));
        }
        //查询激励视频次数
        $incentive_duct = Db::name('advertise')
            ->where('much_id', $data['much_id'])
            ->field('incentive_duct')
            ->find();
        $vd = Db::name('user_watch_ads')
            ->whereTime('fulfill_time', 'today')
            ->where('ad_type', 0)
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->count();
        if ($incentive_duct['incentive_duct'] == 0) {
            $info['free_ad_valve'] = 1;
        } else {
            if ($vd >= $incentive_duct['incentive_duct']) {
                $info['free_ad_valve'] = 0;
            } else {
                $info['free_ad_valve'] = 1;
            }
        }

        $rs = ['status' => 'success', 'msg' => '成功！', 'error_code' => $rs['error_code'], 'info' => $info, 'list' => $user_list, 'count' => $get_user_count];
        return $this->json_rewrite($rs);
    }


    /**
     * 抽奖判断
     */
    public function set_user_records()
    {
        $data = input('param.');
        //判断当前用户是否是游客
        if ($this->user_info['tourist'] == 1) {
            return ['status' => 'error', 'msg' => '请登录！', 'error_code' => -3];
        }
        $redis = new RedisLock();
        if ($redis->_redis != null) {
            //  获取锁 或 上锁 锁的有效期为5秒
            $lockStatus = $redis->lock('records_lock' . $data['much_id'], 3);
            if ($lockStatus) {
                //  逻辑代码
                $res = $this->records($data);
                if ($res['status'] == 'error') {
                    $rs = ['status' => 'error', 'msg' => $res['msg'], 'error_code' => -1];
                    return $this->json_rewrite($rs);
                }
                //  解锁
                $redis->unlock('records_lock' . $data['much_id']);
                Db::name('prompt_msg')->insert(['capriole' => 13, 'tyid' => $res['get_info']['id'], 'msg_time' => time(), 'type' => 0, 'retter' => '用户：' . $this->user_info['user_nick_name'] . '，在抽奖活动中抽中了 [' . $res['info']['prize_name'] . ']', 'status' => 0, 'much_id' => $data['much_id']]);
                $notices = Db::name('prompt_msg')
                    ->where('status', 0)
                    ->where('type', 0)
                    ->where('much_id', $data['much_id'])
                    ->count('*');
                cache('notices_' . $data['much_id'], $notices);


                return $this->json_rewrite(['status' => 'success', 'msg' => '抽奖成功！', 'error_code' => 0, 'id' => $res['id'], 'info' => $res['info'], 'count' => $res['count']]);
            } else {
                $rs = ['status' => 'error', 'msg' => '当前活动火爆,请稍后重试！', 'error_code' => -2];
                return $this->json_rewrite($rs);
            }
        } else {
            $randomLock = md5(uniqid(mt_rand(), true));
            $getLocked = Cache::remember('records_lock' . $data['much_id'], $randomLock, 3);
            //  未上锁
            if ($randomLock == $getLocked) {
                //  逻辑代码
                $res = $this->records($data);
                if ($res['status'] == 'error') {
                    $rs = ['status' => 'error', 'msg' => $res['msg'], 'error_code' => -1];
                    return $this->json_rewrite($rs);
                }
                Cache::rm('records_lock' . $data['much_id']);

                Db::name('prompt_msg')->insert(['capriole' => 13, 'tyid' => $res['get_info']['id'], 'msg_time' => time(), 'type' => 0, 'retter' => '用户：' . $this->user_info['user_nick_name'] . '，在抽奖活动中抽中了 [' . $res['info']['prize_name'] . ']', 'status' => 0, 'much_id' => $data['much_id']]);
                $notices = Db::name('prompt_msg')
                    ->where('status', 0)
                    ->where('type', 0)
                    ->where('much_id', $data['much_id'])
                    ->count('*');
                cache('notices_' . $data['much_id'], $notices);


                return $this->json_rewrite(['status' => 'success', 'msg' => '抽奖成功！', 'error_code' => 0, 'id' => $res['id'], 'info' => $res['info'], 'count' => $res['count']]);
            } else {
                $rs = ['status' => 'error', 'msg' => '当前活动火爆,请稍后重试！', 'error_code' => -3];
                return $this->json_rewrite($rs);
            }
        }
    }

    /**
     * 抽奖逻辑
     */
    public function records($data)
    {
        //$data = input('param.');
        $util = new Util();
        $info = Db::name('event_raffle')->where('status', '1')->where('much_id', $data['much_id'])->find();
        if (empty($info)) {
            return ['status' => 'error', 'msg' => '系统还没有设置奖品哦！', 'error_code' => 1];
        }
        //抽奖开始时间判断
        if ($info['start_time'] > time()) {
            return ['status' => 'error', 'msg' => '抽奖活动还没有开始哦！', 'error_code' => 1];
        }
        //抽奖结束时间判断
        if ($info['end_time'] < time()) {
            return ['status' => 'error', 'msg' => '抽奖活动已经结束了！', 'error_code' => 1];
        }


        //查询今天我抽了几次了
        $get_user_count = Db::name('user_raffle_records')->whereTime('join_time', 'today')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->count();
        if ($info['draw_restrictions'] != 0) {
            if ($get_user_count >= $info['draw_restrictions']) {
                return ['status' => 'error', 'msg' => '今日抽奖已达上限！', 'error_code' => 18];
            }
        }

        // 奖品数组
        $proArr = json_decode($info['prize_content'], true);
        // 奖品等级奖品权重数组
        $arr = array();
        if (is_array($proArr)) {
            foreach ($proArr as $key => $val) {
                if ($val['reserve'] > 0) {
                    $arr[$key] = $val['odds'];
                }
            }
        }
        if (count($arr) == 0) {
            return ['status' => 'error', 'msg' => '奖品都没有库存啦！', 'error_code' => 1];
        }
        while (true) {
            // 中奖 id
            $rid = $this->get_rand($arr);
            if (bcsub($proArr[$rid]['reserve'], 1) >= 0) {
                break;
            }

        }
        //查询是否要支付费用
        $check_free_chance = 0;
        //前几次免费
        if ($info['free_chance'] == 0) {//不免费
            $check_free_chance = 1; //不免费
        } else {
            if ($get_user_count >= $info['free_chance']) {
                $check_free_chance = 1; //不免费
            }
        }
        if ($data['type'] == 1) {
            $check_free_chance = 0;
        }
        //中奖详情
        $prize_content = $proArr[$rid];
        Db::startTrans();
        try {
            //付费
            if ($check_free_chance == 1) {
                //扣除当前用户的贝壳或者积分
                //消费明细
                if ($info['deplete_type'] == 0) {//使用贝壳
                    //查询用户贝壳是否够
                    if (bcsub($this->user_info['conch'], $info['deplete_score'], 2) < 0) {
                        Db::rollback();
                        return ['status' => 'error', 'msg' => $this->design['currency'] . '不足无法抽奖！', 'error_code' => 1];
                    }
                    $solution = '抽奖消耗' . $info['deplete_score'] . $this->design['currency'];
                    $user_info = $util->get_openId_user($data['openid'], $data['much_id']);
                    $confer = bcsub($user_info['conch'], $info['deplete_score'], 2);
                    $ins_amount = $util->user_amount($this->user_info['id'], 2, $info['deplete_score'], $user_info['fraction'], $user_info['fraction'], $user_info['conch'], bcsub($user_info['conch'], $info['deplete_score'], 2), $info['deplete_type'], $solution, $data['much_id']);
                } else {
                    $user_info = $util->get_openId_user($data['openid'], $data['much_id']);
                    if (bcsub($user_info['fraction'], $info['deplete_score'], 2) < 0) {
                        Db::rollback();
                        return ['status' => 'error', 'msg' => $this->design['confer'] . '不足无法抽奖！', 'error_code' => 2];
                    }
                    $solution = '抽奖消耗' . $info['deplete_score'] . $this->design['confer'];
                    $confer = bcsub($user_info['fraction'], $info['deplete_score'], 2);
                    $ins_amount = $util->user_amount($this->user_info['id'], 2, $info['deplete_score'], $user_info['fraction'], $confer, $user_info['conch'], $user_info['conch'], $info['deplete_type'], $solution, $data['much_id']);

                }
                if (!$ins_amount) {
                    Db::rollback();
                    return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 3];
                }
                //减去用户积分或者贝壳
                $user_set = Db::name('user')->where('much_id', $data['much_id'])->where('id', $this->user_info['id'])->update([intval($info['deplete_type']) == 0 ? 'conch' : 'fraction' => $confer]);
                if (!$user_set) {
                    Db::rollback();
                    return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 4];
                }
            }
            //免费
            //增加各种获奖积分0.未中奖 1.实物奖励 2.积分奖励 3.贝壳奖励 4.经验奖励 5.荣誉值奖励
            if ($prize_content['choose'] == 2) {//积分奖励
                $solution = '抽奖收益' . $prize_content['score'] . $this->design['confer'];
                $user_info = $util->get_openId_user($data['openid'], $data['much_id']);
                $confer = bcadd($user_info['fraction'], $prize_content['score'], 2);
                $ins_amount = $util->user_amount($this->user_info['id'], 3, $prize_content['score'], $user_info['fraction'], $confer, $user_info['conch'], $user_info['conch'], 1, $solution, $data['much_id']);
                if (!$ins_amount) {
                    Db::rollback();
                    return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 5];
                }
                $user_set = Db::name('user')->where('much_id', $data['much_id'])->where('id', $this->user_info['id'])->update(['fraction' => $confer]);
                if (!$user_set) {
                    Db::rollback();
                    return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 6];
                }
            }
            if ($prize_content['choose'] == 3) {//贝壳奖励
                $user_info = $util->get_openId_user($data['openid'], $data['much_id']);
                $solution = '抽奖收益' . $prize_content['score'] . $this->design['currency'];
                $confer = bcadd($user_info['conch'], $prize_content['score'], 2);
                $ins_amount = $util->user_amount($this->user_info['id'], 3, $prize_content['score'], $user_info['fraction'], $user_info['fraction'], $user_info['conch'], bcadd($user_info['conch'], $info['deplete_score'], 2), 0, $solution, $data['much_id']);
                if (!$ins_amount) {
                    Db::rollback();
                    return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 8];
                }
                $user_set = Db::name('user')->where('much_id', $data['much_id'])->where('id', $this->user_info['id'])->update(['conch' => $confer]);
                if (!$user_set) {
                    Db::rollback();
                    return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 7];
                }
            }
            if ($prize_content['choose'] == 4) {//经验值奖励
                $user_info = $util->get_openId_user($data['openid'], $data['much_id']);
                //当前用户经验值
                $this_exp = bcadd($user_info['experience'], $prize_content['score'], 2);
                //当前用户等级
                $this_level = $user_info['level'];
                while (true) {
                    $this_level++;
                    $user_level = Db::name('user_level')->where('level_hierarchy', $this_level)->where('much_id', $data['much_id'])->find();
                    //下一级没有内容跳出循环
                    if (empty($user_level)) {
                        $user_fraction = Db::name('user')->where('id', $this->user_info['id'])->update(['experience' => $this_exp]);
                        if ($user_fraction === false) {
                            Db::rollback();
                            return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 9];
                        }
                        break;
                    }
                    //当前经验大于下一级经验
                    if ($this_exp >= $user_level['need_experience']) {
                        //计算余下经验值
                        $this_exp = bcsub($this_exp, $user_level['need_experience'], 2);
                        //更新用户信息
                        $user_fraction = Db::name('user')->where('id', $this->user_info['id'])->update(['honor_point' => bcadd($user_info['honor_point'], $user_level['honor_point'], 2), 'level' => $this_level, 'experience' => $this_exp]);
                        if (!$user_fraction) {
                            Db::rollback();
                            return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 10];
                        }
                    } else {
                        //更新用户信息
                        //判断是否和数据库一致
                        $user_fraction = Db::name('user')->where('id', $this->user_info['id'])->update(['experience' => $this_exp]);
                        if ($user_fraction === false) {
                            Db::rollback();
                            return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 11];
                        }
                        break;
                    }
                }
                //荣誉值明细表增加数据
                $logger['user_id'] = $this->user_info['id'];
                $logger['type'] = 0;
                $logger['cypher'] = 0;
                $logger['dot_before'] = $this->user_info['experience'];
                $logger['points'] = $prize_content['score'];
                $logger['dot_after'] = $this_exp;
                $logger['receive_time'] = time();
                $logger['much_id'] = $data['much_id'];
                $logger['dot_cap'] = '抽奖奖品：' . $prize_content['score'] . '经验值';
                $user_amount = Db::name('user_exp_glory_logger')->insert($logger);
                if (!$user_amount) {
                    Db::rollback();
                    return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 12];
                }

            }
            if ($prize_content['choose'] == 5) {//荣誉值奖励
                $user_info = $util->get_openId_user($data['openid'], $data['much_id']);
                //当前用户荣誉点
                $this_exp = bcadd($user_info['honor_point'], $prize_content['score'], 2);

                //更新用户信息
                $user_honor_point = Db::name('user')->where('id', $this->user_info['id'])->update(['honor_point' => $this_exp]);
                if ($user_honor_point === false) {
                    Db::rollback();
                    return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 13];
                }
                //荣誉值明细表增加数据
                $logger['user_id'] = $this->user_info['id'];
                $logger['type'] = 1;
                $logger['cypher'] = 0;
                $logger['dot_before'] = $user_info['honor_point'];
                $logger['points'] = $prize_content['score'];
                $logger['dot_after'] = $this_exp;
                $logger['receive_time'] = time();
                $logger['much_id'] = $data['much_id'];
                $logger['dot_cap'] = '抽奖奖品：' . $prize_content['score'] . '荣誉值';
                $user_amount = Db::name('user_exp_glory_logger')->insert($logger);
                if (!$user_amount) {
                    Db::rollback();
                    return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 14];
                }

            }
            //抽奖记录增加数据
            $raffle_records['user_id'] = $this->user_info['id'];
            $raffle_records['record_number'] = uniqid();
            $raffle_records['er_id'] = $info['id'];
            $raffle_records['join_time'] = time();
            $raffle_records['win_type'] = $prize_content['choose'];
            $raffle_records['prize_name'] = $prize_content['prize_name'];
            $raffle_records['reward_score'] = $prize_content['score'];
            $raffle_records['address_details'] = NULL;
            $raffle_records['courier_convert'] = NULL;
            $raffle_records['see_time'] = intval($prize_content['choose']) == 1 ? 0 : time();
            $raffle_records['delivery_status'] = (int)$prize_content['choose'] == 1 ? 0 : 1;
            $raffle_records['much_id'] = $data['much_id'];
            $ins_raffle_records = Db::name('user_raffle_records')->insert($raffle_records);
            if (!$ins_raffle_records) {
                Db::rollback();
                return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 15];
            }
            //成功重置JSON
            $prize_content['reserve'] = bcsub($prize_content['reserve'], 1);
            $proArr[$rid] = $prize_content;
            $prize_content_json = json_encode($proArr, JSON_FORCE_OBJECT + JSON_UNESCAPED_UNICODE);
            //更新json
            $prize_up = Db::name('event_raffle')->where('id', $info['id'])->update(['prize_content' => $prize_content_json]);
            if ($prize_up === false) {
                Db::rollback();
                return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 16];
            }
            // 提交事务
            Db::commit();
            return ['status' => 'success', 'msg' => '抽奖成功！', 'error_code' => 0, 'get_info' => $info, 'id' => $rid, 'info' => $proArr[$rid], 'count' => $get_user_count];
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return ['status' => 'error', 'msg' => '系统错误！', 'error_code' => 17, 'exc' => $e->getMessage()];
        }

    }

    //增加用户地址
    public function ok_address()
    {
        $data = input('param.');
        if (empty($data['name'])) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '收货人不能为空！', 'error_code' => 1]);
        }
        if (empty($data['phone'])) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '电话不能为空！', 'error_code' => 2]);
        }
        if (empty($data['address'])) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '地址不能为空！', 'error_code' => 3]);
        }
        $arr = ['name' => $data['name'], 'phone' => $data['phone'], 'address' => $data['address']];
        $up = Db::name('user_raffle_records')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['address_details' => json_encode($arr)]);
        if (!$up) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，请稍后重试！', 'error_code' => 4]);
        }
        return $this->json_rewrite(['status' => 'success', 'msg' => '设置成功！', 'error_code' => 0]);
    }

    /**
     * 我的奖品
     */
    public function get_user_luck()
    {
        $data = input('param.');
        $list = Db::name('user_raffle_records')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('win_type<>0')
            ->order('join_time desc')
            ->page($data['page'], 20)
            ->select();
        foreach ($list as $k => $v) {
            $list[$k]['join_time'] = date('m-d H:i', $v['join_time']);
            $list[$k]['see_time_format'] = date('Y-m-d H:i', $v['see_time']);
            $list[$k]['address_format'] = json_decode($v['address_details'], true);
        }
        return $this->json_rewrite(['status' => 'success', 'msg' => '成功！', 'error_code' => 0, 'info' => $list]);
    }

    /**
     * 抽奖算法
     * @param array $proArr 奖品等级奖品权重数组
     * @return [int]         中奖奖品等级
     */
    public function get_rand($proArr = array())
    {
        if (empty($proArr)) die;
        $rid = '';

        // 概率数组的总权重
        $proSum = array_sum($proArr);

        // 概率数组循环
        foreach ($proArr as $k => $proCur) {
            // 从 1 到概率总数中任意取值
            $randNum = mt_rand(1, $proSum);
            // 判断随机数是否在概率权重中
            if ($randNum <= $proCur) {
                // 取出奖品 id
                $rid = $k;
                break;
            } else {
                // 如果随机数不在概率权限中，则不断缩小总权重，直到从奖品数组中取出一个奖品
                $proSum -= $proCur;
            }
        }

        unset($proArr);
        return $rid;
    }
}