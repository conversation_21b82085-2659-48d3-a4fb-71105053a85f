<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:84:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/index/index.html";i:1750239759;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755608968;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>
    /* Modern Dashboard Styles */
    :root {
        --body-bg: #f8f9fa;
        --widget-bg: #ffffff;
        --text-color: #495057;
        --heading-color: #343a40;
        --border-color: #e9ecef;
        --shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        --border-radius: 8px;
    }

    .modern-dashboard {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        background-color: var(--body-bg);
        padding: 24px;
    }

    .stat-cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 24px;
        margin-bottom: 24px;
    }

    .stat-card {
        background-color: var(--widget-bg);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        padding: 20px;
        display: flex;
        align-items: center;
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 12px rgba(0, 0, 0, 0.08);
    }

    .stat-card .icon-wrapper {
        flex-shrink: 0;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
    }

    .stat-card .icon-wrapper i {
        font-size: 24px;
        color: #fff;
    }
    
    .stat-card .details .number {
        font-size: 28px;
        font-weight: 600;
        color: var(--heading-color);
        line-height: 1.2;
    }
    
    .stat-card .details .number span {
        font-size: 14px;
        font-weight: 400;
        color: var(--text-color);
    }

    .stat-card .details .desc {
        font-size: 14px;
        color: var(--text-color);
        margin-top: 4px;
    }
    
    /* Icon Colors */
    .icon-users { background-color: #3B82F6; }
    .icon-circle { background-color: #10B981; }
    .icon-chart { background-color: #F59E0B; }
    .icon-comments { background-color: #8B5CF6; }
    .icon-cart { background-color: #EF4444; }
    .icon-rmb { background-color: #14B8A6; }
    .icon-gift { background-color: #EC4899; }
    .icon-stack { background-color: #6366F1; }

    .widgets-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 24px;
    }

    .widget {
        background-color: var(--widget-bg);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        overflow: hidden;
    }

    .widget-header {
        padding: 16px 24px;
        border-bottom: 1px solid var(--border-color);
    }

    .widget-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--heading-color);
        display: flex;
        align-items: center;
    }
    
    .widget-title i {
        margin-right: 10px;
        font-size: 20px;
    }
    
    .font-green { color: #10B981; }
    .font-red { color: #EF4444; }
    
    .widget-content {
        padding: 24px;
    }
    
    #tpl-echarts-A {
        width: 100%;
        height: 350px; /* Ensure chart has height */
    }
    
    .active-users-widget .summary {
        text-align: center;
        margin-bottom: 20px;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: var(--border-radius);
    }
    
    .active-users-widget .summary .title {
        font-size: 14px;
        color: var(--text-color);
    }
    
    .active-users-widget .summary .number {
        font-size: 24px;
        font-weight: 700;
        color: #F59E0B;
        margin-top: 5px;
    }
    
    .widget-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .widget-table th, .widget-table td {
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }
    
    .widget-table thead th {
        font-size: 13px;
        font-weight: 600;
        color: var(--text-color);
        background-color: #f8f9fa;
        text-transform: uppercase;
    }
    
    .widget-table tbody tr:last-child td {
        border-bottom: none;
    }

    .widget-table tbody tr:hover {
        background-color: #f8f9fa;
    }
    
    .widget-table .user-info {
        display: flex;
        align-items: center;
    }

    .widget-table .user-pic {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 12px;
        object-fit: cover;
    }
    
    .widget-table .user-name {
        font-weight: 500;
        color: var(--heading-color);
        text-decoration: none;
    }
    
    .widget-table .user-name:hover {
        text-decoration: underline;
    }
    
    .widget-table .percentage {
        font-weight: 700;
        color: #10B981;
    }

</style>

<div class="modern-dashboard">
    <!-- Stat Cards Section -->
    <div class="stat-cards-grid">
        <div class="stat-card">
            <div class="icon-wrapper icon-users"><i class="am-icon-users"></i></div>
            <div class="details">
                <div class="number"><?php echo $new_user_today; ?></div>
                <div class="desc">今日新增用户</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-circle"><i class="am-icon-circle-o"></i></div>
            <div class="details">
                <div class="number"><?php echo $new_toryon_today; ?></div>
                <div class="desc">今日申请创建圈子</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-chart"><i class="am-icon-bar-chart-o"></i></div>
            <div class="details">
                <div class="number"><?php echo $new_paper_today; ?></div>
                <div class="desc">今日发帖数量</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-comments"><i class="am-icon-comments-o"></i></div>
            <div class="details">
                <div class="number"><?php echo $new_punch_today; ?></div>
                <div class="desc">今日签到人数</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-cart"><i class="am-icon-shopping-cart"></i></div>
            <div class="details">
                <div class="number"><?php echo $new_sorder_today; ?></div>
                <div class="desc">今日商品订单</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-rmb"><i class="am-icon-rmb"></i></div>
            <div class="details">
                <div class="number"><?php echo $new_userial_today; ?></div>
                <div class="desc">今日用户充值收益</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-gift"><i class="am-icon-gift"></i></div>
            <div class="details">
                <div class="number"><?php echo $new_subsicont_today; ?> <span>(<?php echo $defaultNavigate['currency']; ?>)</span></div>
                <div class="desc">今日送礼总金额</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-stack"><i class="am-icon-stack-overflow"></i></div>
            <div class="details">
                <div class="number"><?php echo $new_subsidy_today; ?> <span>(<?php echo $defaultNavigate['currency']; ?>)</span></div>
                <div class="desc">今日平台总扣税额</div>
            </div>
        </div>
    </div>

    <!-- Widgets Section -->
    <div class="widgets-grid">
        <!-- Monthly Stats Chart -->
        <div class="widget">
            <div class="widget-header">
                <div class="widget-title font-green">
                    <i class="am-icon-line-chart"></i>
                    <span>本月用户统计</span>
                </div>
            </div>
            <div class="widget-content">
                <div class="tpl-echarts" id="tpl-echarts-A"></div>
            </div>
        </div>

        <!-- Active Users Table -->
        <div class="widget active-users-widget">
            <div class="widget-header">
                <div class="widget-title font-red">
                    <i class="am-icon-bar-chart"></i>
                    <span>本周发帖活跃用户</span>
                </div>
            </div>
            <div class="widget-content">
                <div class="summary">
                    <div class="title">共计 (单位：条)</div>
                    <div class="number"><?php echo $large_total; ?></div>
                </div>
                <table class="widget-table">
                    <thead>
                        <tr>
                            <th>用户昵称</th>
                            <th style="text-align: center;">发帖次数</th>
                            <th style="text-align: center;">统计百分比</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(is_array($large_user) || $large_user instanceof \think\Collection || $large_user instanceof \think\Paginator): $i = 0; $__LIST__ = $large_user;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <tr>
                            <td>
                                <a class="user-info" href="<?php echo url('essay/index'); ?>&egon=0&hazy_name=<?php echo $vo['user_wechat_open_id']; ?>&page=1" target="_blank" title="<?php echo emoji_decode($vo['user_nick_name']); ?>">
                                    <img src="<?php echo $vo['user_head_sculpture']; ?>" class="user-pic" alt="avatar">
                                    <span class="user-name"><?php echo subtext(emoji_decode($vo['user_nick_name']),10); ?></span>
                                </a>
                            </td>
                            <td style="text-align: center;"><?php echo $vo['hasty']; ?></td>
                            <td style="text-align: center;" class="percentage"><?php echo $vo['percentage']; ?>%</td>
                        </tr>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<script src="./assets/js/base-enhanced.js"></script>
<script>
var baseConfig = {
    urls: {
        proxyBase: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        proxyResource: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        repairMissing: "<?php echo url('index/repairMissing'); ?>",
        purgeCache: "<?php echo url('index/purgeCache'); ?>",
        ordinary: "<?php echo url('ordinary'); ?>",
        receipt: "<?php echo url('receipt'); ?>"
    },
    menuActive: '<?php echo $menuActive; ?>',
    menuList: '<?php echo $menuList; ?>',
    much_role: '<?php echo $much_role; ?>',
    notice: <?php echo $notice; ?>,
    vacant: <?php echo $vacant; ?>
};
BaseEnhanced.init(baseConfig);
window.retakeCache = BaseEnhanced.retakeCache;
window.getOriginalSrc = BaseEnhanced.getOriginalSrc;
</script>
<?php if($much_role=='founder'): ?>
<script>
window.updateFix = function() {
    layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
        btn: ['确定', '取消'],
        title: '提示'
    }, function() {
        // 执行修复操作
        performDatabaseRepair();
    }, function(index) {
        // 取消操作
        layer.close(index);
    });
};

function performDatabaseRepair() {
    var loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });
    $.post("<?php echo url('index/repairMissing'); ?>", function(data) {
        layer.close(loadingIndex);
        if (data !== false) {
            layer.msg('数据库缺失字段修复完成', {
                icon: 1,
                time: 1500
            }, function() {
                location.reload();
            });
        } else {
            layer.msg('修复失败，请稍后重试', {
                icon: 2,
                time: 2000
            }, function() {
                location.reload();
            });
        }
    }).error(function() {
        layer.close(loadingIndex);
        layer.msg('网络错误，修复失败', {
            icon: 2,
            time: 2000
        });
    });
}
</script>
<?php endif; ?>

</body>
</html>