{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-cog"></span> URL 还原功能测试
        </div>
    </div>
    <div class="tpl-block">
        <h3>测试说明</h3>
        <p>此页面用于测试图片代理 URL 的自动还原功能。</p>
        
        <h3>测试图片</h3>
        <div style="margin: 20px 0;">
            <!-- 这里放一些 HTTP 图片用于测试 -->
            <img id="testImg1" src="http://example.com/test1.jpg" style="width: 100px; height: 100px; border: 1px solid #ccc; margin: 10px;" alt="测试图片1">
            <img id="testImg2" src="http://example.com/test2.jpg" style="width: 100px; height: 100px; border: 1px solid #ccc; margin: 10px;" alt="测试图片2">
        </div>
        
        <h3>测试表单</h3>
        <form id="testForm">
            <div class="am-form-group">
                <label>图片1 URL:</label>
                <input type="text" id="img1Url" class="am-form-field" readonly>
            </div>
            <div class="am-form-group">
                <label>图片2 URL:</label>
                <input type="text" id="img2Url" class="am-form-field" readonly>
            </div>
            <div class="am-form-group">
                <button type="button" onclick="updateUrls()" class="am-btn am-btn-primary">获取当前图片URL</button>
                <button type="button" onclick="testSave()" class="am-btn am-btn-success">测试保存</button>
            </div>
        </form>
        
        <h3>测试结果</h3>
        <div id="testResult" style="background: #f5f5f5; padding: 10px; margin: 10px 0; min-height: 100px;">
            <p>点击"测试保存"按钮查看结果...</p>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
function updateUrls() {
    // 获取图片的当前 src
    var img1 = document.getElementById('testImg1');
    var img2 = document.getElementById('testImg2');
    
    document.getElementById('img1Url').value = img1.src;
    document.getElementById('img2Url').value = img2.src;
}

function testSave() {
    var img1 = document.getElementById('testImg1');
    var img2 = document.getElementById('testImg2');
    
    // 模拟保存数据
    var testData = {
        img1_src: img1.src,
        img2_src: img2.src,
        img1_attr: $(img1).attr('src'),
        img2_attr: $(img2).attr('src'),
        // 测试使用全局函数获取原始URL
        img1_original: window.getOriginalSrc ? window.getOriginalSrc(img1) : 'function not available',
        img2_original: window.getOriginalSrc ? window.getOriginalSrc(img2) : 'function not available'
    };
    
    // 发送 AJAX 请求测试
    $.post("{:url('index/testUrlRestore')}", testData, function(response) {
        document.getElementById('testResult').innerHTML = 
            '<h4>测试结果：</h4>' +
            '<pre>' + JSON.stringify(response, null, 2) + '</pre>';
    }, 'json').fail(function(xhr, status, error) {
        document.getElementById('testResult').innerHTML = 
            '<h4>请求失败：</h4>' +
            '<p>Status: ' + status + '</p>' +
            '<p>Error: ' + error + '</p>';
    });
}

// 页面加载完成后自动更新URL显示
$(document).ready(function() {
    setTimeout(function() {
        updateUrls();
    }, 2000); // 等待2秒让图片代理逻辑执行完成
});
</script>
{/block}
