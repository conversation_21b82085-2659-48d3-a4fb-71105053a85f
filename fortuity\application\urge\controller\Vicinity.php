<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\common\Playful;
use app\common\Remotely;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

# vicinity
class Vicinity extends Base
{
    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, array(), 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    public function located_list()
    {
        if (!Remotely::isUnLockProperty(base64_decode('6ZmE6L+R5ZyI5a2Q'))) {
            abort(404);
        }
        // 获取当前URL路径
        $url = $this->defaultQuery();

        // 获取搜索参数
        $realmName = trim(input('get.toryName', ''));

        // 构建查询条件
        $when = array();
        if ($realmName !== '') {
            // 先查找匹配的圈子ID
            $toryIds = Db::name('territory')
                ->where('realm_name', 'like', "%{$realmName}%")
                ->where('much_id', $this->much_id)
                ->where('is_del', 0)
                ->column('id');
            if (!empty($toryIds)) {
                $when[] = function ($query) use ($toryIds) {
                    $query->whereIn('tory_id', $toryIds);
                };
            } else {
                // 如果没有匹配的圈子，设置一个不可能的条件
                $when[] = function ($query) {
                    $query->where('tory_id', -1);
                };
            }
        }

        // 执行数据库查询和分页
        $list = Db::name('territory_nearby')
            ->where($when)
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'toryName' => $realmName]])
            ->each(function ($item) {
                $item['realm_name'] = Db::name('territory')->where('id', $item['tory_id'])->where('much_id', $this->much_id)->where('is_del', 0)->value('realm_name');
                return $item;
            });

        // 分配数据给视图
        $this->assign('list', $list);
        $this->assign('tory_name', $realmName);

        // 获取当前页码
        $page = request()->get('page', 1);
        $this->assign('page', $page);

        return $this->fetch();
    }

    /*
     * 新增圈子位置
     */
    public function new_located()
    {
        if (!Remotely::isUnLockProperty(base64_decode('6ZmE6L+R5ZyI5a2Q'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data['tory_id'] = intval(input('post.tory_id'));
            $data['longitude'] = trim(input('post.longitude'));
            $data['latitude'] = trim(input('post.latitude'));
            $data['create_time'] = time();
            $data['much_id'] = $this->much_id;

            // 检查圈子编号是否已存在
            $exists = Db::name('territory_nearby')
                ->where('tory_id', $data['tory_id'])
                ->where('much_id', $this->much_id)
                ->find();
            if ($exists) {
                return json(['code' => 0, 'msg' => '该圈子位置已存在']);
            }

            Db::startTrans();
            try {
                Db::name('territory_nearby')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            // 获取已经添加过位置的圈子ID
            $existingToryIds = Db::name('territory_nearby')
                ->where('much_id', $this->much_id)
                ->column('tory_id');

            // 获取圈子列表供选择，排除已经添加过位置的圈子
            $query = Db::name('territory')
                ->where('is_del', 0)
                ->where('much_id', $this->much_id)
                ->field('id,realm_name');

            // 如果有已存在的圈子，排除它们
            if (!empty($existingToryIds)) {
                $query->whereNotIn('id', $existingToryIds);
            }

            $toryList = $query->select();
            $toryListData = base64_encode(rawurlencode(json_encode($toryList)));
            $this->assign('toryListData', $toryListData);
            return $this->fetch();
        }
    }

    /*
     * 编辑圈子位置
     */
    public function edit_located()
    {
        if (!Remotely::isUnLockProperty(base64_decode('6ZmE6L+R5ZyI5a2Q'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $data['longitude'] = trim(input('post.longitude'));
            $data['latitude'] = trim(input('post.latitude'));

            // 验证经纬度格式
            if (!is_numeric($data['longitude']) || !is_numeric($data['latitude'])) {
                return json(['code' => 0, 'msg' => '经纬度必须是数字']);
            }

            // 验证经纬度范围
            if ($data['longitude'] < -180 || $data['longitude'] > 180) {
                return json(['code' => 0, 'msg' => '经度必须在-180到180之间']);
            }

            if ($data['latitude'] < -90 || $data['latitude'] > 90) {
                return json(['code' => 0, 'msg' => '纬度必须在-90到90之间']);
            }

            Db::startTrans();
            try {
                Db::name('territory_nearby')
                    ->where('id', $fid)
                    ->where('much_id', $this->much_id)
                    ->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '位置信息更新成功']);
        } else {
            $fid = request()->get('fid', '');
            if ($fid) {
                $info = Db::name('territory_nearby')
                    ->where('id', $fid)
                    ->where('much_id', $this->much_id)
                    ->find();
                if ($info) {
                    // 获取圈子列表供选择
                    $toryList = Db::name('territory')
                        ->where('is_del', 0)
                        ->where('much_id', $this->much_id)
                        ->field('id,realm_name')
                        ->select();
                    $toryListData = base64_encode(rawurlencode(json_encode($toryList)));
                    $this->assign('toryListData', $toryListData);
                    $this->assign('info', $info);
                    return $this->fetch();
                } else {
                    $this->redirect('vicinity/located_list');
                }
            } else {
                $this->redirect('vicinity/located_list');
            }
        }
    }

    /*
     * 删除圈子位置
     */
    public function del_located()
    {
        if (!Remotely::isUnLockProperty(base64_decode('6ZmE6L+R5ZyI5a2Q'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            Db::startTrans();
            try {
                Db::name('territory_nearby')
                    ->where('id', $fid)
                    ->where('much_id', $this->much_id)
                    ->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 附近圈子配置
     */
    public function located_config()
    {
        if (!Remotely::isUnLockProperty(base64_decode('6ZmE6L+R5ZyI5a2Q'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            // 获取并验证输入参数
            $distance_radius = input('post.distance_radius');
            $is_home_show = input('post.is_home_show');

            // 检查必填参数
            if ($distance_radius === null || $distance_radius === '') {
                return json(['code' => 0, 'msg' => '距离半径不能为空']);
            }

            if ($is_home_show === null || $is_home_show === '') {
                return json(['code' => 0, 'msg' => '首页展示设置不能为空']);
            }

            $data['distance_radius'] = floatval($distance_radius);
            $data['is_home_show'] = intval($is_home_show);

            // 验证距离半径
            if ($data['distance_radius'] <= 0) {
                return json(['code' => 0, 'msg' => '距离半径必须大于0']);
            }

            if ($data['distance_radius'] > 6371000) {
                return json(['code' => 0, 'msg' => '距离半径不能超过6371000米（地球半径约6371公里）']);
            }

            // 验证首页展示设置
            if (!in_array($data['is_home_show'], [0, 1])) {
                return json(['code' => 0, 'msg' => '首页展示设置参数错误']);
            }

            // 获取或创建配置，然后更新
            $config = $this->getLocationConfig();

            Db::startTrans();
            try {
                // 更新配置
                Db::name('territory_nearby_config')
                    ->where('much_id', $this->much_id)
                    ->update($data);

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }

            return json(['code' => 1, 'msg' => '配置保存成功']);
        } else {
            // 获取配置，如果不存在会自动创建
            $config = $this->getLocationConfig();

            $this->assign('config', $config);
            return $this->fetch();
        }
    }

    /*
     * 获取附近圈子配置（统一方法）
     * 如果没有配置则自动创建默认配置
     */
    private function getLocationConfig()
    {
        // 获取当前配置
        $config = Db::name('territory_nearby_config')
            ->where('much_id', $this->much_id)
            ->find();

        // 如果没有配置，创建默认配置
        if (!$config) {
            $defaultConfig = array(
                'distance_radius' => 1000.00,  // 默认1000米
                'is_home_show' => 1,            // 默认首页展示
                'much_id' => $this->much_id
            );

            try {
                Db::name('territory_nearby_config')->insert($defaultConfig);

                // 重新获取插入的配置
                $config = Db::name('territory_nearby_config')
                    ->where('much_id', $this->much_id)
                    ->find();
            } catch (\Exception $e) {
                // 插入失败时返回默认值（不包含ID）
                $config = array(
                    'distance_radius' => 1000.00,
                    'is_home_show' => 1
                );
            }
        }

        return $config;
    }
}
