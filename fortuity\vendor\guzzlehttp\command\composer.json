{"name": "guzzlehttp/command", "description": "Provides the foundation for building command-based web service clients", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}], "require": {"php": ">=5.5.0", "guzzlehttp/guzzle": "^6.2", "guzzlehttp/promises": "~1.3", "guzzlehttp/psr7": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "autoload": {"psr-4": {"GuzzleHttp\\Command\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "0.9-dev"}}}