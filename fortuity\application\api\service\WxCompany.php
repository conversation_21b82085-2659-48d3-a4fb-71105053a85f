<?php

namespace app\api\service;

use RuntimeException;
use think\Controller;
use think\Db;
use WechatPay\GuzzleMiddleware\Util\AesUtil;
use WePayV3\Transfers;

class WxCompany extends Controller
{
    public $payurl = 'https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers';
    protected $mch_appid;
    protected $mchid;
    protected $api_cert;
    protected $api_key;
    protected $api_p12;
    protected $nonce_str = '';
    protected $sign = '';
    protected $partner_trade_no = '';
    protected $check_name = 'NO_CHECK';
    protected $amount = 0;
    protected $desc = '';
    protected $spbill_create_ip = '';
    protected $signKey = '';
    protected $config = '';
    protected $bandData = '';

    /**
     * mch_appid：绑定支付的APPID（必须配置，开户邮件中可查看）
     *
     * mchid：商户号（必须配置，开户邮件中可查看）
     *
     * signKey：商户支付密钥，参考开户邮件设置（必须配置，登录商户平台自行设置）
     * 设置地址：https://pay.weixin.qq.com/index.php/account/api_cert
     *
     */
    /**
     * @param $openid 微信用户openid
     * @param $amount 钱
     * @param $desc 描述
     * @param $ip IP地址
     * @return array|mixed
     */
    public function companyToPocket($openid, $amount, $order_sn, $desc, $ip, $much_id)
    {
        $getConfig = cache('fatal_' . $much_id);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $much_id)->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $much_id, $getConfig);
            }
        }
        if ($getConfig['version_type'] == 1) { //V3
            $item = ['order_sn'=>$order_sn,'much_id' => $much_id, 'batch_name' => '提现', 'batch_remark' => "提现金额{$amount}元", 'total_amount' => $amount, 'openid' => $openid];
            return $this->WechatPay($item, $getConfig);
        }
        $createCatalogPath = EXTEND_PATH . 'Wxpay' . DS . 'Cert';
        $createPath = iconv('UTF-8', 'GBK', $createCatalogPath);
        if (file_exists($createPath) == false) {
            @mkdir($createPath, 0777, true);
        }
        $apiclientCert = EXTEND_PATH . 'Wxpay' . DS . 'Cert' . DS . 'apiclient_cert_' . $much_id . '.pem';
        $apiclientKey = EXTEND_PATH . 'Wxpay' . DS . 'Cert' . DS . 'apiclient_key_' . $much_id . '.pem';
        @file_put_contents($apiclientCert, $getConfig['apiclient_cert']);      //生成证书
        @file_put_contents($apiclientKey, $getConfig['apiclient_key']);


        $this->mch_appid = $getConfig['app_id'];//公众号appid
        $this->mchid = $getConfig['app_mchid']; //商户号
        $this->signKey = $getConfig['app_key'];
        //=======【证书路径设置】=====================================
        /**
         * TODO：设置商户证书路径
         * 证书路径,注意应该填写绝对路径（仅退款、撤销订单时需要，可登录商户平台下载，
         * API证书下载地址：https://pay.weixin.qq.com/index.php/account/api_cert，下载之前需要安装商户操作证书）
         * @var path
         */
        $this->api_cert = $apiclientCert;
        $this->api_key = $apiclientKey;
        $this->nonce_str = md5(time());
        $this->spbill_create_ip = $this->get_client_ip();
        $this->config = array(
            "mch_appid" => $this->mch_appid,
            'mchid' => $this->mchid,
            'api_cert' => $this->api_cert,
            'api_key' => $this->api_key,
            'api_p12' => $this->api_p12,
        );

        $this->config = array(
            "mch_appid" => $this->mch_appid,
            'mchid' => $this->mchid,
            'api_cert' => $this->api_cert,
            'api_key' => $this->api_key,
        );

        $webData = array(
            "mch_appid" => $this->mch_appid,
            'mchid' => $this->mchid,
            'nonce_str' => $this->nonce_str,
            'partner_trade_no' => empty($order_sn) ? $this->getOrderSn('R') : $order_sn,
            'openid' => $openid,
            'amount' => $amount * 100,
            'desc' => $desc,
            'check_name' => $this->check_name,
            'spbill_create_ip' => empty($ip) ? $this->spbill_create_ip : $ip,
        );
        $webData['sign'] = $this->createSine($webData);
        return $this->goWxServer($webData);

    }

    //微信提现接口
    public function WechatPay($data, $getConfig)
    {
        try {
            // 1. 手动加载入口文件
            include EXTEND_PATH . "WePay/include.php";

            // 2. 金额验证
            if (bccomp($data['total_amount'], 0.3) == -1) {
                return ['status' => 1, 'code' => '500', 'msg' => '金额不能小于0.3'];
            }

            // 3. 创建证书目录
//            $createCatalogPath = EXTEND_PATH . 'Wxpay' . DS . 'Cert';
//            $createPath = iconv('UTF-8', 'GBK', $createCatalogPath);
//            if (file_exists($createPath) == false) {
//                @mkdir($createPath, 0777, true);
//            }

            // 4. 准备证书文件（恢复文件写入以确保证书格式正确）
//            $apiclientKey = EXTEND_PATH . 'Wxpay' . DS . 'Cert' . DS . 'apiclient_key_' . $data['much_id'] . '.pem';
//            $apiclientCert = EXTEND_PATH . 'Wxpay' . DS . 'Cert' . DS . 'apiclient_cert_' . $data['much_id'] . '.pem';
//            @file_put_contents($apiclientKey, $getConfig['apiclient_key']);
//            @file_put_contents($apiclientCert, $getConfig['apiclient_cert']);

            // 5. 准备公众号配置参数
            $config = [
                // 可选，公众号APPID
                'appid' => $getConfig['app_id'],
                // 必填，微信商户编号ID
                'mch_id' => $getConfig['app_mchid'],
                // 必填，微信商户V3接口密钥
                'mch_v3_key' => $getConfig['app_key_v3'],
                // 可选，微信商户证书序列号，可从公钥中提取
                'cert_serial' => $getConfig['certificate_serial_number'],
                // 必填，微信商户证书公钥，支持证书内容或文件路径
                'cert_public' => $getConfig['apiclient_cert'],
                // 必填，微信商户证书私钥，支持证书内容或文件路径
                'cert_private' => $getConfig['apiclient_key'],
                // 可选，运行时的文件缓存路径
                'cache_path' => ''
            ];

            $pay = Transfers::instance($config);

            //$order_number = $this->getOrderSn();

            $result = $pay->batchs([
                'out_bill_no' => $data['order_sn'],
//                'batch_name' => $data['batch_name'],
//                'batch_remark' => $data['batch_remark'],
                'transfer_amount' => $data['total_amount'] * 100,
                'transfer_remark' => $data['batch_name'],
                'openid' => $data['openid'],
                'notify_url'=>$getConfig['pay_react'],
                'transfer_scene_id' => '1005',
                'transfer_scene_report_infos' => [       // 固定传两组
                    [
                        'info_type'    => '岗位类型',   // 固定值
                        'info_content' => '外卖员', // 示例值
                    ],
                    [
                        'info_type'    => '报酬说明',   // 固定值
                        'info_content' => '7月份配送费', // 示例值
                    ],
                ],
            ]);
            if(isset($result['code'])){
                return ['status' => 1, 'code' => '500', 'msg' => $result['message'], 'out_batch_no' => ''];
            }else{
                $result['app_mchid']=$getConfig['app_mchid'];
                $result['app_id']= $getConfig['app_id'];
                $result['openid'] = $data['openid'];
                //如果返回成功 更新wxpay_transfer_num
                Db::name('user_withdraw_money_order')->where('much_id',$data['much_id'])->where('mch_order', $data['order_sn'])->update(['package_info'=>$result['package_info'],'wxpay_transfer_num'=>$result['transfer_bill_no']]);
                return ['status' => 0, 'code' => '999', 'info'=>$result];
            }
//
//            if ($result['code'] == 'OK') {
//                return ['status' => 0, 'code' => '200', 'msg' => 'success', 'out_batch_no' => isset($result['out_batch_no']) ? $result['out_batch_no'] : $order_number];
//            } else {
//                return ['status' => 1, 'code' => '500', 'msg' => $result['message'], 'out_batch_no' => ''];
//            }
            // 返回成功结果


        } catch (\Exception $exception) {
            // 出错啦，处理下吧
            return ['status' => 1, 'code' => '500', 'msg' => $exception->getMessage(), 'out_batch_no' => ''];
        }
    }

    //生成v3 Authorization
    protected function createAuthorization($method, $merchant_id, $mch_private_key, $serial_no, $apis)
    {
        $url = "https://api.mch.weixin.qq.com/v3/certificates";
        if (!in_array('sha256WithRSAEncryption', openssl_get_md_methods(true))) {
            throw new RuntimeException("当前PHP环境不支持SHA256withRSA");
        }
        $url_parts = parse_url($url);
        // PHP 8.2兼容性修复：使用{$var}替代${var}语法
        $canonical_url = ($url_parts['path'] . (!empty($url_parts['query']) ? "?{$url_parts['query']}" : ""));
        //当前时间戳
        $timestamp = time();
        //随机字符串
        $nonce = $this->createNoncestr();
        $message = $method . "\n" .
            $canonical_url . "\n" .
            $timestamp . "\n" .
            $nonce . "\n" .
            "\n";
        //生成签名
        openssl_sign($message, $raw_sign, openssl_get_privatekey(file_get_contents($mch_private_key)), 'sha256WithRSAEncryption');
        $sign = base64_encode($raw_sign);
        //Authorization 类型
        $schema = 'WECHATPAY2-SHA256-RSA2048';
        //生成token
        $token = sprintf('mchid="%s",serial_no="%s",nonce_str="%s",timestamp="%d",signature="%s"', $merchant_id, $serial_no, $nonce, $timestamp, $sign);
        $header = [
            'Accept' => 'application/json',
            'User-Agent' => 'https://zh.wikipedia.org/wiki/User_agent',
            'Authorization' => $schema . ' ' . $token
        ];
        $client = new \GuzzleHttp\Client();
        $resp = $client->request('GET', $url, ['headers' => $header]);
        $data = json_decode($resp->getBody(), true);
        $obj = new AesUtil($apis);//商户后台->账户中心->API安全->APIv3密钥
        return $obj->decryptToString($data['data'][0]['encrypt_certificate']['associated_data'], $data['data'][0]['encrypt_certificate']['nonce'], $data['data'][0]['encrypt_certificate']['ciphertext']) . PHP_EOL;

    }

    /**
     *  作用：产生随机字符串，不长于32位
     */
    public function createNoncestr($length = 32)
    {
        $chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

    /**
     * @param $bankData array('enc_bank_no'=>'','enc_true_name'=>'','bank_code'=>'')
     * @param $enc_bank_no_name 银行卡号 或实名
     * @param $enc_true_name_code 实名或银行编号
     */
    protected function setBankData($bankData, $enc_bank_no_name)
    {
        if (!is_array($bankData) && $enc_bank_no_name == '') {
            return array('state' => 0, 'msg' => "参数错误");
        }
        if (is_array($bankData)) {
            $this->bandData = $bankData;
        }
        if (is_string($bankData) && is_int($bankData) && !is_array($bankData) && $enc_bank_no_name != '') {
            $bankData = array(
                'enc_bank_no' => $bankData,
                'enc_true_name' => $enc_bank_no_name,
            );
            $bankData['bank_code'] = $this->getBankCode($bankData['enc_true_name']);
            $this->bandData = $bankData;
        }

    }

    /**
     * @param $amount
     * @param $desc
     * @return array|mixed
     */
    protected function companyPayToBank($amount, $desc)
    {

        $webData = array(
            "mch_appid" => $this->mch_appid,
            'mchid' => $this->mchid,
            'nonce_str' => $this->nonce_str,
            'partner_trade_no' => $this->getOrderSn('R'),
            'amount' => $amount * 100,
            'desc' => $desc,
        );
        $webData = array_merge($webData, $this->bandData);
        $webData['sign'] = $this->createSine($webData);
        return $this->goWxServer($webData);
    }

    /**
     *
     *
     * 1056
     */
    private function getBankCode($bank_name)
    {
        switch ($bank_name) {
            case  "工商银行":
                return 1002;
                break;
            case  "农业银行":
                return 1005;
                break;
            case  "中国银行":
                return 1026;
                break;
            case  "建设银行":
                return 1003;
                break;
            case  "招商银行":
                return 1001;
                break;
            case  "邮储银行":
                return 1066;
                break;
            case  "交通银行":
                return 1020;
                break;
            case  "浦发银行":
                return 1004;
                break;
            case  "民生银行":
                return 1006;
                break;
            case  "兴业银行":
                return 1009;
                break;
            case  "平安银行":
                return 1010;
                break;
            case  "中信银行":
                return 1021;
                break;
            case  "华夏银行":
                return 1025;
                break;
            case  "广发银行":
                return 1027;
                break;
            case  "光大银行":
                return 1022;
                break;
            case  "北京银行":
                return 1032;
                break;
            case  "宁波银行":
                return 1056;
                break;
        }
    }

    public function goWxServer($webData)
    {
        $this->config = array(
            "mch_appid" => $this->mch_appid,
            'mchid' => $this->mchid,
            'api_cert' => $this->api_cert,
            'api_key' => $this->api_key,
        );
        $wGet = $this->array2xml($webData);
        $res = $this->http_post($this->payurl, $wGet, $this->config);
        if (!$res) {
            return array('status' => 1, 'msg' => "Can't connect the server");
        }
        libxml_disable_entity_loader(true);
        $content = json_decode(json_encode(simplexml_load_string($res, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        if ($content['return_code'] == 'FAIL') {
            return array('status' => 1, 'msg' => $content['return_msg']);
        }
        if ($content['result_code'] == 'FAIL') {
            return array('status' => 1, 'msg' => $content['err_code'] . ':' . $content['err_code_des']);
        }
        return $content;
    }

    public function createSine($data)
    {
        $tArr = array();
        foreach ($data as $k => $v) {
            $tArr[] = $k . "=" . $v;
        }
        sort($tArr);
        $sign = implode("&", $tArr);
        $sign .= "&key=" . $this->signKey;
        return strtoupper(md5($sign));
    }

    private function array2xml($arr, $level = 1)
    {
        $s = $level == 1 ? "<xml>" : '';
        foreach ($arr as $tagname => $value) {
            if (is_numeric($tagname)) {
                $tagname = $value['TagName'];
                unset($value['TagName']);
            }
            if (!is_array($value)) {
                $s .= "<{$tagname}>" . (!is_numeric($value) ? '<![CDATA[' : '') . $value . (!is_numeric($value) ? ']]>' : '') . "</{$tagname}>";
            } else {
                $s .= "<{$tagname}>" . $this->array2xml($value, $level + 1) . "</{$tagname}>";
            }
        }
        $s = preg_replace("/([\x01-\x08\x0b-\x0c\x0e-\x1f])+/", ' ', $s);
        return $level == 1 ? $s . "</xml>" : $s;

    }

    /**
     * 获取客户端IP地址
     * @param integer $type 返回类型 0 返回IP地址 1 返回IPV4地址数字
     * @return mixed
     */
    private function get_client_ip($type = 0)
    {
        $type = $type ? 1 : 0;
        static $ip = NULL;
        if ($ip !== NULL) return $ip[$type];
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            $pos = array_search('unknown', $arr);
            if (false !== $pos) unset($arr[$pos]);
            $ip = trim($arr[0]);
        } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        // IP地址合法验证
        $long = sprintf("%u", ip2long($ip));
        $ip = $long ? array($ip, $long) : array('0.0.0.0', 0);
        return $ip[$type];
    }

    /**
     * @param string $str
     * @return string
     * 唯一码
     */
    private function getOrderSn($str = '')
    {
        $order_id_main = date('YmdHis') . rand(100, 999);
        $order_id_len = strlen($order_id_main);
        $order_id_sum = 0;
        for ($i = 0; $i < $order_id_len; $i++) {
            $order_id_sum += (int)(substr($order_id_main, $i, 1));
        }
        $order_sn = $order_id_main . str_pad((100 - $order_id_sum % 100) % 100, 2, '0', STR_PAD_LEFT);
        return $str . $order_sn;
    }

    /**
     * @param $url
     * @param $param
     * @param $config
     * @return bool|mixed
     * post 请求；
     */
    private function http_post($url, $param, $config)
    {
        $oCurl = curl_init();
        if (defined('CURLOPT_IPRESOLVE') && defined('CURL_IPRESOLVE_V4')) {
            curl_setopt($oCurl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
        }
        if (stripos($url, "https://") !== FALSE) {
            curl_setopt($oCurl, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($oCurl, CURLOPT_SSL_VERIFYHOST, FALSE);
        }
        if (is_string($param)) {
            $strPOST = $param;
        } else {
            $aPOST = array();
            foreach ($param as $key => $val) {
                $aPOST[] = $key . "=" . urlencode($val);
            }
            $strPOST = join("&", $aPOST);
        }
        curl_setopt($oCurl, CURLOPT_URL, $url);
        curl_setopt($oCurl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($oCurl, CURLOPT_POST, true);
        curl_setopt($oCurl, CURLOPT_POSTFIELDS, $strPOST);
        if ($config) {
            curl_setopt($oCurl, CURLOPT_SSLCERT, $config['api_cert']);
            curl_setopt($oCurl, CURLOPT_SSLKEY, $config['api_key']);
            curl_setopt($oCurl, CURLOPT_CAINFO, $config['rootca']);
        }
        $sContent = curl_exec($oCurl);
        $aStatus = curl_getinfo($oCurl);
        curl_close($oCurl);
        //file_put_contents("lloogg.txt",$aStatus["http_code"].'---'.curl_error($oCurl));
        if (intval($aStatus["http_code"]) == 200) {
            return $sContent;
        } else {
            return false;
        }
    }

}