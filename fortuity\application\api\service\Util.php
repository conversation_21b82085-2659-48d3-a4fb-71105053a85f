<?php

namespace app\api\service;

use app\common\Gyration;
use think\Db;

class Util
{
    /**
     * 获取当前用户认证列表
     */
    public function get_att_list($uid, $much_id)
    {
        $info = Db::name('user_attest')
            ->where('much_id', $much_id)
            ->where('user_id', $uid)
            ->where('adopt_status', 1)
            ->field('ut_inject,at_id')
            ->select();
        if (empty($info)) {
            return '';
        } else {
            foreach ($info as $k => $v) {
                $attest = Db::name('attest')
                    ->where('id', $v['at_id'])
                    ->where('much_id', $much_id)
                    ->where('status', 1)
                    ->where('is_del', 0)
                    ->field('id,at_name,at_icon')
                    ->find();
                $info[$k]['at_icon'] = $attest['at_icon'];
                $info[$k]['at_name'] = $attest['at_name'];
            }

            return ['info' => $info];
        }
    }

    /**
     * 获取当前用户是否认证
     */
    public function get_att_info($uid, $much_id)
    {
        $info = Db::name('user_attest')
            ->where('much_id', $much_id)
            ->where('user_id', $uid)
            ->where('adopt_status', 1)
            ->field('ut_inject,at_id')
            ->find();
        if (empty($info)) {
            return ['info' => '', 'attest' => ''];
        } else {
            $attest = Db::name('attest')
                ->where('id', $info['at_id'])
                ->where('much_id', $much_id)
                ->where('status', 1)
                ->where('is_del', 0)
                ->field('id,at_name,at_icon')
                ->find();
            if (empty($attest)) {
                return '';
            } else {
                return ['info' => $info, 'attest' => $attest];
            }
        }
    }

    /**
     * 判断是圈主还是管理员
     */
    public function check_qq($open_id, $tory_id)
    {
        $this_qq = 'no';
        $learned = Db::name('territory_learned')->where('tory_id', $tory_id)->find();

        $json_da = json_decode($learned['bulord'], true);
        // PHP 8.4兼容性修复：确保 $json_da 是数组
        if (is_array($json_da)) {
            foreach ($json_da as $k => $v) {
                if ($v == $open_id) {
                    $this_qq = 'da';
                }
            }
        }
        $json_xiao = json_decode($learned['sulord'], true);
        // PHP 8.4兼容性修复：确保 $json_xiao 是数组
        if (is_array($json_xiao)) {
            foreach ($json_xiao as $k => $v) {
                if ($v == $open_id) {
                    $this_qq = 'xiao';
                }
            }
        }
        return $this_qq;
    }

    /**
     * openID获取用户信息
     */
    public function get_openId_user($open_id, $much_id)
    {
        if (empty($open_id)) {
            return '';
        }
        $user_info = Db::name('user')->where('much_id', $much_id)->where('user_wechat_open_id', $open_id)->find();
        $user_info['user_nick_name'] = emoji_decode($user_info['user_nick_name']);
        $user_info['autograph'] = emoji_decode($user_info['autograph']);
        return $user_info;
    }

    /**
     * 检查是否是会员
     */
    public function get_user_vip($uid)
    {
        $user_info = Db::name('user')->where('id', $uid)->find();
        if ($user_info['vip_end_time'] > time()) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 回复是那个圈子的
     */
    public function get_user_applaud($id)
    {
        $info = Db::name('paper_reply')->where('id', $id)->find();
        return $this->get_page_user($info['paper_id']);
    }

    /**
     * 获取帖子是那个会员发的
     */
    public function get_page_user($p_id)
    {
        $info = Db::name('paper')->where('id', $p_id)->find();
        return $info;
    }

    /**
     * API验证内容安全
     */
    public function check_msg($msg, $much_id, $openid)
    {
        //获取后台APPID
        $access_token = $this->getWchatAcctoken($much_id);
        $url = "https://api.weixin.qq.com/wxa/msg_sec_check?access_token=" . $access_token;
        $result = Gyration::_requestPost($url, json_encode(array('content' => $msg, 'version' => 2, 'scene' => 3, 'openid' => $openid), JSON_UNESCAPED_UNICODE));
        $error_json = json_decode($result, true);
        return $error_json;
    }

    /**
     * 发送模版消息
     */
    public function add_template($data)
    {
        $tmpl = new TmplService();
        return $tmpl->add_template($data);
    }

    /**
     * 发送站内信
     */
    public function add_user_smail($uid, $maring, $much_id, $type, $paper_id = 0)
    {
        $d['user_id'] = $uid;
        $d['maring'] = $maring;
        $d['much_id'] = $much_id;
        $d['clue_time'] = time();
        $d['skip_type'] = $type;
        $d['paper_id'] = $paper_id;
        $is = Db::name('user_smail')->insert($d);
        return $is;
    }

    /**
     * 获取是否关注了
     */
    public function get_user_trailing($uid, $tory_id)
    {
        $info = Db::name('user_trailing')->where('tory_id', $tory_id)->where('user_id', $uid)->find();
        if ($info) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 圈子发帖数量
     */
    public function get_territory_papo_count($id)
    {
        $count = Db::name('paper')->where('whether_delete', 0)->where('tory_id', $id)->where('study_status', 1)->count();
        return $count;
    }

    /**
     * 获取帖子详情（过滤）
     */
    public function get_paper($id)
    {
        $info = Db::name('paper')->where('id', $id)->find();
        $info['study_title'] = emoji_encode($info['study_title']);
        $info['study_content'] = emoji_encode($info['study_content']);
        $info['reject_reason'] = emoji_encode($info['reject_reason']);
        $info['whether_reason'] = emoji_encode($info['whether_reason']);
        $info['image_part'] = json_decode($info['image_part'], true);
        return $info;
    }

    /**
     * 获取回复详情（过滤）
     */
    public function paper_reply($id)
    {
        $info = Db::name('paper_reply')->alias('r')
            ->join('user u', 'u.id=r.user_id')
            ->where('r.id', $id)
            ->where('r.whether_delete', '0')
            ->field('r.*,u.gender,u.user_nick_name,u.user_head_sculpture,u.user_wechat_open_id')
            ->find();

        $info['user_nick_name'] = emoji_decode($info['user_nick_name']);
        $info['is_qq'] = $this->check_qq($info['user_wechat_open_id'], $this->get_user_applaud($info['id'])['tory_id']);
        $info['user_vip'] = $this->get_user_vip($info['user_id']);
        $info['reply_content'] = emoji_decode($info['reply_content']);
        $info['apter_time'] = formatTime($info['apter_time']);
        $info['image_part'] = json_decode($info['image_part']);
        $info['is_voice'] = false;
        $info['whether_reason'] = emoji_encode($info['whether_reason']);
        return $info;
    }

    /**
     * 获取邀请码的主人
     */
    public function get_user_invitation($yzm, $much_id)
    {
        $user_invitation_code = Db::name('user_invitation_code')->where('code', $yzm)->where('much_id', $much_id)->find();
        $user_info = Db::name('user')->where('id', $user_invitation_code['user_id'])->where('much_id', $much_id)->find();
        return $user_info;
    }

    /**
     * 审核后积分
     */
    public function set_user_red($data)
    {
        //用户详情
        $user_info = Db::name('user')->where('id', $data['uid'])->where('much_id', $data['much_id'])->find();
        //帖子详情
        $fa_info = $this->get_page_user($data['id']);
        //回复详情
        $hui_info = $this->paper_reply($data['reply_id']);

        //查询今日帖子回复数量
        //$paper_hui_count = Db::name('paper_reply')->where('user_id', $data['uid'])->where('much_id', $data['much_id'])->whereTime('apter_time', 'today')->count();
        //判断身份铭牌
        if ($hui_info['uccid'] != 0) {
            //判断当前是否是否过期
            $check_name_card = Db::name('user_camouflage_card')
                ->where('ccid', $hui_info['uccid'])
                ->where('user_id', $hui_info['user_id'])
                ->where('much_id', $data['much_id'])
                ->where('expired_time', '> time', date('Y-m-d H:i:s', time()))
                ->find();
            if (!empty($check_name_card)) {
                $ins['uccid'] = $hui_info['uccid'];
            } else {
                $ins['uccid'] = 0;
            }
        } else {
            $ins['uccid'] = 0;
        }
        // 启动事务
        Db::startTrans();
        try {
            //查询当前帖子是否是红包帖子
            $paper = Db::name('paper_red_packet')->where('paper_id', $data['id'])->where('much_id', $data['much_id'])->find();
            if (!empty($paper)) {

                //红包数量不等于0
                if ($paper['surplus_quantity'] != 0) {
                    //拼手气红包
                    if ($paper['red_type'] == 1) {
                        $red = new RedPaper();
                        if ($paper['initial_type'] == 0) {
                            $red->amount = $paper['surplus_conch'];
                        } else {
                            $red->amount = $paper['surplus_fraction'];
                        }

                        $red->num = $paper['surplus_quantity'];
                        $red->paper_min = 0.01;
                        $get_money = $red->handle()['items'][0];
                    }
                    //普通红包
                    if ($paper['red_type'] == 0) {
                        if ($paper['initial_type'] == 0) {
                            $get_money = $this->rob_red_avg($paper['surplus_conch'], $paper['surplus_quantity']);
                        } else {
                            $get_money = $this->rob_red_avg($paper['surplus_fraction'], $paper['surplus_quantity']);
                        }
                    }
                    //查询是否已经领过这个红包了
                    $check_paer_hui = Db::name('user_red_packet')->where('red_packet_id', $paper['id'])->where('user_id', $data['uid'])->where('much_id', $data['much_id'])->find();
                    //没领过
                    if (empty($check_paer_hui)) {
                        $packet['user_id'] = $data['uid'];
                        $packet['red_packet_id'] = $paper['id'];
                        $packet['obtain_fraction'] = $get_money;
                        $packet['obtain_time'] = time();
                        $packet['much_id'] = $data['much_id'];
                        $packet['reply_id'] = $data['reply_id'];
                        $packet_res = Db::name('user_red_packet')->insert($packet);
                        if (!$packet_res) {
                            Db::rollback();
                            $rs = ['status' => 'error', 'msg' => '审核失败！4'];
                            return json_encode($rs);
                        }
                        //增加积分表数据
                        $amount['user_id'] = $data['uid'];
                        $amount['category'] = 3;
                        $amount['finance'] = $get_money;
                        $amount['ruins_time'] = time();
                        $amount['solution'] = '回帖红包奖励';
                        $amount['evaluate'] = $paper['initial_type'];
                        $amount['much_id'] = $data['much_id'];
                        if ($paper['initial_type'] == 0) {
                            $amount['poem_fraction'] = $user_info['fraction'];
                            $amount['surplus_fraction'] = $user_info['fraction'];
                            $amount['poem_conch'] = $user_info['conch'];
                            $amount['surplus_conch'] = bcadd($user_info['conch'], $get_money, 2);
                        } else {
                            $amount['poem_fraction'] = $user_info['fraction'];
                            $amount['surplus_fraction'] = bcadd($user_info['fraction'], $get_money, 2);;
                            $amount['poem_conch'] = $user_info['conch'];
                            $amount['surplus_conch'] = $user_info['conch'];
                        }
                        $amount_res = Db::name('user_amount')->insert($amount);
                        if (!$amount_res) {
                            Db::rollback();
                            $rs = ['status' => 'error', 'msg' => '审核失败！3'];
                            return json_encode($rs);
                        } else {
                            $rs = ['info' => $hui_info, 'status' => 'success', 'msg' => '审核成功'];
                        }
                        //$zong_money += $get_money;
                        //更新红包数据
                        $packet_c['surplus_quantity'] = $paper['surplus_quantity'] - 1;
                        if ($paper['initial_type'] == 0) {
                            $packet_c['surplus_conch'] = bcsub($paper['surplus_conch'], $get_money, 2);
                        } else {
                            $packet_c['surplus_fraction'] = bcsub($paper['surplus_fraction'], $get_money, 2);

                        }
                        $packet_c_res = Db::name('paper_red_packet')->where('id', $paper['id'])->where('much_id', $data['much_id'])->update($packet_c);
                        if (!$packet_c_res) {
                            Db::rollback();
                            $rs = ['status' => 'error', 'msg' => '审核失败！1'];
                            return json_encode($rs);
                        }

                        //用户增加贝壳
                        if ($paper['initial_type'] == 0) {
                            $uop['conch'] = $amount['surplus_conch'];
                        } else {
                            $uop['fraction'] = $amount['surplus_fraction'];
                        }
                        $user_j_res = Db::name('user')->where('id', $data['uid'])->where('much_id', $data['much_id'])->update($uop);
                        if (!$user_j_res) {
                            Db::rollback();
                            $rs = ['status' => 'error', 'msg' => '审核失败！6'];
                            return json_encode($rs);
                        }
                    }


                }
            }

            //帖子标题
            $page_title = $fa_info['study_title'] == '' ? '' : subtext($fa_info['study_title'], 10);
            //回复详情
            $hui_title = subtext($hui_info['reply_content'], 10);

            if (empty($hui_info['reply_content'])) {
                if ($hui_info['reply_type'] == 0) {
                    $hui_title = '[一张图片]';
                }
                if ($hui_info['reply_type'] == 1) {
                    $hui_title = '[一段语音]';
                }
            }

            if (empty($page_title)) {
                if ($fa_info['study_type'] == 0) {
                    $page_title = '[图片帖子]';
                }
                if ($fa_info['study_type'] == 1) {
                    $page_title = '[语音帖子]';
                }
                if ($fa_info['study_type'] == 2) {
                    $page_title = '[视频帖子]';
                }
            }
            if ($ins['uccid'] != 0) {
                $card_info_a = Db::name('camouflage_card')->where('id', $ins['uccid'])->where('much_id', $data['much_id'])->find();
                $user_info['user_nick_name'] = $card_info_a['forgery_name'];
            }
            //发送模版
            $this->add_template(['much_id' => $data['much_id'],
                'at_id' => 'YL0001',
                'user_id' => $fa_info['user_id'],
                'page' => 'yl_welore/pages/packageA/article/index?id=' . $fa_info['id'] . '&type=' . $fa_info['study_type'],
                'keyword1' => empty(strip_tags(emoji_decode($page_title))) ? '暂无标题' : strip_tags(emoji_decode($page_title)),
                'keyword2' => emoji_decode($user_info['user_nick_name']),
                'keyword3' => $hui_title,
                'keyword4' => date('Y年m月d日 H:i:s', time()),
            ]);
            // 提交事务
            Db::commit();
            return json_encode($rs);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '回复失败！' . $e->getMessage()];
            return json_encode($rs);
        }

    }

    /**
     * 普通红包
     */
    public function rob_red_avg($sum, $num)
    {
        // PHP 8.4兼容性修复：防止除零错误
        if ($num <= 0) {
            return array(); // 红包数量无效时返回空数组
        }
        $res = $sum > 0 ? $sum / $num : 0;
        for ($i = 0; $i < $num; $i++) {
            $arr[$i] = $res;
        }
        //check($arr);
        return $arr[0];
    }

    /**
     * 获取用户等级
     */
    public function get_user_level($level_hierarchy, $much_id)
    {
        $res = Db::name('user_level')->where('much_id', $much_id)->where('level_hierarchy', $level_hierarchy)->find();
        return $res;
    }

    /**
     * 用户佩戴的昵称特效
     */
    public function get_user_special_nickname($user_id, $much_id)
    {
        //查询用户表
        $user = Db::name('user')->where('id', $user_id)->where('much_id', $much_id)->find();
        if ($user['wear_special_id'] == 0) {
            return 'yl_style0';
        }
        $info = Db::name('special_nickname')
            ->where('id', $user['wear_special_id'])
            ->where('much_id', $much_id)
            ->find();
        if ($info['status'] == 0) {
            return 'yl_style0';
        }
        return $info['special_style'];
    }

    /**
     * 用户佩戴的昵称特效
     */
    public function get_user_avatar_frame($user_id, $much_id)
    {
        //查询用户表
        $user = Db::name('user')->where('id', $user_id)->where('much_id', $much_id)->find();
        if ($user['wear_af'] == 0) {
            return '';
        }
        $info = Db::name('avatar_frame')
            ->where('id', $user['wear_af'])
            ->where('much_id', $much_id)
            ->find();
        if ($info['status'] == 0) {
            return '';
        }
        return $info['adorn_icon'];
    }

    /**
     * 获取勋章图片
     */
    public function get_medal($id, $much_id)
    {
        $res = Db::name('medal')->where('much_id', $much_id)->where('id', $id)->find();
        if ($res) {
            return $res['merit_icon'];
        } else {
            return '';
        }

    }

    /**
     * 合并数组
     */
    public function array_and_time($data1, $data2)
    {
        // PHP 8.4兼容性修复：确保参数是数组
        $data1 = is_array($data1) ? $data1 : array();
        $data2 = is_array($data2) ? $data2 : array();
        $arr = array_merge($data1, $data2);
        $ctime_str = array();
        foreach ($arr as $key => $v) {
            $ctime_str[] = $arr[$key]['unlock_time'];
        }
        array_multisort($ctime_str, SORT_DESC, $arr);
        return $arr;
    }

    /**
     * 检测权限
     */
    public function get_user_check($open_id, $tory_id)
    {
        //是否是超管或者管理员
        $da_xiao = $this->check_qq($open_id, $tory_id);

        if ($da_xiao == 'xiao' || $da_xiao == 'da') {
            return 1; //有权限
        }
        //是否是超管
        $cg = Db::name('user_maker')->where('user_open_id', $open_id)->where('status', '1')->count();
        if ($cg > 0) {
            return 1; //有权限
        }
        return 0;
    }

    /**
     * 检测违规词汇
     */
    public function get_check_msg($msg, $much_id, $openid)
    {
        // 如果消息为空直接返回成功
        if (empty($msg)) {
            return ['status' => 'success', 'code' => 0];
        }
        // 获取违规词配置（只查询一次数据库）
        $user_violation = Db::name('user_violation')->where('much_id', $much_id)->find();
        if (empty($user_violation)) {
            return ['status' => 'success', 'code' => 0];
        }
        // 预处理消息内容
        $name = preg_replace('# #', '', emoji_encode($msg));
        // 处理违规词
        $violation = explode("|", $user_violation['content_offend']);
        $violation = array_filter($violation); // 过滤空值
        // 优化的违规词检测
        if (!empty($violation)) {
            // 分批处理大量违规词，避免内存溢出
            $batch_size = 300;
            $total = count($violation);
            for ($i = 0; $i < $total; $i += $batch_size) {
                $batch = array_slice($violation, $i, $batch_size);
                foreach ($batch as $word) {
                    if (empty($word)) continue;
                    // 使用mb_stripos提高搜索效率，不区分大小写
                    if (mb_stripos($name, $word) !== false) {
                        return ['status' => 'error', 'code' => 1, 'msg' => '内容含有违规词!'];
                    }
                }
                // 释放内存
                unset($batch);
            }
        }
        // 是否开启网络验证（直接使用已查询的数据）
        if ($user_violation['open_network_content_offend'] == 1) {
            $ms = $this->check_msg($msg, $much_id, $openid);
            if ($ms['errcode'] == 0) {
                if ($ms['result']['suggest'] == 'risky') {
                    return ['status' => 'error', 'code' => 1, 'msg' => '内容含有违法违规内容-1', 'info' => $ms];
                }
            } else {
                return ['status' => 'error', 'code' => 1, 'msg' => $ms['errmsg'], 'info' => $ms];
            }
        }
        // 腾讯云TMS
        if ($user_violation['open_network_nickname_offend'] == 2) {
            return Moderation::Text($much_id, $msg);
        }
        return ['status' => 'success', 'code' => 0];
    }

    /**
     * 查询会员是否回复了此贴
     */
    public function reply_article($id, $user_id, $much_id)
    {
        $paper_reply = Db::name('paper_reply')->where('paper_id', $id)->where('user_id', $user_id)->where('much_id', $much_id)->count();
        return $paper_reply;
    }

    //统一方法，增加用户经验和荣誉明细
    public function user_exp_glory_logger($user, $type, $cypher, $points, $dot_cap, $much_id)
    {
        if ($type == 0) {
            $dot_before = $user['experience'];
        } else {
            $dot_before = $user['honor_point'];
        }
        if ($cypher == 0) {
            $dot_after = bcadd($dot_before, $points);
        } else {
            $dot_after = bcsub($dot_before, $points);
        }
        $d['user_id'] = $user['id'];
        $d['type'] = $type;
        $d['cypher'] = $cypher;
        $d['dot_before'] = $dot_before;
        $d['points'] = $points;
        $d['dot_after'] = $dot_after;
        $d['dot_cap'] = $dot_cap;
        $d['receive_time'] = time();
        $d['much_id'] = $much_id;
        $ins_user_exp_glory_logger = Db::name('user_exp_glory_logger')->insert($d);
        return $ins_user_exp_glory_logger;
    }

    //统一方法，增加用户资金明细表
    public function user_amount($user_id, $category, $finance, $poem_fraction, $surplus_fraction, $poem_conch, $surplus_conch, $evaluate, $solution, $much_id)
    {
        $amount['user_id'] = $user_id;
        $amount['category'] = $category;
        if ($category == 1 || $category == 2) {
            $amount['finance'] = -$finance;
        } else {
            $amount['finance'] = $finance;
        }

        $amount['poem_fraction'] = $poem_fraction;
        $amount['surplus_fraction'] = $surplus_fraction;
        $amount['poem_conch'] = $poem_conch;
        $amount['surplus_conch'] = $surplus_conch;
        $amount['solution'] = $solution;
        $amount['ruins_time'] = time();
        $amount['evaluate'] = $evaluate;
        $amount['much_id'] = $much_id;
        $ins_amount = Db::name('user_amount')->insert($amount);
        return $ins_amount;
    }

    /**
     * 计算全部回复人数
     */
    public function get_paper_reply($paper_id, $much_id)
    {
        //计算全部回复人数
        $huifu_all_count = Db::name('paper_reply')->where('reply_status', 1)->where('paper_id', $paper_id)->where('whether_delete=0')->where('much_id', $much_id)->count();

        $huifu_hui_count = Db::name('paper_reply')->alias('p')
            ->join('paper_reply_duplex r', 'r.reply_id=p.id')
            ->where('p.whether_delete=0')
            ->where('p.much_id', $much_id)
            ->where('p.paper_id', $paper_id)
            ->count();
        return formatNumber(bcadd($huifu_all_count, $huifu_hui_count));
    }

    /**
     * 计算帖子收藏人数
     */
    public function get_paper_collect($paper_id, $much_id)
    {
        $count = Db::name('user_collect')
            ->where('much_id', $much_id)
            ->where('paper_id', $paper_id)
            ->count();
        return formatNumber($count);
    }

    /**
     * 获取uid详情
     */
    public function get_uid_info($uid)
    {
        $user_info = Db::name('user')->where('id', $uid)->find();
        return $user_info;
    }

    /**
     * 获取评论回复（回复编号找帖子ID）
     */
    public function get_paper_reply_list($id, $much_id)
    {
        $list = Db::name('paper_reply')->alias('p')
            ->join('user u', 'u.id=p.user_id')
            ->where('p.paper_id', $id)
            ->where('p.much_id', $much_id)
            ->where('p.whether_delete', 0)
            ->where('p.reply_status', 1)
            ->where('p.reply_type', 0)
            ->order('p.praise desc')
            ->limit(3)
            ->field('p.uccid,p.reply_content,p.image_part,u.user_nick_name')
            ->select();
        foreach ($list as $k => $v) {
            $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            $list[$k]['reply_content'] = emoji_decode($v['reply_content']);
            $list[$k]['reply_content'] = Alternative::ExpressionHtml($list[$k]['reply_content']);
            $list[$k]['image_part'] = json_decode($v['image_part']);
            if (empty($list[$k]['image_part'])) {
                $list[$k]['image_part'] = array();
            }
            if (intval($v['uccid']) != 0) {
                //查询佩戴的身份
                $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $much_id)->find();
                $list[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                $list[$k]['user_id'] = 0;
                $list[$k]['user_nick_name'] = $card_info['forgery_name'];
            }
        }
        return $list;
    }

    /**
     * 获取评论回复（回复编号找帖子ID）
     */
    public function get_dupx_paper($reply_id)
    {
        $info = Db::name('paper_reply')->where('id', $reply_id)->find();
        return $info;
    }

    /**
     * 圈子所在广场
     */
    public function get_territory_needle($territory_id, $much_id)
    {
        $info = Db::name('territory')->where('id', $territory_id)->where('much_id', $much_id)->find();
        $c_info = Db::name('needle')->where('id', $info['needle_id'])->where('much_id', $much_id)->find();
        return $c_info;
    }

    /**
     *  新人营销 审核内容 增加积分/贝壳
     *  much_id //多用户标识
     *  key // 1:圈子 2：帖子回复
     *  tory_id //圈子ID
     *  paper_id //帖子ID
     *  uid //当前发帖回复用户ID
     */
    public function new_user_task($data)
    {
        //获取奖励详情
        $task = Db::name('new_user_task')->where('much_id', $data['much_id'])->find();
        if (empty($task)) {
            return ['code' => -1, 'msg' => '未设置新人营销！'];
        }
        if ($task['reward_status'] == 0) {
            return ['code' => -1, 'msg' => '未开启新人营销！'];
        }
        //获取当前用户信息
        $user_info = Db::name('user')->where('much_id', $data['much_id'])->where('id', $data['uid'])->find();
        //判断用户是否超过奖励时间
        $time = bcsub(time(), $task['reg_less_day'] * 86400);
        if (bccomp($time, $user_info['user_reg_time']) >= 0) {
            return ['code' => -1, 'msg' => '用户已超过新人营销期！'];
        }
        $tory_ids = empty($task['tory_ids']) ? '' : explode(",", $task['tory_ids']);
        $paper_ids = empty($task['paper_ids']) ? '' : explode(",", $task['paper_ids']);
        //判断发的内容是否在营销设置里
        if ($data['key'] == 1) {
            $check = empty($task['tory_ids']) ? true : in_array($data['tory_id'], $tory_ids);
        } else {
            $check = empty($task['paper_ids']) ? true : in_array($data['paper_id'], $paper_ids);
        }
        if (!$check) {
            return ['code' => -1, 'msg' => '不在奖励范围！'];
        }
        //查询完成次数
        $task_count = Db::name('new_user_task_record')->where('user_id', $data['uid'])->where('much_id', $data['much_id'])->count();
        if ($task['reward_count'] != 0) {
            if ($task_count >= $task['reward_count']) {
                return ['code' => -1, 'msg' => '奖励次数已达上限！'];
            }
        }

//        foreach ($task_list as $k => $v) {
//            if ($data['key'] == 1) {
//                //已经奖励过了
//                if ($v['tory_id'] == $data['tory_id']) {
//                    return ['code' => -1, 'msg' => '已获得奖励！'];
//                }
//            } else {
//                //已经奖励过了
//                if ($v['paper_id'] == $data['paper_id']) {
//                    return ['code' => -1, 'msg' => '已获得奖励！'];
//                }
//            }
//        }

        // 启动事务
//        Db::startTrans();
//        try {
        //没有奖励过，插入数据
        //判断奖励的内容 0:积分 1：贝壳
        if ($task['reward_type'] == 0) {
            //货币明细表增加数据
            $money = bcadd(abs($task['reward_code']), $user_info['fraction'], 2);
            $amount = $this->user_amount($data['uid'], 3, abs($task['reward_code']), $user_info['fraction'], $money, $user_info['conch'], $user_info['conch'], 1, '新人营销奖励', $data['much_id']);
        } else {
            //货币明细表增加数据
            $money = bcadd(abs($task['reward_code']), $user_info['conch'], 2);
            $amount = $this->user_amount($data['uid'], 3, abs($task['reward_code']), $user_info['fraction'], $user_info['fraction'], $user_info['conch'], $money, 0, '新人营销奖励', $data['much_id']);
        }
        if (!$amount) {
            //Db::rollback();
            return ['code' => 1, 'msg' => '插入数据失败！code:1'];
        }
        //插入任务完成列表
        $n['user_id'] = $data['uid'];
        //$n['tory_id'] =  empty($task['tory_ids']) ?0:$data['tory_id'];
        //$n['paper_id'] = empty($task['paper_ids']) ?0:$data['paper_id'];
        $n['tory_id'] = $task['tory_ids'];
        $n['paper_id'] = $data['paper_id'];
        $n['reward_type'] = $task['reward_type'];
        $n['reward_code'] = $task['reward_code'];
        $n['reward_time'] = time();
        $n['much_id'] = $data['much_id'];
        $new = Db::name('new_user_task_record')->insert($n);
        if (!$new) {
            //Db::rollback();
            return ['code' => 1, 'msg' => '插入数据失败！code:2'];
        }
        $design = Db::name('design')->where('much_id', $data['much_id'])->find();
        //增加用户余额
        if ($task['reward_type'] == 0) {
            $msg = '新人营销奖励:' . $task['reward_code'] . $design['confer'];
            //货币明细表增加数据
            $money = bcadd(abs($task['reward_code']), $user_info['fraction'], 2);
            $user_update = Db::name('user')->where('id', $data['uid'])->update(['fraction' => $money]);
        } else {
            $msg = '新人营销奖励:' . $task['reward_code'] . $design['currency'];
            //货币明细表增加数据
            $money = bcadd(abs($task['reward_code']), $user_info['conch'], 2);
            $user_update = Db::name('user')->where('id', $data['uid'])->update(['conch' => $money]);
        }
        if (!$user_update) {
            //Db::rollback();
            return ['code' => 1, 'msg' => '插入数据失败！code:3'];
        }
        // 提交事务
        //Db::commit();
        //获取积分名称
        return ['code' => 0, 'msg' => $msg];

    }

    /**
     * 获取帖子回复总量
     */
    public function get_reply_count($paper_id, $much_id)
    {
        $rep_count = Db::name('paper_reply')->where('paper_id', $paper_id)->where('whether_delete', 0)->where('reply_status', 1)->where('much_id', $much_id)->count();
        return $rep_count;
    }

    /**
     * 小程序接口秘钥
     */
    public function getWchatAcctoken($much_id)
    {
        //获取后台APPID
        $getConfig = cache('fatal_' . $much_id);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $much_id)->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $much_id, $getConfig);
            }
        }
        $option = ['ssl' => ['verify_peer' => false, 'verify_peer_name' => false]];

        $acc = cache('access_token_' . $much_id);
        if ($acc) {
            //$exp = json_decode(cache('access_token_' . $data['much_id']));
            if ($acc['expires_in'] < time()) {
                $url_access_token = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=' . $getConfig['app_id'] . '&secret=' . $getConfig['app_secret'];

                $json_access_token = file_get_contents($url_access_token, false, stream_context_create($option));
                $arr_access_token = json_decode($json_access_token, true);
                $arr_access_token['expires_in'] = $arr_access_token['expires_in'] + time();
                cache('access_token_' . $much_id, $arr_access_token);
                $access_token = $arr_access_token['access_token'];

            } else {
                $access_token = $acc['access_token'];
            }
        } else {
            $url_access_token = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=' . $getConfig['app_id'] . '&secret=' . $getConfig['app_secret'];
            $json_access_token = file_get_contents($url_access_token, false, stream_context_create($option));
            $arr_access_token = json_decode($json_access_token, true);
            $arr_access_token['expires_in'] = $arr_access_token['expires_in'] + time();
            cache('access_token_' . $much_id, $arr_access_token);
            $access_token = $arr_access_token['access_token'];
        }
        return $access_token;
    }

    /**
     * 头条接口密钥
     */
    public function getTouTiaoAcctoken($much_id)
    {
        $acc = cache('access_token_toutiao_' . $much_id);
        $url_access_token = 'https://developer.toutiao.com/api/apps/v2/token';
        if ($acc) {
            if ($acc['expires_in'] < time()) {
                $json_access_token = $this->TouTiaoApiPost($url_access_token, ['appid' => 'tt0230748d9d0e90a701', 'secret' => 'd62d38e004857d834837b5d447a3b6b7371071ae', 'grant_type' => 'client_credential'], $much_id);
                $arr_access_token = json_decode($json_access_token, true);
                $arr_access_token['expires_in'] = $arr_access_token['data']['expires_in'] + time();
                cache('access_token_toutiao_' . $much_id, $arr_access_token);
                $access_token = $arr_access_token['data']['access_token'];
            } else {
                $access_token = $acc['access_token'];
            }
        } else {
            $json_access_token = $this->TouTiaoApiPost($url_access_token, ['appid' => 'tt0230748d9d0e90a701', 'secret' => 'd62d38e004857d834837b5d447a3b6b7371071ae', 'grant_type' => 'client_credential'], $much_id);
            $arr_access_token = json_decode($json_access_token, true);
            $arr_access_token['expires_in'] = $arr_access_token['data']['expires_in'] + time();
            cache('access_token_toutiao_' . $much_id, $arr_access_token);
            $access_token = $arr_access_token['data']['access_token'];
        }
        return $access_token;
    }

    /**
     * @param string $prefix
     * @return string
     * 生成文件名
     */
    public function create_uuid($prefix = "Q")
    {
        $str = md5(uniqid(mt_rand(), true));
        $uuid = substr($str, 0, 8) . '';
        $uuid .= substr($str, 8, 4) . '';
        $uuid .= substr($str, 12, 4) . '';
        $uuid .= substr($str, 16, 4) . '';
        $uuid .= substr($str, 20, 12);
        return $prefix . $uuid;
    }

    /**
     * 头条POST访问
     */
    public function TouTiaoApiPost($url, $param, $much_id)
    {
        $params = json_encode($param, true);
        $option = [
            'ssl' => ['verify_peer' => false, 'verify_peer_name' => false],
            'http' => [
                'method' => 'POST',
                'header' => 'Content-type: application/json',
                'content' => $params,
                'timeout' => 30 // 超时时间（单位:s）
            ]
        ];
        return @file_get_contents($url, false, stream_context_create($option));
    }

    /**
     * 开奖
     */
    public function insKai($data)
    {
        $util = new Util();
        $info = Db::name('sweepstake_list')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        if (empty($info) || $info['is_del'] == 1) {
            return ['msg' => '当前活动已被删除！', 'code' => 0, 'info' => []];
        }
        //查询是否已经开奖
        $winning = Db::name('sweepstake_winning')->where('sp_id', $data['id'])->find();
        if (!empty($winning)) {
            return ['msg' => '当前活动已开奖！', 'code' => 0, 'info' => []];
        }
        //查询当前活动是否到开奖时间
        if ($info['draw_time'] >= time() && $data['now'] == 0) {
            return ['msg' => '未到开奖时间！', 'code' => 0, 'info' => []];
        }

        $prize_list = json_decode($info['prize_list'], true);
        //计算总数量
        $totalQuantity = 0;
        if (is_array($prize_list)) {
            foreach ($prize_list as $item) {
                $totalQuantity += intval($item['extractQuantity']);
            }
        }
        //数组倒叙
        usort($prize_list, function ($a, $b) {
            return $b['prizeLevel'] - $a['prizeLevel'];
        });

        $prize_new = $prize_list;

        //如果是从号池抽奖
        if ($info['extract_type'] == 0) {
            $selectedPrize = Db::name('sweepstake_participate')
                ->orderRaw('rand()')
                ->where('sp_id', $data['id'])
                ->limit($totalQuantity)
                ->field('id,lucky_number')
                ->select();

            //if (count($selectedPrize) < $totalQuantity) {
            $iva = intval($totalQuantity) - intval($selectedPrize);
            //查询当前活动的最大值
            $participate = Db::name('sweepstake_participate')
                ->where('much_id', $data['much_id'])
                ->where('sp_id', $info['id'])
                ->order('id desc')
                ->find();
            $number = $participate['lucky_number'];
            $originalLength = strlen($number);
            $number = ltrim($number, '0');
            $arr = array();
            for ($i = 1; $i <= $iva; $i++) {
                $c = strval(intval($number) + $i);
                $numberOfDigits = str_pad($c, $originalLength, '0', STR_PAD_LEFT);
                $arr[$i]['lucky_number'] = $numberOfDigits;
                $arr[$i]['id'] = 0;
            }
            $selectedPrize = array_merge($selectedPrize, $arr);
            $ic = 0;
            foreach ($prize_list as $k => $v) {
                // 截取数组的一部分
                $prize_list[$k]['luckyNumber'] = array_slice($selectedPrize, $ic, $v['extractQuantity']);
                $ic += $v['extractQuantity'];
            }
//            }else{
//
//            }
        } else {
            //查询当前活动的最大值
            $participate = Db::name('sweepstake_participate')
                ->where('much_id', $data['much_id'])
                ->where('sp_id', $info['id'])
                ->order('id desc')
                ->find();
            //查询奖券组
            $zu = $participate['award_categories'];
            $zu_name = array();
            if ($zu > 0) {
                for ($i = 0; $i <= $zu; $i++) {
                    array_push($zu_name, $i);
                }
            } else {
                $zu_name = [0];
            }
            //生成随机奖券
            $random_numbers = array();
            while (count($random_numbers) < $totalQuantity) {
                $random_prefix = $zu_name[array_rand($zu_name)]; // 从前缀数组中随机选择一个
                $random_number = mt_rand($info['random_extract_range_start'], $info['random_extract_range_end']); // 生成随机数，位数不确定
                $formatted_number = str_pad($random_number, strlen($info['random_extract_range_end']), '0', STR_PAD_LEFT); // 添加前缀并格式化为带前导零的字符串
                if (!in_array($formatted_number, $random_numbers)) {
                    $random_numbers[] = array($formatted_number, $random_prefix); // 创建包含随机数和前缀的数组
                }
            }
            foreach ($prize_list as $k => $v) {
                $prize_list[$k]['luckyNumber'] = array();
                foreach ($random_numbers as $a => $b) {
                    $check_lucky_number = Db::name('sweepstake_participate')
                        ->where('lucky_number', $b[0])
                        ->where('award_categories', $b[1])
                        ->where('much_id', $data['much_id'])
                        ->where('sp_id', $data['id'])
                        ->find();
                    if ($check_lucky_number && intval($v['extractQuantity']) > count($prize_list[$k]['luckyNumber'])) {
                        if ($info['is_group'] == 1) {
                            array_push($prize_list[$k]['luckyNumber'], ['id' => $check_lucky_number['id'], 'lucky_number' => $this->numberToLetter($b[1]) . $b[0]]);
                        } else {
                            array_push($prize_list[$k]['luckyNumber'], ['id' => $check_lucky_number['id'], 'lucky_number' => $b[0]]);
                        }

                        unset($random_numbers[$a]);
                    }
                }
            }
            if ($info['is_group'] == 1) {
                foreach ($random_numbers as $key => $value) {
                    $random_numbers[$key] = array(
                        'id' => 0,
                        'lucky_number' => $this->numberToLetter($value[1]) . $value[0],
                    );
                }
            } else {
                foreach ($random_numbers as $key => $value) {
                    $random_numbers[$key] = array(
                        'id' => 0,
                        'lucky_number' => $value[0],
                    );
                }
            }
            $ic = 0;
            foreach ($prize_list as $k => $v) {
                if (count($v['luckyNumber']) < intval($v['extractQuantity'])) {
                    // 截取数组的一部分
                    $in = intval($v['extractQuantity']) - count($prize_list[$k]['luckyNumber']);
                    $array_slice = array_slice($random_numbers, $ic, $in);
                    $prize_list[$k]['luckyNumber'] = array_merge($prize_list[$k]['luckyNumber'], $array_slice);
                    $ic += $in;
                }
            }
        }
        // 启动事务
        Db::startTrans();
        try {
            //循环分割后的数组进行数据库操作
            foreach ($prize_list as $k => $v) {
                $prize_new[$k]['luckyNumber'] = array();
                //再循环奖券
                foreach ($v['luckyNumber'] as $a => $b) {
                    array_push($prize_new[$k]['luckyNumber'], $b['lucky_number']);
                    //再循环奖券
                    if ($b['id'] != 0) {
                        $participate_info = Db::name('sweepstake_participate')
                            ->where('id', $b['id'])
                            ->find();
                        $is_payout_prizes = $v['prizeType'] != 0 ? 1 : 0;


                        //修改中奖编号
                        $up_info = Db::name('sweepstake_participate')
                            ->where('id', $b['id'])
                            ->update(['is_payout_prizes' => $is_payout_prizes, 'is_award' => 1, 'update_time' => time(), "award_level" => $v['prizeLevel'], 'award_type' => $v['prizeType']]);
                        if (!$up_info) {
                            Db::rollback();
                            return ['msg' => '开奖失败-2！', 'code' => 0, 'info' => []];
                        }
                        $user_info = Db::name('user')->where('id', $participate_info['user_id'])->where('much_id', $data['much_id'])->find();
                        //派奖 0实物，1贝壳，2积分，3经验，4荣誉
                        if ($v['prizeType'] == 0) {
                            $user_up = true;
                        }
                        if ($v['prizeType'] == 1) {
                            //计算金额
                            $user_conch = bcadd($v['prizeQuantity'], $user_info['conch'], 2);
                            $user_up = Db::name('user')->where('id', $participate_info['user_id'])->update(['conch' => $user_conch]);
                        }
                        if ($v['prizeType'] == 2) {
                            //计算金额
                            $user_fraction = bcadd($v['prizeQuantity'], $user_info['fraction'], 2);
                            $user_up = Db::name('user')->where('id', $participate_info['user_id'])->update(['fraction' => $user_fraction]);
                        }
                        if ($v['prizeType'] == 3) {
                            $user_up = $this->addExp($user_info, $v['prizeQuantity'], $data['much_id']);
                        }
                        if ($v['prizeType'] == 4) {
                            //计算金额
                            $user_honor_point = bcadd($v['prizeQuantity'], $user_info['honor_point'], 2);
                            $user_up = Db::name('user')->where('id', $participate_info['user_id'])->update(['honor_point' => $user_honor_point]);
                        }
                        if (!$user_up) {
                            Db::rollback();
                            return ['msg' => '开奖失败-3！', 'code' => 0, 'info' => []];
                        }
                        $maring = "恭喜您！在活动：" . $info['lottery_name'] . '，中得' . numToWord($v['prizeLevel']) . '等奖。奖品：' . $v['prizeName'];
                        //发送站内信
                        $util->add_user_smail($participate_info['user_id'], $maring, $data['much_id'], 0);
                        $util->add_template([
                            'much_id' => $data['much_id'],
                            'at_id' => 'YL0009',
                            'user_id' => $participate_info['user_id'],
                            'page' => 'yl_welore/pages/user_smail/index',
                            'keyword1' => '恭喜您！中奖啦！',
                            'keyword2' => date('Y年m月d日 H:i:s', time())
                        ]);
                    }
                }
            }

            //保存开奖信息
            usort($prize_new, function ($a, $b) {
                return $a['prizeLevel'] - $b['prizeLevel'];
            });
            $winning_ins = Db::name('sweepstake_winning')->insert(['prize_outcome' => json_encode($prize_new, JSON_UNESCAPED_UNICODE), 'prize_time' => time(), 'sp_id' => $data['id'], 'much_id' => $data['much_id']]);
            if (!$winning_ins) {
                Db::rollback();
                return ['msg' => '开奖失败-4！', 'code' => 0, 'info' => []];
            }
            //修改中奖详情
            $up_list = Db::name('sweepstake_list')
                ->where('id', $data['id'])
                ->update(['is_winning' => 1]);
            if (!$up_list) {
                Db::rollback();
                return ['msg' => '开奖失败-5！', 'code' => 0, 'info' => []];
            }
            // 提交事务
            Db::commit();
            return ['msg' => '开奖成功！', 'code' => 1, 'info' => []];
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return ['msg' => '开奖失败-6！', 'code' => 0, 'info' => []];
        }
    }

    /**
     * 经验值结算
     */
    public function addExp($user_info, $money, $much_id)
    {
        //当前用户经验值
        $this_exp = bcadd($user_info['experience'], $money, 2);
        //当前用户等级
        $this_level = $user_info['level'];
        while (true) {
            $this_level++;
            $user_level = Db::name('user_level')->where('level_hierarchy', $this_level)->where('much_id', $much_id)->find();
            //下一级没有内容跳出循环
            if (empty($user_level)) {
                $user_fraction = Db::name('user')->where('id', $user_info['id'])->update(['experience' => $this_exp]);
                if ($user_fraction === false) {
                    Db::rollback();
                    return false;
                }
                break;
            }
            //return $this_exp >= $user_level['need_experience'];
            //当前经验大于下一级经验
            if ($this_exp >= $user_level['need_experience']) {
                //计算余下经验值
                $this_exp = bcsub($this_exp, $user_level['need_experience'], 2);
                //更新用户信息
                $user_fraction = Db::name('user')->where('id', $user_info['id'])->update(['honor_point' => bcadd($user_info['honor_point'], $user_level['honor_point'], 2), 'level' => $this_level, 'experience' => $this_exp]);
                if (!$user_fraction) {
                    Db::rollback();
                    return false;
                }
            } else {
                //更新用户信息
                //判断是否和数据库一致
                $user_fraction = Db::name('user')->where('id', $user_info['id'])->update(['experience' => $this_exp]);
                if ($user_fraction === false) {
                    Db::rollback();
                    return false;
                }
                break;
            }
        }
        return true;
    }

    public function numberToLetter($number)
    {
        $base = 26; // 字母表的基数
        $letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        $result = "";
        while ($number >= 0) {
            $remainder = $number % $base;
            $result = $letters[$remainder] . $result;
            $number = floor($number / $base) - 1;

            if ($number < 0) {
                break;
            }
        }
        return $result;
    }
}