<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:89:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/systems/proclaim.html";i:1629698932;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755608968;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-adn"></span> 流量主
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-form am-form-horizontal" style="width: 670px;height:790px;margin: 0px auto;box-shadow: 0px 0px 10px 0px black;">
                    <div class="am-form-group" style="padding-top:40px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">圈子广告</label>
                        <div class="am-u-sm-8 am-u-end" style="margin-top:3px;">
                            <div style="width:100%;display:flex;align-items:center;">
                                <label>
                                    <input type="radio" name="adstory" value="0" <?php if($list['adstory']==0): ?>checked<?php endif; ?>>
                                    <span style="font-size:14px;font-weight:initial;">关闭</span>
                                </label>
                                <label style="margin-left:20px;">
                                    <input type="radio" name="adstory" value="1"  <?php if($list['adstory']==1): ?>checked<?php endif; ?>>
                                    <span style="font-size:14px;font-weight:initial;">开启</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding-top:10px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">帖子广告</label>
                        <div class="am-u-sm-8 am-u-end" style="margin-top:3px;">
                            <div style="width:100%;display:flex;align-items:center;">
                                <label>
                                    <input type="radio" name="adsper" value="0" <?php if($list['adsper']==0): ?>checked<?php endif; ?>>
                                    <span style="font-size:14px;font-weight:initial;">关闭</span>
                                </label>
                                <label style="margin-left:20px;">
                                    <input type="radio" name="adsper" value="1"  <?php if($list['adsper']==1): ?>checked<?php endif; ?>>
                                    <span style="font-size:14px;font-weight:initial;">开启</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0px 0px 0px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">广告相隔行数</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="number" id="isolate" value="<?php echo $list['isolate']; ?>" placeholder="请输入广告相隔行数">
                            <small>每相隔多少行 ( 数据 ) 显示一条广告</small>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">Banner ID</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="adunitId" value="<?php echo $list['adunit_id']; ?>" placeholder="请输入Banner流量主ID">
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0px 0px 0px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">激励式 ID</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="incentiveId" value="<?php echo $list['incentive_id']; ?>" placeholder="请输入激励式流量主ID">
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0px 0px 0px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">激励次数限制</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="number" id="incentiveDuct" value="<?php echo $list['incentive_duct']; ?>" placeholder="请输入激励式广告次数限制">
                            <small>用户每日浏览激励式广告的奖励次数限制 0为不限制 默认每日5次</small>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding-top:10px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">贴片广告</label>
                        <div class="am-u-sm-8 am-u-end" style="margin-top:3px;">
                            <div style="width:100%;display:flex;align-items:center;">
                                <label>
                                    <input type="radio" name="prePostTwig" value="0" <?php if($list['pre_post_twig']==0): ?>checked<?php endif; ?>>
                                    <span style="font-size:14px;font-weight:initial;">关闭</span>
                                </label>
                                <label style="margin-left:20px;">
                                    <input type="radio" name="prePostTwig" value="1"  <?php if($list['pre_post_twig']==1): ?>checked<?php endif; ?>>
                                    <span style="font-size:14px;font-weight:initial;">开启</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">视频贴片 ID</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="prePostId" value="<?php echo $list['pre_post_id']; ?>" placeholder="请输入视频贴片流量主ID">
                        </div>
                    </div>

                    <div class="am-form-group" style="padding-top:10px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">原生模版广告</label>
                        <div class="am-u-sm-8 am-u-end" style="margin-top:3px;">
                            <div style="width:100%;display:flex;align-items:center;">
                                <label>
                                    <input type="radio" name="latticeTwig" value="0" <?php if($list['lattice_twig']==0): ?>checked<?php endif; ?>>
                                    <span style="font-size:14px;font-weight:initial;">关闭</span>
                                </label>
                                <label style="margin-left:20px;">
                                    <input type="radio" name="latticeTwig" value="1"  <?php if($list['lattice_twig']==1): ?>checked<?php endif; ?>>
                                    <span style="font-size:14px;font-weight:initial;">开启</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">原生模版广告 ID</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="latticeId" value="<?php echo $list['lattice_id']; ?>" placeholder="请输入原生模版广告流量主ID">
                            <small>仅在圈子内展示</small>
                        </div>
                    </div>

                    <div class="am-form-group" style="margin-top:20px;">
                        <div class="am-u-sm-7 am-u-sm-push-5">
                            <button type="button" class="am-btn am-btn-secondary" style="border-radius: 5px;" onclick="holdSave();">保存设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<script src="./assets/js/base-enhanced.js"></script>
<script>
var baseConfig = {
    urls: {
        proxyBase: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        proxyResource: "<?php echo url('urge/proxy/proxy_resource'); ?>",
        repairMissing: "<?php echo url('index/repairMissing'); ?>",
        purgeCache: "<?php echo url('index/purgeCache'); ?>",
        ordinary: "<?php echo url('ordinary'); ?>",
        receipt: "<?php echo url('receipt'); ?>"
    },
    menuActive: '<?php echo $menuActive; ?>',
    menuList: '<?php echo $menuList; ?>',
    much_role: '<?php echo $much_role; ?>',
    notice: <?php echo $notice; ?>,
    vacant: <?php echo $vacant; ?>
};
BaseEnhanced.init(baseConfig);
window.retakeCache = BaseEnhanced.retakeCache;
window.getOriginalSrc = BaseEnhanced.getOriginalSrc;
</script>
<?php if($much_role=='founder'): ?>
<script>
window.updateFix = function() {
    layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
        btn: ['确定', '取消'],
        title: '提示'
    }, function() {
        // 执行修复操作
        performDatabaseRepair();
    }, function(index) {
        // 取消操作
        layer.close(index);
    });
};

function performDatabaseRepair() {
    var loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });
    $.post("<?php echo url('index/repairMissing'); ?>", function(data) {
        layer.close(loadingIndex);
        if (data !== false) {
            layer.msg('数据库缺失字段修复完成', {
                icon: 1,
                time: 1500
            }, function() {
                location.reload();
            });
        } else {
            layer.msg('修复失败，请稍后重试', {
                icon: 2,
                time: 2000
            }, function() {
                location.reload();
            });
        }
    }).error(function() {
        layer.close(loadingIndex);
        layer.msg('网络错误，修复失败', {
            icon: 2,
            time: 2000
        });
    });
}
</script>
<?php endif; ?>

<script>


    var holdSave = function () {

        var rangeData = {};
        rangeData['adstory'] = $('[name=adstory]:checked').val();
        rangeData['adsper'] = $('[name=adsper]:checked').val();
        rangeData['prePostTwig'] = $('[name=prePostTwig]:checked').val();
        rangeData['isolate'] = Number($('#isolate').val().match(/^\d+(?:\.\d{0,0})?/));
        rangeData['adunitId'] = $.trim($('#adunitId').val());
        rangeData['prePostId'] = $.trim($('#prePostId').val());
        rangeData['incentiveDuct'] = Number($('#incentiveDuct').val().match(/^\d+(?:\.\d{0,0})?/));
        rangeData['incentiveId'] = $.trim($('#incentiveId').val());
        rangeData['latticeTwig'] = $('[name=latticeTwig]:checked').val();
        rangeData['latticeId'] = $.trim($('#latticeId').val());

        $.ajax({
            type: "post",
            url: "<?php echo url('systems/proclaim'); ?>",
            data: rangeData,
            dataType: 'json',
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }
        });
    }
</script>

</body>
</html>