<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\Alternative;
use app\api\service\TmplService;
use app\common\Playful;
use app\common\Remotely;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

class People extends Base
{

    /*
     * 模板消息
     */
    public function mention()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5YWs5LyX6YCa55+l'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data['imagine_data'] = json_encode(input('post.item/a', array()), true);
            Db::startTrans();
            try {
                Db::name('wx_popular')->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $list = Db::name('wx_popular')->where('much_id', $this->much_id)->value('imagine_data');
            $this->assign('list', $list);
            return $this->fetch();
        }
    }

    /*
     * 微信配置
     */
    public function config()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5YWs5LyX6YCa55+l'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data['wx_app_qrcode'] = trim(input('post.appQrcode'));
            $data['wx_app_name'] = trim(input('post.appName'));
            $appId = trim(input('post.appId', ''));
            if ($appId !== '') {
                $data['wx_app_id'] = authcode($appId, 'ENCODE', 'YuLuoMassInfo');
            }
            $appSecret = trim(input('post.appSecret', ''));
            if ($appSecret !== '') {
                $data['wx_app_secret'] = authcode($appSecret, 'ENCODE', 'YuLuoMassInfo');
            }
            Db::startTrans();
            try {
                Db::name('wx_popular')->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $list = Db::name('wx_popular')->where('much_id', $this->much_id)->field('wx_app_qrcode,wx_app_name,wx_app_id,wx_app_secret')->find();
            $list['wx_app_id'] = authcode($list['wx_app_id'], 'DECODE', 'YuLuoMassInfo');
            $list['wx_app_secret'] = authcode($list['wx_app_secret'], 'DECODE', 'YuLuoMassInfo');
            $this->assign('domain', request()->domain());
            $this->assign('much_id', $this->much_id);
            $this->assign('list', $list);
            return $this->fetch();
        }
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, array(), 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    /*
     * 一键拨号
     */
    public function dial()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5LiA6ZSu5ouo5Y+3'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data = input('post.item/a', array());
            Db::startTrans();
            try {
                Db::name('call_phone_config')->where('much_id', $this->much_id)->update(['force_input_phone' => intval($data['forceInputPhone'])]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $cpcInfo = Db::name('call_phone_config')->where('much_id', $this->much_id)->find();
            if (!$cpcInfo) {
                $cpcInfo = ['force_input_phone' => 0, 'much_id' => $this->much_id];
                Db::startTrans();
                try {
                    $cpcInfo['id'] = Db::name('call_phone_config')->insertGetId($cpcInfo);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
            }
            $this->assign('list', $cpcInfo);
            return $this->fetch();
        }
    }

    /*
     * 点评列表
     */
    public function correct()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5YaF5a6554K56K+E'))) {
            abort(404);
        }
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('paper_review_score')
            ->where('assess_content', 'like', "%{$hazy_name}%")
            ->where('much_id', $this->much_id)
            ->orderRaw('audit_status <> 0 asc,case when audit_status = 0 then id end asc,case when audit_status <> 0 then id end desc')
            ->paginate(10, false, ['query' => ['s' => $this->defaultQuery(), 'hazy_name' => $hazy_name]])
            ->each(function ($item) {
                $paInfo = Db::name('paper')->where('id', $item['pa_id'])->where('much_id', $this->much_id)->field('study_title,study_content')->find();
                $item['study_title'] = $paInfo['study_title'];
                $item['study_content'] = $paInfo['study_content'];
                $item['user'] = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
                return $item;
            });
        $expressionHtml = function ($val) {
            return Alternative::ExpressionHtml($val);
        };
        $this->assign('expressionHtml', $expressionHtml);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
    * 点评审核
    */
    public function trialCorrect()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5YaF5a6554K56K+E'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            $prsInfo = Db::name('paper_review_score')->where('id', $fid)->where('much_id', $this->much_id)->find();
            //  判断审核状态
            if (intval($prsInfo['audit_status']) !== 0) {
                return json(['code' => 0, 'msg' => '系统繁忙，请刷新页面后重试！']);
            }
            $data['audit_status'] = intval(input('post.process', 0));
            //  审核拒绝
            if ($data['audit_status'] === 2) {
                $data['audit_reason'] = trim(input('post.inject', ''));
            }
            Db::startTrans();
            try {
                //  更改审核状态
                Db::name('paper_review_score')->where('id', $fid)->where('audit_status', 0)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($data['audit_status'] >= 1 && $data['audit_status'] <= 2) {
                $tmplService = new TmplService();
                //  点评审核通过
                switch ($data['audit_status']) {
                    case 1:
                        //  被点评用户
                        $paInfo = Db::name('paper')->where('id', $prsInfo['pa_id'])->where('much_id', $this->much_id)->field('tory_id,user_id,study_type')->find();
                        $tmplService->add_template([
                            'much_id' => $this->much_id,
                            'at_id' => 'YL0009',
                            'user_id' => $paInfo['user_id'],
                            'page' => 'yl_welore/pages/packageA/article/index?id=' . $paInfo['id'] . '&type=' . $paInfo['study_type'],
                            'keyword1' => '您的帖子有新的点评！',
                            'keyword4' => date('Y年m月d日 H:i:s', time()),
                        ]);
                        Db::name('user_smail')->insert(['user_id' => $paInfo['user_id'], 'maring' => "您的帖子有新的点评！", 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                        //  点评用户
                        $tmplService->add_template([
                            'much_id' => $this->much_id,
                            'at_id' => 'YL0009',
                            'user_id' => $prsInfo['user_id'],
                            'page' => 'yl_welore/pages/user_smail/index',
                            'keyword1' => "您的点评已通过审核！",
                            'keyword4' => date('Y年m月d日 H:i:s', time()),
                        ]);
                        Db::name('user_smail')->insert(['user_id' => $prsInfo['user_id'], 'maring' => "您的点评已通过审核！", 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                        break;
                    case 2:
                        $tmplService->add_template([
                            'much_id' => $this->much_id,
                            'at_id' => 'YL0009',
                            'user_id' => $prsInfo['user_id'],
                            'page' => 'yl_welore/pages/user_smail/index',
                            'keyword1' => "很抱歉，您的点评未通过审核！",
                            'keyword4' => date('Y年m月d日 H:i:s', time()),
                        ]);
                        Db::name('user_smail')->insert(['user_id' => $prsInfo['user_id'], 'maring' => "很抱歉，您的点评未通过审核，拒绝原因：{$data['audit_reason']}", 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                        break;
                }
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 新增点评
     */
    public function newCorrect()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5YaF5a6554K56K+E'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data['pa_id'] = input('post.pid');
            $paInfo = Db::name('paper')->where('id', $data['pa_id'])->where('much_id', $this->much_id)->field('tory_id,user_id,study_type')->find();
            if (!$paInfo) {
                return json(['code' => 0, 'msg' => '帖子信息错误，code：1！']);
            }
            $data['tory_id'] = $paInfo['tory_id'];
            $data['ne_id'] = Db::name('territory')->where('id', $data['tory_id'])->where('much_id', $this->much_id)->value('needle_id');
            if (!$data['ne_id']) {
                return json(['code' => 0, 'msg' => '帖子信息错误，code：2！']);
            }
            $data['user_id'] = intval(input('post.uid'));
            $userInfo = Db::name('user')->where('id', $data['user_id'])->where('much_id', $this->much_id)->find();
            if (!$userInfo) {
                return json(['code' => 0, 'msg' => '用户UID填写有误，请检查后重试！']);
            }
            $prsCount = Db::name('paper_review_score')->where('pa_id', $data['pa_id'])->where('user_id', $data['user_id'])->where('much_id', $this->much_id)->count();
            if ($prsCount > 0) {
                return json(['code' => 0, 'msg' => '该用户已点评，请勿重复点评！']);
            }
            $data['assess_score'] = input('post.assessScore');
            $data['assess_content'] = emoji_encode($this->safe_html(input('post.assessContent')));
            $data['assess_time'] = time();
            $data['is_show'] = input('post.isShow');
            $data['audit_status'] = 1;
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('paper_review_score')->insert($data);
                Db::name('user_smail')->insert(['user_id' => $paInfo['user_id'], 'maring' => '您的帖子有新的点评', 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            $tmplService = new TmplService();
            $tmplService->add_template([
                'much_id' => $this->much_id,
                'at_id' => 'YL0009',
                'user_id' => $paInfo['user_id'],
                'page' => 'yl_welore/pages/packageA/article/index?id=' . $paInfo['id'] . '&type=' . $paInfo['study_type'],
                'keyword1' => '您的帖子有新的点评',
                'keyword4' => date('Y年m月d日 H:i:s', time()),
            ]);
            return json(['code' => 1, 'msg' => '点评成功！']);
        } else {
            $fid = intval(input('get.fid', 0));
            if ($fid === 0) {
                abort(404);
            }
            $pCount = Db::name('paper')->where('id', $fid)->where('much_id', $this->much_id)->count();
            if ($pCount < 1) {
                abort(404);
            }
            $umrList = Db::name('user_maker')->where('much_id', $this->much_id)->select();
            $userList = array();
            for ($i = 0; $i < count($umrList); $i++) {
                $userList[] = Db::name('user')->where('user_wechat_open_id', $umrList[$i]['user_open_id'])->where('much_id', $this->much_id)->field('id,user_nick_name,user_wechat_open_id')->find();
            }
            $this->assign('fid', $fid);
            $this->assign('userList', $userList);
            return $this->fetch();
        }
    }

    /*
     * 删除点评
     */
    public function delCorrect()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5YaF5a6554K56K+E'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            Db::startTrans();
            try {
                Db::name('paper_review_score')->where('id', $fid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 常用语句
     */
    public function often()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5YaF5a6554K56K+E'))) {
            abort(404);
        }
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('paper_review_common_terms')
            ->where('common_content', 'like', "%{$hazy_name}%")
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $this->defaultQuery(), 'hazy_name' => $hazy_name]])
            ->each(function ($item) {
                $item['user'] = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 删除常用语句
     */
    public function delOften()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5YaF5a6554K56K+E'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            Db::startTrans();
            try {
                Db::name('paper_review_common_terms')->where('id', $fid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 点评设置
     */
    public function setup()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5YaF5a6554K56K+E'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data = input('post.item/a', array());
            Db::startTrans();
            try {
                Db::name('paper_review_config')->where('much_id', $this->much_id)->update(['is_auto_audit' => intval($data['isAutoAudit']), 'is_all_review' => intval($data['isAllReview'])]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $prcInfo = Db::name('paper_review_config')->where('much_id', $this->much_id)->find();
            if (!$prcInfo) {
                $prcInfo = ['is_auto_audit' => 0, 'is_all_review' => 0, 'much_id' => $this->much_id];
                Db::startTrans();
                try {
                    $prcInfo['id'] = Db::name('paper_review_config')->insertGetId($prcInfo);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
            }
            $this->assign('list', $prcInfo);
            return $this->fetch();
        }
    }

    /*
     * 失物招领分类
     */
    public function lost_type()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $list = Db::name('lost_item_type')
            ->where('name', 'like', "%{$hazy_name}%")
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->order('sort', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 失物招领分类排序
     */
    public function lost_type_type_sort()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $sort = request()->post('dalue');
            Db::startTrans();
            try {
                Db::name('lost_item_type')->where('id', $syid)->where('much_id', $this->much_id)->update(['sort' => $sort]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 新增失物招领分类
     */
    public function new_lost_type()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data['name'] = trim(input('post.name'));
            $data['sort'] = intval(input('post.sort'));
            $data['status'] = intval(input('post.status'));
            $data['create_time'] = time();
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('lost_item_type')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            return $this->fetch();
        }
    }

    /*
     * 编辑失物招领分类
     */
    public function edit_lost_type()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $data['name'] = trim(input('post.name'));
            $data['sort'] = intval(input('post.sort'));
            $data['status'] = intval(input('post.status'));
            Db::startTrans();
            try {
                Db::name('lost_item_type')->where('id', $fid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $fid = intval(input('get.fid'));
            $litInfo = Db::name('lost_item_type')->where('id', $fid)->where('much_id', $this->much_id)->find();
            if ($litInfo) {
                $this->assign('list', $litInfo);
                return $this->fetch();
            } else {
                abort(404);
            }
        }
    }

    /*
     * 删除失物招领
     */
    public function del_lost_type()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            Db::startTrans();
            try {
                Db::name('lost_item_type')->where('id', $fid)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     *  失物招领列表
     */
    public function lost_found()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        $fid = intval(input('get.fid', 0));
        $when = array();
        //  如果fid不等于0 则查询
        $fid !== 0 && $when['id'] = $fid;
        $hazy_name = trim(input('get.hazy_name', ''));
        $page = intval(input('get.page', 1));
        $list = Db::name('lost_item')
            ->where($when)
            ->where('item_name|item_detail', 'like', "%{$hazy_name}%")
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->orderRaw('audit_status <> 0 asc,case when audit_status = 0 then id end asc,case when audit_status <> 0 then id end desc')
            ->field('item_detail,lost_address', true)
            ->paginate(10, false, ['query' => ['s' => $this->defaultQuery(), 'hazy_name' => $hazy_name]])
            ->each(function ($item) {
                $item['type_name'] = Db::name('lost_item_type')->where('id', $item['item_type'])->where('much_id', $this->much_id)->value('name');
                $item['user'] = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 查看失物招领详情
     */
    public function lost_found_detail()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        $fid = intval(input('get.fid'));
        $liInfo = Db::name('lost_item')->where('id', $fid)->where('much_id', $this->much_id)->find();
        if ($liInfo) {
            $liInfo['type_name'] = Db::name('lost_item_type')->where('id', $liInfo['item_type'])->where('much_id', $this->much_id)->value('name');
            $liInfo['user'] = Db::name('user')->where('id', $liInfo['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
            $expressionHtml = function ($val) {
                return Alternative::ExpressionHtml($val);
            };
            $this->assign('expressionHtml', $expressionHtml);
            $this->assign('list', $liInfo);
            return $this->fetch();
        } else {
            abort(404);
        }
    }

    /*
     * 失物招领审核
     */
    public function trial_lost_found()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            $liInfo = Db::name('lost_item')->where('id', $fid)->where('much_id', $this->much_id)->find();
            //  判断审核状态
            if (intval($liInfo['audit_status']) !== 0) {
                return json(['code' => 0, 'msg' => '系统繁忙，请刷新页面后重试！']);
            }
            $data['audit_status'] = intval(input('post.process', 0));
            //  审核拒绝
            if ($data['audit_status'] === 2) {
                $data['audit_reason'] = trim(input('post.inject', ''));
            }
            Db::startTrans();
            try {
                //  更改审核状态
                Db::name('lost_item')->where('id', $fid)->where('audit_status', 0)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($data['audit_status'] >= 1 && $data['audit_status'] <= 2) {
                $tmplService = new TmplService();
                $templateData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $liInfo['user_id'],
                    'page' => 'yl_welore/pages/user_smail/index',
                    'keyword4' => date('Y年m月d日 H:i:s', time()),
                ];
                $maringText = '';
                //  失物招领审核通过
                switch ($data['audit_status']) {
                    case 1:
                        $templateData['keyword1'] = "您发布的失物招领信息已通过审核";
                        $maringText = "{$templateData['keyword1']}！";
                        break;
                    case 2:
                        $templateData['keyword1'] = "您发布的失物招领信息未通过审核";
                        $maringText = "很抱歉，您发布的失物招领信息未通过审核，拒绝原因：{$data['audit_reason']}";
                        break;
                }
                //  模板消息
                $tmplService->add_template($templateData);
                //  站内信
                Db::name('user_smail')->insert(['user_id' => $liInfo['user_id'], 'maring' => $maringText, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     *  删除失物招领信息
     */
    public function del_lost_found()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            Db::startTrans();
            try {
                Db::name('lost_item')->where('id', $fid)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 失物招领回复
     */
    public function lost_reply()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        $hazy_name = trim(input('get.hazy_name', ''));
        $page = request()->get('page', 1);
        $list = Db::name('lost_item_reply')
            ->where('content', 'like', "%{$hazy_name}%")
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->orderRaw('audit_status <> 0 asc,case when audit_status = 0 then id end asc,case when audit_status <> 0 then id end desc')
            ->paginate(10, false, ['query' => ['s' => $this->defaultQuery(), 'hazy_name' => $hazy_name]])
            ->each(function ($item) {
                $item['li_name'] = Db::name('lost_item')->where('id', $item['li_id'])->where('much_id', $this->much_id)->value('item_name');
                $item['user'] = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
                return $item;
            });
        $expressionHtml = function ($val) {
            return Alternative::ExpressionHtml($val);
        };
        $this->assign('expressionHtml', $expressionHtml);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 失物招领回复审核
     */
    public function trial_lost_reply()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            $lirInfo = Db::name('lost_item_reply')->where('id', $fid)->where('much_id', $this->much_id)->find();
            //  判断审核状态
            if (intval($lirInfo['audit_status']) !== 0) {
                return json(['code' => 0, 'msg' => '系统繁忙，请刷新页面后重试！']);
            }
            $data['audit_status'] = intval(input('post.process', 0));
            //  审核拒绝
            if ($data['audit_status'] === 2) {
                $data['audit_reason'] = trim(input('post.inject', ''));
            }
            Db::startTrans();
            try {
                //  更改审核状态
                Db::name('lost_item_reply')->where('id', $fid)->where('audit_status', 0)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($data['audit_status'] >= 1 && $data['audit_status'] <= 2) {
                $tmplService = new TmplService();
                $templateData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $lirInfo['user_id'],
                    'page' => 'yl_welore/pages/user_smail/index',
                    'keyword4' => date('Y年m月d日 H:i:s', time()),
                ];
                $maringText = '';
                //  失物招领审核通过
                switch ($data['audit_status']) {
                    case 1:
                        $templateData['keyword1'] = "您回复失物招领的信息已通过审核";
                        $maringText = "{$templateData['keyword1']}！";
                        break;
                    case 2:
                        $templateData['keyword1'] = "您回复失物招领的信息未通过审核";
                        $maringText = "很抱歉，您发布的失物招领信息未通过审核，拒绝原因：{$data['audit_reason']}";
                        break;
                }
                //  模板消息
                $tmplService->add_template($templateData);
                //  站内信
                Db::name('user_smail')->insert(['user_id' => $lirInfo['user_id'], 'maring' => $maringText, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 删除失物招领回复信息
     */
    public function del_lost_reply()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            Db::startTrans();
            try {
                Db::name('lost_item_reply')->where('id', $fid)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 失物招领置顶列表
     */
    public function lost_top()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        $page = request()->get('page', 1);
        $list = Db::name('lost_item_top')
            ->alias('lit')
            ->join('lost_item li', 'lit.li_id = li.id')
            ->join('user u', 'lit.user_id = u.id')
            ->where('lit.is_pay', 1)
            ->whereRaw('lit.add_time + (lit.top_day * 86400) > ' . time())
            ->where('lit.much_id', $this->much_id)
            ->field('lit.li_id, COUNT(lit.li_id) as li_count, SUM(lit.top_day) as total_top_day, lit.pay_type, li.item_name as li_name, li.top_time as li_top_time, u.user_nick_name, u.user_wechat_open_id, u.uvirtual')
            ->group('lit.li_id')
            ->order('lit.id', 'desc')
            ->paginate(10, false, ['query' => ['s' => $this->defaultQuery()]])
            ->each(function ($item) {
                $item['user'] = [
                    'user_nick_name' => $item['user_nick_name'],
                    'user_wechat_open_id' => $item['user_wechat_open_id'],
                    'uvirtual' => $item['uvirtual'],
                ];
                $item['pay_info'] = '';
                $litPrice = array();
                for ($i = 0; $i < 3; $i++) {
                    $litPrice[] = Db::name('lost_item_top')->where('li_id', $item['li_id'])->where('pay_type', $i)->whereRaw('add_time + (top_day * 86400) > ' . time())->where('much_id', $this->much_id)->sum('pay_price');
                }
                $getDescription = function ($key) {
                    switch ($key) {
                        case 0:
                            return ' ( 贝壳 ) ';
                        case 1:
                            return ' ( 积分 ) ';
                        case 2:
                            return ' ( 微信支付 ) ';
                        default:
                            return '';
                    }
                };
                $str = '';
                foreach ($litPrice as $key => $value) {
                    if ($value != 0) {
                        $str .= ' ' . $value . $getDescription($key) . '+';
                    }
                }
                $item['pay_info'] = rtrim($str, '+');
                $item['add_time'] = Db::name('lost_item_top')->where('li_id', $item['li_id'])->order(['id' => 'asc'])->where('much_id', $this->much_id)->value('add_time');
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 失物招领取消置顶
     */
    public function lost_cancel_top()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $hid = intval(input('post.hid'));
            Db::startTrans();
            try {
                Db::name('lost_item')->where('id', $hid)->where('much_id', $this->much_id)->update(['top_time' => time()]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 失物招领配置
     */
    public function lost_config()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5aSx54mp5oub6aKG'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data = input('post.item/a', array());
            Db::startTrans();
            try {
                Db::name('lost_item_config')->where('much_id', $this->much_id)->update(['is_auto_audit' => intval($data['isAutoAudit']), 'reply_is_auto_audit' => intval($data['replyIsAutoAudit']), 'top_twig' => intval($data['topTwig']), 'price_type' => intval($data['priceType']), 'top_price' => floatval($data['topPrice']), 'help_document' => $this->safe_html($data['helpDocument'])]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $prcInfo = Db::name('lost_item_config')->where('much_id', $this->much_id)->find();
            if (!$prcInfo) {
                $prcInfo = ['is_auto_audit' => 0, 'reply_is_auto_audit' => 0, 'top_twig' => 0, 'top_price' => 0.00, 'price_type' => '', 'help_document' => '', 'much_id' => $this->much_id];
                Db::startTrans();
                try {
                    $prcInfo['id'] = Db::name('lost_item_config')->insertGetId($prcInfo);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
            }
            $this->assign('list', $prcInfo);
            return $this->fetch();
        }
    }

}