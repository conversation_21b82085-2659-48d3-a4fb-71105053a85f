{extend name="/base"/}
{block name="main"}
<style>
    /* 简洁现代的页面样式 */
    .tpl-portlet-components {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        padding: 24px;
        margin: 0;
    }

    /* 标题区域 */
    .portlet-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 16px;
        margin-bottom: 24px;
        border-bottom: 1px solid #eee;
    }

    .caption {
        font-size: 18px;
        color: #333;
        font-weight: 500;
        display: flex;
        align-items: center;
    }

    .caption .am-icon-map-marker {
        margin-right: 8px;
        color: #666;
    }

    /* 新增按钮 */
    .customize-span {
        display: inline-flex;
        align-items: center;
        padding: 8px 16px;
        background: #1890ff;
        color: white !important;
        border-radius: 4px;
        text-decoration: none;
        font-size: 14px;
        transition: background 0.3s;
    }

    .customize-span:hover {
        background: #40a9ff;
        color: white !important;
        text-decoration: none;
    }

    .customize-span .am-icon-plus {
        margin-right: 4px;
    }

    /* 搜索区域 */
    #search-list {
        background: #fafafa;
        border-radius: 4px;
        padding: 16px;
        margin-bottom: 16px;
        border: 1px solid #f0f0f0;
    }

    .search-label {
        font-weight: normal;
        color: #666;
        margin-bottom: 4px;
        display: block;
        font-size: 14px;
    }

    .search-input {
        padding: 4px 11px;
        height: 36px;
        width: 200px;
        font-size: 14px;
        transition: border-color 0.3s;
    }

    .search-input:focus {
        border-color: #1890ff;
        outline: none;
        box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
    }

    .search-btn {
        background: #1890ff;
        color: white;
        border: 1px solid #1890ff;
        border-radius: 4px;
        padding: 4px 15px;
        margin-top: 12px !important;
        height: 32px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        gap: 4px;
    }

    .search-btn:hover {
        background: #40a9ff;
        border-color: #40a9ff;
    }

    /* 表格样式 */
    .table-main {
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        overflow: hidden;
    }

    .table-main thead th {
        background: #fafafa;
        color: #333;
        font-weight: 500;
        font-size: 14px;
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        text-align: center;
    }

    .table-main tbody td {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        font-size: 14px;
        text-align: center;
    }

    .table-main tbody tr:hover {
        background-color: #fafafa;
    }

    .table-main tbody tr:last-child td {
        border-bottom: none;
    }

    /* 操作按钮 */
    .am-btn-xs {
        padding: 4px 8px;
        font-size: 12px;
        border-radius: 4px;
        margin-right: 8px;
        transition: all 0.3s;
        border: 1px solid #d9d9d9;
        background: white;
    }

    .am-text-secondary {
        color: #666;
        border-color: #d9d9d9;
    }

    .am-text-secondary:hover {
        color: #1890ff;
        border-color: #1890ff;
    }

    .am-text-danger {
        color: #ff4d4f;
        border-color: #ffccc7;
    }

    .am-text-danger:hover {
        color: #ff7875;
        border-color: #ff4d4f;
    }

    /* 分页 */
    .am-pagination > li > a {
        border-radius: 4px;
        margin: 0 4px;
        border: 1px solid #d9d9d9;
        color: #666;
    }

    .am-pagination > li > a:hover {
        color: #1890ff;
        border-color: #1890ff;
    }

    .am-pagination > .am-active > a {
        background-color: #1890ff;
        border-color: #1890ff;
        color: white;
    }
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-map-marker"></span> 附近圈子列表
        </div>
        <div>
            <a href="{:url('vicinity/new_located')}" class="customize-span">
                <span class="am-icon-plus"></span> 新增圈子位置
            </a>
        </div>
    </div>
    <div class="tpl-block">
        <div id="search-list" style="display: flex;flex-wrap: wrap;width: 100%;min-height: 38px;">
            <div class="search-wide-pitch search-inline">
                <label class="search-label">圈子名称</label>
                <div class="search-input-inline">
                    <input type="text" name="toryName" value="{$tory_name}" class="search-input">
                </div>
            </div>
            <button class="search-wide-pitch search-btn" onclick="turtle();">
                <i class="am-icon-search"></i> 搜 索
            </button>
        </div>
        <div class="am-g" style="margin-top: 15px;">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th class="text-center" width="25%">圈子名称</th>
                            <th class="text-center" width="25%">经度</th>
                            <th class="text-center" width="25%">纬度</th>
                            <th class="text-center" width="25%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td style="width: 20%">
                                <div style="display: flex;justify-content: center;align-items: center;height: 42px;">
                                    {$vo.realm_name}
                                </div>
                            </td>
                            <td style="width: 20%">
                                <div style="display: flex;justify-content: center;align-items: center;height: 42px;">
                                    {$vo.longitude}
                                </div>
                            </td>
                            <td style="width: 20%">
                                <div style="display: flex;justify-content: center;align-items: center;height: 42px;">
                                    {$vo.latitude}
                                </div>
                            </td>
                            <td style="width: 20%">
                                <div style="display: flex;justify-content: center;align-items: center;height: 42px;gap: 8px;">
                                    <button type="button" class="am-btn am-btn-xs am-text-secondary" onclick="editInfo('{$vo.id}');" title="编辑位置信息">
                                        <span class="am-icon-pencil-square-o"></span> 编辑
                                    </button>
                                    <button type="button" class="am-btn am-btn-xs am-text-danger am-hide-sm-only" onclick="deleteInfo('{$vo.id}')" title="删除位置信息">
                                        <span class="am-icon-trash-o"></span> 删除
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    function turtle() {
        var searchInput = $('#search-list .search-input');
        var n = 0;
        for (var i = 0; i < searchInput.length; i++) {
            if (searchInput[i].value.trim() !== '') {
                n++;
            }
        }
        if (n > 0) {
            location.href = `{:url('vicinity/located_list')}&${searchInput.serialize()}&page={$page}`;
        } else {
            location.href = "{:url('vicinity/located_list')}&page={$page}";
        }
    }

    function editInfo(fid) {
        location.href = "{:url('vicinity/edit_located')}&fid=" + fid;
    }

    function deleteInfo(fid) {
        layer.confirm('您确定要删除当前圈子位置吗', {
            btn: ['确定', '取消']
        }, function () {
            $.post("{:url('vicinity/del_located')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

</script>
{/block}
