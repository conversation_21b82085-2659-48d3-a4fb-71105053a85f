<?php

namespace app\api\controller;

use app\common\Remotely;
use think\Db;

class Plugunit
{
    public function get_plug()
    {
        $data = input('param.');
        //  查询数据
        $petalOldData = Db::name('template_slot_valve')->where('much_id', $data['much_id'])->value('petal_data');
        //  解密数据
        $petalNewData = json_decode(authcode($petalOldData, 'DECODE', base64_decode('WXVMdW9QbHVnaW4=')), true);
        //$key = Remotely::readNotch();
        //$item = $key['current_stream']['un_lock_ids'];
        foreach ($petalNewData as $k => $v) {
            $petalNewData[$k] = md5($v);
        }

        return base64_encode(rawurlencode(json_encode($petalNewData)));
    }

    public function check_plug($key, $much_id)
    {
        $return = false;
        $item = Remotely::readNotch()['current_stream']['un_lock_ids'];
        //dump($item);
        foreach ($item as $k => $v) {
            if ($v == $key) {
                $return = true;
            }
        }
        //  查询数据
        $petalOldData = Db::name('template_slot_valve')->where('much_id', $much_id)->find();
        $petalNewData = json_decode(authcode($petalOldData['petal_data'], 'DECODE', base64_decode('WXVMdW9QbHVnaW4=')), true);

        $in = in_array($key, $petalNewData);
        //查询关闭的插件
        if ($petalOldData['is_reversal'] == 0) {
            if ($in) {
                $return = false;
            } else {
                $return = true;
            }
        } else {
            if ($in) {
                $return = true;
            } else {
                $return = false;
            }
        }

        return $return;
    }
}