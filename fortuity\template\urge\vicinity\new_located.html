{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        padding: 40px;
    }

    .portlet-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 25px;
        margin-bottom: 35px;
        border-bottom: 2px solid #f5f5f5;
    }

    .caption {
        font-size: 20px;
        color: #23b7e5;
        font-weight: 600;
    }

    .caption .am-icon-plus {
        margin-right: 8px;
        font-size: 18px;
    }

    .am-form-horizontal .am-form-group {
        margin: 45px 0;
    }

    .am-form-horizontal .am-form-label {
        text-align: right;
        font-weight: 600;
        padding-top: 12px;
        font-size: 15px;
        color: #333;
    }

    .am-form-horizontal .am-form-group .am-u-sm-9 {
        padding-left: 25px;
    }

    .am-form-horizontal input[type='text'] {
        border-radius: 6px;
        border: 1px solid #ddd;
        padding: 12px 15px;
        transition: all 0.3s;
        width: 100%;
        height: 48px;
        font-size: 14px;
    }

    .am-form-horizontal input[type='text']:focus {
        border-color: #23b7e5;
        box-shadow: 0 0 0 2px rgba(35,183,229,0.1);
    }

    .el-input__inner {
        border-radius: 6px;
        border: 1px solid #ddd;
        padding: 12px 15px;
        transition: all 0.3s;
        background: #fff !important;
        height: 48px;
        font-size: 14px;
    }

    .el-input__inner:focus {
        border-color: #23b7e5;
        box-shadow: 0 0 0 3px rgba(35,183,229,0.1);
    }

    .am-btn {
        padding: 12px 30px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.3s;
        margin-right: 15px;
    }

    .am-btn-primary {
        background-color: #23b7e5;
        border-color: #23b7e5;
        min-width: 120px;
    }

    .am-btn-primary:hover {
        background-color: #49c5ec;
        border-color: #49c5ec;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(35,183,229,0.3);
    }

    .am-btn-default {
        background-color: #f8f9fa;
        border-color: #ddd;
        color: #666;
        min-width: 100px;
    }

    .am-btn-default:hover {
        background-color: #e9ecef;
        border-color: #ccc;
        transform: translateY(-1px);
    }

    .required {
        color: #e74c3c;
        margin-left: 4px;
        font-weight: bold;
    }

    .error-message {
        color: #e74c3c;
        font-size: 13px;
        margin-top: 8px;
        padding: 8px 12px;
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 4px;
        display: flex;
        align-items: center;
    }

    .error-message::before {
        content: "⚠";
        margin-right: 6px;
        font-weight: bold;
    }

    .help-text {
        color: #666;
        font-size: 13px;
        margin-top: 8px;
        padding: 6px 0;
        line-height: 1.4;
    }

    .has-error input,
    .has-error .el-input__inner {
        border-color: #e74c3c;
        box-shadow: 0 0 0 3px rgba(231,76,60,0.1);
    }

    /* 按钮组间距 */
    .am-form-group:last-child {
        margin-top: 50px;
        padding-top: 30px;
        border-top: 1px solid #f0f0f0;
    }

    /* 禁用状态 */
    .am-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: none !important;
    }

    /* 坐标获取链接样式 */
    .coordinate-links {
        margin-top: 8px;
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .coord-link {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        color: #495057;
        text-decoration: none;
        font-size: 12px;
        transition: all 0.3s;
    }

    .coord-link:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
        color: #23b7e5;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .coord-link .am-icon-map-marker,
    .coord-link .am-icon-location-arrow {
        margin-right: 4px;
        font-size: 12px;
    }

    @media (max-width: 768px) {
        .coordinate-links {
            flex-direction: column;
            gap: 8px;
        }

        .coord-link {
            justify-content: center;
        }
    }
</style>
<div id="app" class="tpl-portlet-components" v-cloak>
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-plus"></span> 新增圈子位置
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-8 am-u-md-offset-2">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            圈子名称<span class="required">*</span>
                        </label>
                        <div class="am-u-sm-9" :class="{'has-error': errors.toryId}">
                            <el-select
                                class="w-100"
                                v-model="item.toryId"
                                filterable
                                placeholder="请选择要设置位置的圈子">
                                <el-option v-for="tory in toryOptions" :key="tory.id" :label="tory.realm_name" :value="tory.id"></el-option>
                            </el-select>
                            <div v-if="errors.toryId" class="error-message">{{errors.toryId}}</div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            圈子经度<span class="required">*</span>
                        </label>
                        <div class="am-u-sm-9" :class="{'has-error': errors.longitude}">
                            <input
                                type="text"
                                v-model="item.longitude"
                                placeholder="请输入圈子的经度坐标">
                            <div class="help-text">经度范围：-180 到 180，例如：116.40</div>
                            <div class="coordinate-links">
                                <a href="https://lbs.amap.com/tools/picker" target="_blank" class="coord-link">
                                    <span class="am-icon-map-marker"></span> 高德地图获取坐标
                                </a>
                                <a href="https://api.map.baidu.com/lbsapi/getpoint/index.html" target="_blank" class="coord-link">
                                    <span class="am-icon-location-arrow"></span> 百度地图获取坐标
                                </a>
                            </div>
                            <div v-if="errors.longitude" class="error-message">{{errors.longitude}}</div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            圈子纬度<span class="required">*</span>
                        </label>
                        <div class="am-u-sm-9" :class="{'has-error': errors.latitude}">
                            <input
                                type="text"
                                v-model="item.latitude"
                                placeholder="请输入圈子的纬度坐标">
                            <div class="help-text">纬度范围：-90 到 90，例如：39.92</div>
                            <div class="coordinate-links">
                                <a href="https://lbs.amap.com/tools/picker" target="_blank" class="coord-link">
                                    <span class="am-icon-map-marker"></span> 高德地图获取坐标
                                </a>
                                <a href="https://api.map.baidu.com/lbsapi/getpoint/index.html" target="_blank" class="coord-link">
                                    <span class="am-icon-location-arrow"></span> 百度地图获取坐标
                                </a>
                            </div>
                            <div v-if="errors.latitude" class="error-message">{{errors.latitude}}</div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-offset-3">
                            <button
                                type="button"
                                class="am-btn am-btn-primary"
                                @click="holdSave"
                                :disabled="isLoading">
                                {{isLoading ? '保存中...' : '保存'}}
                            </button>
                            <button
                                type="button"
                                class="am-btn am-btn-default"
                                onclick="history.back()"
                                :disabled="isLoading">
                                返回
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    var vm = new Vue({
        el: '#app',
        data() {
            return {
                item: {
                    toryId: '',
                    longitude: '',
                    latitude: ''
                },
                toryOptions: [],
                isLoading: false,
                errors: {
                    toryId: '',
                    longitude: '',
                    latitude: ''
                }
            }
        },
        created() {
            // 初始化圈子选项
            this.toryOptions = JSON.parse(decodeURIComponent(atob('{$toryListData}')));
        },
        methods: {
            // 清除错误信息
            clearErrors: function() {
                this.errors = {
                    toryId: '',
                    longitude: '',
                    latitude: ''
                };
            },

            // 表单验证
            validateForm: function() {
                this.clearErrors();
                let isValid = true;

                // 验证圈子选择
                if (!this.item.toryId) {
                    this.errors.toryId = '请选择圈子';
                    isValid = false;
                }

                // 验证经度
                const longitude = $.trim(this.item.longitude);
                if (!longitude) {
                    this.errors.longitude = '请输入圈子经度';
                    isValid = false;
                } else if (isNaN(longitude) || longitude < -180 || longitude > 180) {
                    this.errors.longitude = '经度必须是-180到180之间的数字';
                    isValid = false;
                }

                // 验证纬度
                const latitude = $.trim(this.item.latitude);
                if (!latitude) {
                    this.errors.latitude = '请输入圈子纬度';
                    isValid = false;
                } else if (isNaN(latitude) || latitude < -90 || latitude > 90) {
                    this.errors.latitude = '纬度必须是-90到90之间的数字';
                    isValid = false;
                }

                return isValid;
            },

            // 保存数据
            holdSave: function () {
                if (!this.validateForm()) {
                    return;
                }

                if (this.isLoading) {
                    return;
                }

                this.isLoading = true;
                var setData = JSON.parse(JSON.stringify(this.item));

                $.post("{:url('vicinity/new_located')}", {
                    tory_id: setData.toryId,
                    longitude: setData.longitude,
                    latitude: setData.latitude
                }, (data) => {
                    this.isLoading = false;
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1500}, function () {
                            location.href = "{:url('vicinity/located_list')}";
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2500});
                    }
                }, 'json').fail(() => {
                    this.isLoading = false;
                    layer.msg('网络错误，请稍后重试', {icon: 5, time: 2500});
                });
            }
        }
    });
</script>
{/block}
