<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\common\NetDiskService;
use app\common\Playful;
use app\common\Remotely;
use think\Cache;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

#网盘
class Cloud extends Base
{

    /*
     * 文件列表
     */
    public function files()
    {
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('netdisc')
            ->where('file_md5', 'like', "%{$hazy_name}%")
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate(15, false, ['query' => ['s' => $this->defaultQuery(), 'hazy_name' => $hazy_name]])
            ->each(function ($item) {
                $item['user'] = Db::name('user')->where('id', $item['up_user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
                return $item;
            });
        $ncSum = Db::name('netdisc')->where('much_id', $this->much_id)->sum('file_size');
        $this->assign('list', $list);
        $this->assign('ncSum', $ncSum);
        $this->assign('hazy_name', $hazy_name);
        $setupSize = function ($val) {
            return $this->setupSize($val);
        };
        $this->assign('setupSize', $setupSize);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 计算文件大小
     */
    private function setupSize($fileSize)
    {
        $size = sprintf("%u", $fileSize);
        if ($size == 0) {
            return ("0 Bytes");
        }
        $sizeName = array(" Bytes", " KB", " MB", " GB", " TB", " PB", " EB", " ZB", " YB");
        return round($size / pow(1024, ($i = floor(log($size, 1024)))), 2) . $sizeName[$i];
    }

    /*
     * 文件状态改变
     */
    public function fileUpdateStatus()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = input('post.fid', 0);
            $status = input('post.status', 1);
            Db::startTrans();
            try {
                Db::name('netdisc')->where('id', $fid)->where('much_id', $this->much_id)->update(['file_status' => $status]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 文件保存列表
     */
    public function fileSaves()
    {
        $fid = request()->get('fid', 0);
        $page = request()->get('page', 1);
        $list = Db::name('netdisc_belong')
            ->where('nc_id', $fid)
            ->where('is_dir', 0)
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate(15, false, ['query' => ['s' => $this->defaultQuery(), 'fid' => $fid]])
            ->each(function ($item) {
                $item['user'] = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
                $item['ncInfo'] = Db::name('netdisc')->where('id', $item['nc_id'])->where('much_id', $this->much_id)->field('file_suffix')->find();
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 用户文件删除
     */
    public function delUserFileInfo()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = input('post.fid', 0);
            $nbInfo = Db::name('netdisc_belong')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->find();
            if (!$nbInfo) {
                return json(['code' => 0, 'msg' => '操作失败，请刷新页面后重试！']);
            }
            Db::startTrans();
            try {
                Db::name('netdisc_belong')->where('id', $fid)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                if (intval($nbInfo['is_dir']) === 1) {
                    Db::name('netdisc_belong')->where('parent_path_id', $fid)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 用户文件列表
     */
    public function personal()
    {
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('user')
            ->where('user_nick_name|user_wechat_open_id', 'like', "%{$hazy_name}%")
            ->where('tourist', 0)
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->field('id as uid,user_nick_name,user_wechat_open_id,uvirtual')
            ->paginate(15, false, ['query' => ['s' => $this->defaultQuery(), 'hazy_name' => $hazy_name]])
            ->each(function ($item) {
                $netdiscUserVolume = Db::name('netdisc_user_volume')->where('user_id', $item['uid'])->where('much_id', $this->much_id)->find();
                if ($netdiscUserVolume) {
                    $item = array_merge($item, $netdiscUserVolume);
                }
                $item['total_volume'] = Db::name('netdisc_user_volume')->where('user_id', $item['user_id'])->where('exp_time', ['=', 0], ['>', time()], 'or')->where('much_id', $this->much_id)->sum('quota_size');
                $item['quota_size'] = Db::name('netdisc_user_volume')->where('user_id', $item['user_id'])->where('exp_time', 0)->where('much_id', $this->much_id)->value('quota_size');
                $ngList = Db::name('netdisc_belong')->where('user_id', $item['uid'])->where('is_dir', 0)->where('is_del', 0)->where('much_id', $this->much_id)->field('nc_id')->select();
                $ncIds = array();
                foreach ($ngList as $items) {
                    $ncIds[] = $items['nc_id'];
                }
                $item['use_file_size'] = Db::name('netdisc')->whereIn('id', $ncIds)->where('much_id', $this->much_id)->sum('file_size');
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $setupSize = function ($val) {
            return $this->setupSize($val);
        };
        $this->assign('setupSize', $setupSize);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 开通网盘
     */
    public function openNetDisc()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            $userInfo = Db::name('user')->where('id', $fid)->where('much_id', $this->much_id)->find();
            if ($userInfo) {
                $ncvInfo = Db::name('netdisc_user_volume')->where('user_id', $fid)->where('much_id', $this->much_id)->find();
                if (!$ncvInfo) {
                    $diskSize = Db::name('netdisc_config')->where('much_id', $this->much_id)->value('disk_size');
                    Db::startTrans();
                    try {
                        Db::name('netdisc_user_volume')->insert(['user_id' => $fid, 'quota_size' => $diskSize, 'exp_time' => 0, 'use_status' => 1, 'much_id' => $this->much_id]);
                        Db::commit();
                    } catch (\Exception $e) {
                        Db::rollback();
                        return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                    }
                    return json(['code' => 1, 'msg' => '开通成功']);
                } else {
                    return json(['code' => 0, 'msg' => '此用户已开通过网盘功能']);
                }
            } else {
                return json(['code' => 0, 'msg' => '用户信息不存在！']);
            }
        }
    }

    /*
     * 调整用户空间容量大小
     */
    public function adjustUserVolume()
    {
        if (request()->isPost() && request()->isAjax()) {
            $uid = intval(input('post.uid', 0));
            $quotaSize = bcadd(input('post.quotaSize', 0), '0');
            $nuvInfo = Db::name('netdisc_user_volume')->where('user_id', $uid)->where('exp_time', 0)->where('much_id', $this->much_id)->find();
            if (!$nuvInfo) {
                return json(['code' => 0, 'msg' => '用户信息不存在或未开通网盘功能！']);
            }
            Db::startTrans();
            try {
                Db::name('netdisc_user_volume')->where('user_id', $uid)->where('exp_time', 0)->where('much_id', $this->much_id)->update(['quota_size' => $quotaSize]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        }
    }

    /*
     * 用户网盘列表
     */
    public function steal()
    {
        $uid = intval(request()->get('uid', 0));
        $pid = intval(request()->get('pid', 0));
        $type = intval(request()->get('type', 0));
        $page = request()->get('page', 1);
        $list = Db::name('netdisc_belong')
            ->where('user_id', $uid)
            ->where('parent_path_id', $pid)
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->order(['is_dir' => 'desc', 'id' => 'asc'])
            ->paginate(12, false, ['query' => ['s' => $this->defaultQuery(), 'uid' => $uid, 'pid' => $pid, 'type' => $type]])
            ->each(function ($item) {
                if (intval($item['is_dir']) === 0) {
                    $item['ncInfo'] = Db::name('netdisc')->where('id', $item['nc_id'])->where('much_id', $this->much_id)->field('file_suffix,file_size,file_address,file_status')->find();
                }
                return $item;
            });
        $setupSize = function ($fileSize) {
            $size = sprintf("%u", $fileSize);
            if ($size == 0) {
                return ("0 Bytes");
            }
            $sizeName = array(" Bytes", " KB", " MB", " GB", " TB", " PB", " EB", " ZB", " YB");
            return round($size / pow(1024, ($i = floor(log($size, 1024)))), 2) . $sizeName[$i];
        };
        $folderWhere = array();
        if ($pid !== 0) {
            $folderWhere['id'] = ['<>', $pid];
        }
        $folderList = Db::name('netdisc_belong')->where($folderWhere)->where('user_id', $uid)->where('parent_path_id', 0)->where('is_dir', 1)->where('is_del', 0)->where('much_id', $this->much_id)->select();
        //  允许的上传类型
        $allowedUploadTypes = NetDiskService::allowedUploadTypes($this->much_id);
        $this->assign('allowedUploadTypes', $allowedUploadTypes);
        $this->assign('folderList', $folderList);
        $this->assign('setupSize', $setupSize);
        $this->assign('list', $list);
        $this->assign('uid', $uid);
        $this->assign('pid', $pid);
        $this->assign('type', $type);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 禁用网盘
     */
    public function disabledNetDisc()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            $status = intval(input('post.status', 1));
            Db::startTrans();
            try {
                Db::name('netdisc_user_volume')->where('user_id', $fid)->where('much_id', $this->much_id)->update(['use_status' => $status]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
        if (!Remotely::isUnLockProperty(base64_decode('572R55uY5YiX6KGo'))) {
            abort(404);
        }
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, array(), 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    /*
     * 上传文件
     */
    public function uploadNetDisc()
    {
        if (request()->isPost() && request()->isAjax()) {
            //  获取文件
            $file = request()->file('netDiscFile');
            //  获取文件名称
            $fileName = pathinfo($file->getInfo('name'), PATHINFO_FILENAME);
            //  用户用户编号
            $uid = input('post.uid', 0);
            //  获取目录编号
            $pid = input('post.pid', 0);
            //  获取上传结果
            $upRes = NetDiskService::manipulate($file, $this->much_id);
            //  判断是否上传成功
            if ($upRes['code'] > 0) {
                Db::startTrans();
                try {
                    //  新上传的文件
                    if (intval($upRes['ncId']) === 0) {
                        $upRes['ncId'] = Db::name('netdisc')->insertGetId([
                            'file_type' => $upRes['fileType'],
                            'file_md5' => $upRes['fileMD5'],
                            'file_suffix' => $upRes['getExt'],
                            'file_size' => $upRes['getSize'],
                            'file_address' => $upRes['url'],
                            'file_status' => 1,
                            'up_user_id' => $uid,
                            'up_user_ip' => request()->ip(),
                            'add_time' => time(),
                            'much_id' => $this->much_id,
                        ]);
                    }
                    //  保存至用户网盘
                    Db::name('netdisc_belong')->insert([
                        'nc_id' => $upRes['ncId'],
                        'user_id' => $uid,
                        'file_name' => $fileName,
                        'parent_path_id' => $pid,
                        'is_dir' => 0,
                        'add_time' => time(),
                        'is_sell' => 1,
                        'is_del' => 0,
                        'much_id' => $this->much_id
                    ]);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
                return json(['code' => 1, 'msg' => 'success']);
            } else {
                return json(['code' => 0, 'msg' => $upRes['msg']]);
            }
        }
    }

    /*
     * 新建用户文件夹
     */
    public function newUserFolder()
    {
        if (request()->isPost() && request()->isAjax()) {
            $folderName = trim(input('folderName', ''));
            $uid = intval(input('post.uid', 0));
            $usCount = Db::name('user')->where('id', $uid)->where('much_id', $this->much_id)->count();
            if (!$usCount) {
                return json(['code' => 0, 'msg' => '用户信息错误，请关闭页面重新打开后尝试！']);
            }
            if ($folderName === '') {
                return json(['code' => 0, 'msg' => '新建文件夹名称不能为空！']);
            }
            $ncInfo = Db::name('netdisc_belong')->where('user_id', $uid)->where('file_name', $folderName)->where('much_id', $this->much_id)->find();
            if ($ncInfo) {
                return json(['code' => 0, 'msg' => '新建文件夹名称已经存在，请勿重复创建！']);
            }
            Db::startTrans();
            try {
                Db::name('netdisc_belong')->insert(['nc_id' => 0, 'user_id' => $uid, 'file_name' => $folderName, 'parent_path_id' => 0, 'is_dir' => 1, 'add_time' => time(), 'is_sell' => 0, 'is_del' => 0, 'much_id' => $this->much_id]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        }
    }

    /*
     * 移动用户文件
     */
    public function moveUserFile()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            $uid = intval(input('post.uid', 0));
            $pid = intval(input('post.pid', 0));
            if ($pid !== 0) {
                $folderInfo = Db::name('netdisc_belong')->where('id', $pid)->where('user_id', $uid)->where('is_dir', 1)->where('is_del', 0)->where('much_id', $this->much_id)->find();
                if (!$folderInfo) {
                    return json(['code' => 0, 'msg' => '移动失败，目录不存在，请刷新页面后重试！']);
                }
            }
            Db::startTrans();
            try {
                Db::name('netdisc_belong')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->update(['parent_path_id' => $pid]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 网盘设置
     */
    public function dictate()
    {
        if (request()->isPost() && request()->isAjax()) {
            $setData = input('post.setData/a', array());
            $data['disk_size'] = bcadd($setData['diskSize'], '0');
            $data['upload_size_limit'] = bcadd($setData['uploadSizeLimit'], '0');
            $data['upload_type_limited'] = trim($setData['uploadTypeLimited']);
            $data['rel_paper_icon_hide'] = intval($setData['relPaperIconHide']);
            $data['use_protocol'] = $this->safe_html($setData['protocol']);
            Db::startTrans();
            try {
                Db::name('netdisc_config')->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $list = Db::name('netdisc_config')->where('much_id', $this->much_id)->find();
            $this->assign('list', $list);
            return $this->fetch();
        }
    }

    /*
     * 存储设置
     */
    public function storage()
    {
        if (request()->isPost() && request()->isAjax()) {
            $quickType = intval(request()->post('quicken_type', -1));
            $quickData = Systems::annexOverlapping($quickType, 1, $this->much_id);
            Db::startTrans();
            try {
                Db::name('netdisc_storage')->where('much_id', $this->much_id)->cache("outlying_1_{$this->much_id}")->update($quickData);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            //  查询缓存信息
            $systemOutlying = Cache::get("outlying_1_{$this->much_id}");
            //  缓存信息不存在
            if (!$systemOutlying) {
                //  查询数据库信息
                $systemOutlying = Db::name('netdisc_storage')->where('much_id', $this->much_id)->find();
                //  数据库信息不存在
                if (!$systemOutlying) {
                    //  新建一条信息
                    $systemOutlying = ['quicken_type' => -1, 'much_id' => $this->much_id];
                    Db::startTrans();
                    try {
                        $systemOutlying['id'] = Db::name('netdisc_storage')->insertGetId($systemOutlying);
                        Db::commit();
                    } catch (\Exception $e) {
                        Db::rollback();
                        return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                    }
                }
                //  解析数据
                $systemOutlying = Systems::praise($systemOutlying);
                //  缓存数据
                Cache::set("outlying_1_{$this->much_id}", $systemOutlying, 7200);
            }
            $ossEndpoint = Cache::get('ossEndpoint');
            if (!$ossEndpoint) {
                $ossEndpoint = Db::name('outlying_allude')->where('status', 1)->where('type', 0)->cache('ossEndpoint')->select();
            }
            $ossRegion = Cache::get('ossRegion');
            if (!$ossRegion) {
                $ossRegion = Db::name('outlying_allude')->where('status', 1)->where('type', 1)->cache('ossRegion')->select();
            }
            $this->assign('list', $systemOutlying);
            $this->assign('ossEndpoint', $ossEndpoint);
            $this->assign('ossRegion', $ossRegion);
            return $this->fetch();
        }
    }
}