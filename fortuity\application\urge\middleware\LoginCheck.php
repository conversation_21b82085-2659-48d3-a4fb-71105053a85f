<?php

namespace app\urge\middleware;

use think\Cookie;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\Response;

/**
 * 高性能安全会话校验服务类
 * @package app\urge\middleware
 *
 * 优化特性：
 * - 单次数据库查询优化
 * - PHP 5.6兼容性优化
 * - 专业变量命名规范
 * - 减少重复计算和内存分配
 */
class LoginCheck
{
    // 会话配置常量
    const SESSION_LIFETIME_SECONDS = 43200;        // 12小时会话有效期
    const GRACE_PERIOD_SECONDS = 15;               // 15秒令牌切换宽限期
    const TOKEN_REFRESH_INTERVAL_SECONDS = 300;    // 5分钟令牌刷新间隔

    // 令牌验证状态常量
    const TOKEN_STATUS_VALID_PRIMARY = 1;          // 主令牌有效
    const TOKEN_STATUS_VALID_PREVIOUS = 2;         // 备用令牌有效
    const TOKEN_STATUS_INVALID = 0;                // 令牌无效

    /**
     * 获取当前插件目录名称
     * @return string 插件名称
     */
    private static function getPluginName()
    {
        // 使用__DIR__魔术常量获取当前文件路径
        $currentPath = __DIR__;
        // 解析路径：从 /path/to/addons/plugin_name/fortuity/application/urge/middleware
        // 提取出 plugin_name
        $pathSegments = explode(DS, $currentPath);
        // 查找addons目录的位置
        $addonsIndex = array_search('addons', $pathSegments);
        // 返回插件名称
        return $pathSegments[$addonsIndex + 1];
    }

    /**
     * 获取动态Cookie名称
     * @return string Cookie名称
     */
    private static function getCookieName()
    {
        return self::getPluginName() . '_session_token';
    }

    /**
     * 获取Salt Cookie名称
     * @return string Salt Cookie名称
     */
    private static function getSaltCookieName()
    {
        return self::getPluginName() . '_session_salt';
    }

    /**
     * 高性能会话验证入口方法
     * @return array 验证成功时返回用户会话信息
     * @throws HttpResponseException 验证失败时抛出重定向异常
     */
    public static function run()
    {
        // 步骤1：获取客户端会话令牌
        $clientToken = Cookie::get(self::getCookieName());
        if (empty($clientToken)) {
            self::handleInvalidSession();
        }

        // 步骤2：优化的单次数据库查询 - 同时查找主令牌和备用令牌
        // 使用UNION查询替代whereOr以提升性能
        $sessionRecord = self::findSessionByToken($clientToken);

        // 步骤3：预计算时间戳，避免重复调用time()
        $currentTimestamp = time();

        // 步骤4：执行会话验证逻辑
        $validationResult = self::validateSessionRecord($sessionRecord, $clientToken, $currentTimestamp);

        if ($validationResult['isValid']) {
            return self::buildUserSessionData($sessionRecord);
        }

        // 验证失败，处理无效会话
        $accessType = isset($sessionRecord['access_type']) ? $sessionRecord['access_type'] : null;
        self::handleInvalidSession($clientToken, $accessType);
    }

    /**
     * 优化的数据库查询方法 - 单次查询获取会话记录
     * @param string $token 客户端令牌
     * @return array|null 会话记录或null
     */
    private static function findSessionByToken($token)
    {
        // 使用更高效的查询方式，避免whereOr的性能问题
        $primaryTokenRecord = Db::name('login_checking')
            ->where('session_token', $token)
            ->find();

        if ($primaryTokenRecord) {
            return $primaryTokenRecord;
        }

        // 如果主令牌未找到，查找备用令牌
        return Db::name('login_checking')
            ->where('previous_session_token', $token)
            ->find();
    }

    /**
     * 会话记录验证核心逻辑
     * @param array|null $sessionRecord 会话记录
     * @param string $clientToken 客户端令牌
     * @param int $currentTimestamp 当前时间戳
     * @return array 验证结果数组
     */
    private static function validateSessionRecord($sessionRecord, $clientToken, $currentTimestamp)
    {
        if (empty($sessionRecord)) {
            return array('isValid' => false, 'reason' => 'session_not_found');
        }

        // 检查会话是否过期
        if (self::isSessionExpired($sessionRecord, $currentTimestamp)) {
            return array('isValid' => false, 'reason' => 'session_expired');
        }

        // 确定令牌类型和有效性
        $tokenStatus = self::determineTokenStatus($sessionRecord, $clientToken, $currentTimestamp);
        if ($tokenStatus === self::TOKEN_STATUS_INVALID) {
            return array('isValid' => false, 'reason' => 'invalid_token');
        }

        // 生成当前请求指纹（从cookie读取盐值）
        $sessionSalt = Cookie::get(self::getSaltCookieName());

        // 检查salt是否缺失，如果缺失则自动补全
        if (empty($sessionSalt)) {
            // Salt缺失，生成新的salt并更新会话
            $sessionSalt = self::generateSessionSalt();
            $newFingerprint = self::generateSessionFingerprint($sessionSalt);

            // 更新数据库中的指纹
            Db::name('login_checking')->where('id', $sessionRecord['id'])->update(array(
                'session_fingerprint' => $newFingerprint
            ));

            // 设置salt cookie
            Cookie::set(self::getSaltCookieName(), $sessionSalt, array('expire' => 0, 'httponly' => true));

            // 使用新生成的指纹进行验证
            $currentFingerprint = $newFingerprint;
            $isFingerprintValid = true; // 新生成的指纹肯定匹配
        } else {
            // Salt存在，正常验证
            $currentFingerprint = self::generateSessionFingerprint($sessionSalt);
            $isFingerprintValid = ($sessionRecord['session_fingerprint'] === $currentFingerprint);
        }

        if ($isFingerprintValid) {
            // 理想情况：令牌和指纹都匹配
            self::handleValidSession($sessionRecord, $clientToken, $tokenStatus, $currentTimestamp);
            return array('isValid' => true, 'reason' => 'perfect_match');
        }

        // 指纹不匹配，进行安全自愈检查
        return self::attemptSecurityHealing($sessionRecord, $clientToken, $currentTimestamp, $currentFingerprint);
    }

    /**
     * 检查会话是否已过期
     * @param array $sessionRecord 会话记录
     * @param int $currentTimestamp 当前时间戳
     * @return bool 是否过期
     */
    private static function isSessionExpired($sessionRecord, $currentTimestamp)
    {
        return ($sessionRecord['login_time'] + self::SESSION_LIFETIME_SECONDS) < $currentTimestamp;
    }

    /**
     * 确定令牌状态
     * @param array $sessionRecord 会话记录
     * @param string $clientToken 客户端令牌
     * @param int $currentTimestamp 当前时间戳
     * @return int 令牌状态常量
     */
    private static function determineTokenStatus($sessionRecord, $clientToken, $currentTimestamp)
    {
        // 检查是否为主令牌
        if ($sessionRecord['session_token'] === $clientToken) {
            return self::TOKEN_STATUS_VALID_PRIMARY;
        }

        // 检查是否为宽限期内的备用令牌
        if ($sessionRecord['previous_session_token'] === $clientToken) {
            $graceExpireTime = $sessionRecord['token_rotated_at'] + self::GRACE_PERIOD_SECONDS;
            if ($graceExpireTime > $currentTimestamp) {
                return self::TOKEN_STATUS_VALID_PREVIOUS;
            }
        }

        return self::TOKEN_STATUS_INVALID;
    }

    /**
     * 处理有效会话 - 执行令牌刷新等操作
     * @param array $sessionRecord 会话记录
     * @param string $clientToken 客户端令牌
     * @param int $tokenStatus 令牌状态
     * @param int $currentTimestamp 当前时间戳
     */
    private static function handleValidSession($sessionRecord, $clientToken, $tokenStatus, $currentTimestamp)
    {
        // 只有主令牌且需要刷新时才执行刷新操作
        if ($tokenStatus === self::TOKEN_STATUS_VALID_PRIMARY) {
            $lastRefreshTime = $sessionRecord['token_last_refreshed_at'];
            $shouldRefresh = ($currentTimestamp > $lastRefreshTime + self::TOKEN_REFRESH_INTERVAL_SECONDS);

            if ($shouldRefresh) {
                self::refreshSessionToken($sessionRecord['id'], $clientToken, $currentTimestamp);
            }
        } elseif ($tokenStatus === self::TOKEN_STATUS_VALID_PREVIOUS) {
            // 客户端使用备用令牌，重新发送主令牌（保持当前salt）
            $currentSalt = Cookie::get(self::getSaltCookieName());
            self::setCookieToken($sessionRecord['session_token'], $currentSalt);
        }
    }

    /**
     * 安全自愈机制 - 处理指纹不匹配的情况
     * @param array $sessionRecord 会话记录
     * @param string $clientToken 客户端令牌
     * @param int $currentTimestamp 当前时间戳
     * @param string $currentFingerprint 当前指纹
     * @return array 验证结果
     */
    private static function attemptSecurityHealing($sessionRecord, $clientToken, $currentTimestamp, $currentFingerprint)
    {
        // 获取当前IP地址进行二次验证
        $currentIpAddress = self::getClientIpAddress();
        $isIpMatched = ($sessionRecord['login_ip'] === $currentIpAddress);

        if ($isIpMatched) {
            // 安全自愈：IP匹配，可能是浏览器更新导致指纹变化
            self::performSecurityHealing($sessionRecord['id'], $clientToken, $currentTimestamp, $currentIpAddress, $currentFingerprint);
            return array('isValid' => true, 'reason' => 'security_healing');
        }

        // 高风险：令牌正确但IP和指纹都不匹配，可能是令牌被盗用
        return array('isValid' => false, 'reason' => 'security_risk');
    }

    /**
     * 执行安全自愈操作 - 生成新的指纹和盐值
     * @param int $sessionId 会话ID
     * @param string $oldToken 旧令牌
     * @param int $currentTimestamp 当前时间戳
     * @param string $currentIp 当前IP
     * @param string $currentFingerprint 当前指纹（未使用，将重新生成）
     */
    private static function performSecurityHealing($sessionId, $oldToken, $currentTimestamp, $currentIp, $currentFingerprint)
    {
        $newToken = self::generateCryptographicToken();

        // 生成新的盐值和指纹
        $newSessionSalt = self::generateSessionSalt();
        $newFingerprint = self::generateSessionFingerprint($newSessionSalt);

        $updateData = array(
            'session_token' => $newToken,
            'previous_session_token' => $oldToken,
            'token_rotated_at' => $currentTimestamp,
            'token_last_refreshed_at' => $currentTimestamp,
            'login_ip' => $currentIp,
            'session_fingerprint' => $newFingerprint
        );

        Db::name('login_checking')->where('id', $sessionId)->update($updateData);
        // 同时设置token和salt cookie
        self::setCookieToken($newToken, $newSessionSalt);
    }

    /**
     * 构建用户会话数据
     * @param array $sessionRecord 会话记录
     * @return array 用户会话数据
     */
    private static function buildUserSessionData($sessionRecord)
    {
        return array(
            'uniacid' => $sessionRecord['uniacid'],
            'role' => $sessionRecord['role'],
            'user' => array('username' => $sessionRecord['user_name']),
            'access_type' => $sessionRecord['access_type']
        );
    }

    /**
     * 处理无效会话
     * @param string|null $invalidToken 无效令牌
     * @param int|null $accessType 访问类型
     */
    private static function handleInvalidSession($invalidToken = null, $accessType = null)
    {
        // 清理客户端Cookie，防止重复提交无效令牌
        Cookie::delete(self::getCookieName());
        Cookie::delete(self::getSaltCookieName());

        // 构建重定向URL - 使用动态插件名
        $redirectUrl = self::buildRedirectUrl($accessType);

        // 抛出重定向异常，中断执行流程
        $redirectResponse = Response::create($redirectUrl, 'redirect');
        throw new HttpResponseException($redirectResponse);
    }

    /**
     * 构建重定向URL - 使用动态插件名
     * @param int|null $accessType 访问类型
     * @return string 重定向URL
     */
    private static function buildRedirectUrl($accessType)
    {
        // 获取当前插件名称
        $pluginName = self::getPluginName();

        // 构建动态路径分隔符
        $scriptPath = $_SERVER['SCRIPT_NAME'];
        $pathPattern = 'addons' . DS . $pluginName . DS . 'web' . DS . 'index.php';
        $pathSegments = explode($pathPattern, $scriptPath);
        $basePath = isset($pathSegments[0]) ? $pathSegments[0] : '/';

        // 根据访问类型确定目标路径
        $targetPath = ($accessType === 1)
            ? 'web/index.php?c=home&a=welcome&do=mc'
            : 'web/#/index/home';

        return $basePath . $targetPath;
    }

    /**
     * 刷新会话令牌
     * @param int $sessionId 会话ID
     * @param string $currentToken 当前令牌
     * @param int $timestamp 时间戳
     */
    private static function refreshSessionToken($sessionId, $currentToken, $timestamp)
    {
        $newToken = self::generateCryptographicToken();
        $clientIp = self::getClientIpAddress();

        $updateFields = array(
            'session_token' => $newToken,
            'previous_session_token' => $currentToken,
            'token_rotated_at' => $timestamp,
            'token_last_refreshed_at' => $timestamp,
            'login_ip' => $clientIp
        );

        Db::name('login_checking')->where('id', $sessionId)->update($updateFields);
        self::setCookieToken($newToken);
    }

    /**
     * 设置Cookie令牌 - 使用动态Cookie名称
     * @param string $token 令牌值
     * @param string|null $salt 盐值（可选）
     */
    private static function setCookieToken($token, $salt = null)
    {
        $cookieOptions = array('expire' => 0, 'httponly' => true);
        Cookie::set(self::getCookieName(), $token, $cookieOptions);

        // 如果提供了salt，同时设置salt cookie
        if ($salt !== null) {
            Cookie::set(self::getSaltCookieName(), $salt, $cookieOptions);
        }
    }

    /**
     * 获取客户端IP地址
     * @return string IP地址
     */
    private static function getClientIpAddress()
    {
        // 使用request()函数获取IP，
        return request()->ip();
    }

    /**
     * 生成密码学安全的随机令牌
     * @param int $tokenLength 令牌长度（十六进制字符数）
     * @return string 安全令牌
     * @throws \think\Exception 当无法生成强随机数时抛出异常
     */
    private static function generateCryptographicToken($tokenLength = 64)
    {
        // 计算所需的字节数（PHP 5.6兼容写法）
        $requiredBytes = (int)ceil($tokenLength / 2);

        // 生成密码学安全的随机字节
        $cryptoStrong = false;
        $randomBytes = openssl_random_pseudo_bytes($requiredBytes, $cryptoStrong);

        // 验证随机数强度
        if ($cryptoStrong === false) {
            // 如果OpenSSL无法生成强随机数，使用降级方案
            // 基于时间戳、进程ID和多个随机源生成相对安全的随机数
            $fallbackSources = array(
                microtime(true),
                getmypid(),
                mt_rand(),
                uniqid('', true),
                memory_get_usage(),
                isset($_SERVER['REQUEST_TIME_FLOAT']) ? $_SERVER['REQUEST_TIME_FLOAT'] : time()
            );
            $fallbackString = implode('|', $fallbackSources);
            // 使用多次哈希增强随机性
            $hash1 = hash('sha256', $fallbackString);
            $hash2 = hash('sha256', $hash1 . mt_rand());
            return substr($hash2, 0, $tokenLength);
        }

        // 转换为十六进制字符串
        return bin2hex($randomBytes);
    }

    /**
     * 生成增强会话指纹 - 稳定性优化算法
     * @param string|null $sessionSalt 会话盐值（验证时传入，创建时为null）
     * @return string SHA256哈希指纹
     */
    private static function generateSessionFingerprint($sessionSalt = null)
    {
        // 获取请求实例（PHP 5.6兼容方式）
        $requestInstance = Request::instance();

        // 收集稳定的特征信息（移除易变的网络层信息）
        $fingerprintComponents = array(
            // HTTP头部信息（相对稳定）
            'user_agent' => $requestInstance->header('user-agent'),
            'accept_language' => $requestInstance->header('accept-language'),
            'accept_encoding' => $requestInstance->header('accept-encoding'),
            // 会话盐值（确保唯一性）
            'session_salt' => $sessionSalt
        );

        // 过滤空值并拼接
        $filteredComponents = array_filter($fingerprintComponents, function($value) {
            return !empty($value);
        });

        $signatureString = implode('|', $filteredComponents);

        // 生成SHA256指纹
        return hash('sha256', $signatureString);
    }

    /**
     * 生成会话盐值
     * @return string 随机盐值
     */
    private static function generateSessionSalt()
    {
        // 生成基于时间戳和随机数的盐值
        $timestamp = microtime(true);
        $randomBytes = self::generateCryptographicToken(32);
        $saltComponents = array(
            $timestamp,
            $randomBytes,
            mt_rand(100000, 999999)
        );

        return hash('sha256', implode('|', $saltComponents));
    }
}
