<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\common\Playful;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

class Images extends Base
{

    //图库列表
    public function index()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $gclassify = db('gallery_classify')
            ->where('name', 'like', "%{$hazy_name}%")
            ->where('much_id', $this->much_id)
            ->order('scores', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]])
            ->each(function ($item, $key) {
                $item['count'] = db('gallery')->where('classify_id', $item['id'])->where('much_id', $this->much_id)->count();
                return $item;
            });
        $this->assign('gclassify', $gclassify);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $pageCount = $gclassify->count();
        if ($pageCount < 1 && $page != 1) {
            $this->emptyDataRedirect(['hazy_name' => $hazy_name]);
        }
        $this->assign('page', $page);
        return $this->fetch();
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, array(), 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    //图库排序
    public function sfrieng()
    {
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $scores = request()->post('dalue');
            Db::startTrans();
            try {
                Db::name('gallery_classify')->where('id', $syid)->where('much_id', $this->much_id)->update(['scores' => $scores]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result === true) {
                return json(['code' => 1, 'msg' => '排序成功']);
            }
        }
    }

    //图片管理
    public function superviseImages()
    {
        $url = $this->defaultQuery();
        $gclasid = request()->get('gclasid', 0);
        $gclassify = db('gallery_classify')
            ->where('status', 1)
            ->where('much_id', $this->much_id)
            ->order('scores', 'asc')
            ->order('id', 'asc')
            ->select();
        $gallery = db('gallery')
            ->where('classify_id', $gclasid)
            ->where('much_id', $this->much_id)
            ->order('id', 'desc')
            ->paginate(15, false, ['query' => ['s' => $url, 'gclasid' => $gclasid]]);
        $this->assign('gclassify', $gclassify);
        $this->assign('gallery', $gallery);
        $this->assign('gclasid', $gclasid);
        return $this->fetch();
    }

    //图片移动
    public function stirGallery()
    {
        if (request()->isPost() && request()->isAjax()) {
            $euid = request()->post('euid');
            $gclasid = request()->post('gclasid');
            Db::startTrans();
            try {
                db('gallery')->where('id', $euid)->where('much_id', $this->much_id)->update(['classify_id' => $gclasid]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                $result = false;
                Db::rollback();
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '移动成功']);
            } else {
                return json(['code' => 0, 'msg' => '移动失败']);
            }
        }
    }

    //图片选择框
    public function dialogImages()
    {
        $url = $this->defaultQuery();
        $gclasid = request()->get('gclasid', 0);
        $dynamicStyle = request()->get('dynamicStyle', 0);
        $pictureIndex = request()->get('pictureIndex', -2);
        if ($gclasid > 0) {
            $gcy = db('gallery_classify')->where('id', $gclasid)
                ->where('status', 1)
                ->where('much_id', $this->much_id)
                ->find();
            if (!$gcy) {
                $this->redirect('images/dialogImages');
            }
        }
        $gclassify = db('gallery_classify')
            ->where('status', 1)
            ->where('much_id', $this->much_id)
            ->order('scores', 'asc')
            ->order('id', 'asc')
            ->select();
        foreach ($gclassify as $key => $value) {
            $gclassify[$key]['count'] = db('gallery')
                ->where('classify_id', $value['id'])
                ->where('much_id', $this->much_id)
                ->count();
        }
        if ($gclasid > 0) {
            $gallery = db('gallery')
                ->where('classify_id', $gclasid)
                ->where('much_id', $this->much_id)
                ->order('id', 'desc')
                ->paginate(15, false, ['query' => ['s' => $url, 'gclasid' => $gclasid, 'dynamicStyle' => $dynamicStyle, 'pictureIndex' => $pictureIndex]]);
            $this->assign('gclasid', $gclasid);
        } else {
            $gallery = db('gallery')
                ->where('classify_id', $gclassify[0]['id'])
                ->where('much_id', $this->much_id)
                ->order('id', 'desc')
                ->paginate(15, false, ['query' => ['s' => $url, 'gclasid' => 0, 'dynamicStyle' => $dynamicStyle, 'pictureIndex' => $pictureIndex]]);
            $this->assign('gclasid', $gclassify[0]['id']);
        }
        $this->assign('gclassify', $gclassify);
        $this->assign('gallery', $gallery);
        //富文本
        $this->assign('dynamicStyle', $dynamicStyle);
        //抽奖索引
        $this->assign('pictureIndex', $pictureIndex);
        return $this->fetch();
    }

    //删除选择的图片
    public function unimgs()
    {
        if (request()->isPost() && request()->isAjax()) {
            $euid = request()->post('euid');
            Db::startTrans();
            try {
                db('gallery')->where('id', $euid)->where('much_id', $this->much_id)->delete();
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result === true) {
                return json(['code' => 1, 'msg' => '删除成功']);
            }
        }
    }

    //图库状态
    public function eturvys()
    {
        if (request()->isPost() && request()->isAjax()) {
            $euid = request()->post('euid');
            $status = request()->post('status');
            Db::startTrans();
            try {
                db('gallery_classify')->where('id', $euid)->where('much_id', $this->much_id)->update(['status' => $status]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result === true) {
                return json(['code' => 1, 'msg' => '更改成功']);
            }
        }
    }

    //新增图片库
    public function newImgs()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['name'] = request()->post('name');
            $data['scores'] = request()->post('scores');
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                db('gallery_classify')->insert($data);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result === true) {
                return json(['code' => 1, 'msg' => '新增成功']);
            }
        }
        return $this->fetch();
    }

    //删除图片库
    public function unImagesSify()
    {
        if (request()->isPost() && request()->isAjax()) {
            $suid = request()->post('suid');
            $gcy = db('gallery')->where('classify_id', $suid)->where('much_id', $this->much_id)->find();
            if ($gcy) {
                return json(['code' => 0, 'msg' => '删除失败，请把图库下的图片移动到其他图库或删除清空后再进行删库操作！']);
            }
            Db::startTrans();
            try {
                db('gallery_classify')->where('id', $suid)->where('much_id', $this->much_id)->delete();
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result === true) {
                return json(['code' => 1, 'msg' => '删除成功']);
            }
        }
    }

    //新增网络图片
    public function pullNetworkPicture()
    {
        if (request()->isPost() && request()->isAjax()) {
            $gclasid = request()->post('gclasid');
            $webImg = trim(request()->post('webImg'));
            if ($webImg != '') {
                Db::startTrans();
                try {
                    db('gallery')->insert([
                        'classify_id' => $gclasid,
                        'img_title' => 'web-image',
                        'img_url' => $webImg,
                        'much_id' => $this->much_id
                    ]);
                    $result = true;
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
                if ($result === true) {
                    return json(['code' => 1, 'msg' => '添加成功']);
                }
            } else {
                return json(['code' => 1, 'msg' => 'success']);
            }
        }
    }
}