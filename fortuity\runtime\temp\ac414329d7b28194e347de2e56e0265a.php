<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:86:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/compass/fence.html";i:1749658660;s:77:"/www/wwwroot/wq.inotnpc.com/addons/yl_welore/fortuity/template/urge/base.html";i:1755516152;}*/ ?>
<!DOCTYPE HTML>
<!--STATUS OK-->
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $knight['title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="referrer" content="never">
    <meta name="renderer" content="webkit">
    <meta name="apple-mobile-web-app-title"/>
    <link rel="shortcut icon" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="bookmark" href="<?php echo $knight['sgraph']; ?>" />
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
    <script src="./assets/js/jquery.min.js"></script>
    <script src="./assets/js/vue.min.js"></script>
    <script src="./assets/js/element-ui.min.js"></script>
    <?php if($motUrl == 'index/index'): ?>
    <script src="./assets/js/echarts.min.js"></script>
    <?php endif; ?>
    <!-- 允许加载混合内容 -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
</head>
<body data-type="index">
<header class="am-topbar am-topbar-inverse admin-header">
    <div class="am-topbar-brand" style="width: 100px;">
        <a href="<?php echo url('index/index'); ?>" class="tpl-logo">
            <img src="<?php echo $knight['sgraph']; ?>" style="width: 70px;height: 70px;">
        </a>
    </div>
    <audio id="backPlayer" controls="controls" style="display: none;" >
        <source src="./static/disappear/stound.mp3"/>
    </audio>
    <div class="am-icon-list tpl-header-nav-hover-ico am-fl am-margin-right">
    </div>
    <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
        <ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list tpl-header-list">
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-bell-o"></span> 提醒
                    <span id="notice-0" class="am-badge tpl-badge-success am-round"><?php echo $notice; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="notice-1" class="tpl-color-success"><?php echo $notice; ?></span> 条提醒</h3>
                        <a href="<?php echo url('index/awake'); ?>" target="_blank">查看</a></li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-comment-o"></span> 消息
                    <span id="vacant-0" class="am-badge tpl-badge-danger am-round"><?php echo $vacant; ?></span>
                </a>
                <ul class="am-dropdown-content tpl-dropdown-content">
                    <li class="tpl-dropdown-content-external">
                        <h3>你有 <span id="vacant-1" class="tpl-color-danger"><?php echo $vacant; ?></span> 条新消息</h3>
                        <a href="<?php echo url('index/message'); ?>" target="_blank">查看</a></li>
                    </li>
                </ul>
            </li>
            <li class="am-dropdown" data-am-dropdown="" data-am-dropdown-toggle="">
                <a class="am-dropdown-toggle tpl-header-list-link" href="javascript:;">
                    <span class="am-icon-user" style="margin-right: 10px;"></span>
                    <span style="margin-top: 5px;"><?php echo $much_name; ?> ( <?php echo $much_title; ?> )</span>
                    <span class="am-icon-sort-desc" style="position: relative;top: -2px;"></span>
                </a>
                <ul class="am-dropdown-content">
                    <li>
                        <a href="javascript:void(0);" onclick="retakeCache();">
                            <span class="am-icon-recycle"></span> 清理缓存
                        </a>
                    </li>
                    <?php if($much_role=='founder'): ?>
                    <li>
                        <a href="javascript:void(0);" onclick="updateFix();">
                            <span class="am-icon-wrench"></span> 更新修复
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="<?php echo url('index/logout'); ?>">
                            <span class="am-icon-sign-out"></span> 返回系统
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</header>
<div class="tpl-page-container tpl-page-header-fixed">
    <div class="tpl-left-nav tpl-left-nav-hover">
        <div class="tpl-left-nav-title">
            <h2>功能列表</h2>
        </div>
        <div id="menu" class="tpl-left-nav-list">
            <el-menu :default-active="menuActive" :collapse="false" :unique-opened="true">
                <template v-for="(item) in menuList">
                    <template v-if="item.childMenuItems.length === 0">
                        <a :href="item.mot_url" target="_self">
                            <el-menu-item :index="item.id" :class="{'is-active':Number(item.id) === Number(menuActive)}">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </el-menu-item>
                        </a>
                    </template>
                    <template v-else>
                        <el-submenu :index="item.id">
                            <template slot="title">
                                <i :class="item.icon"></i>
                                <span>{{item.mot_name}}</span>
                            </template>
                            <template v-for="(childrenItem,childrenIndex) in item.childMenuItems">
                                <a :href="childrenItem.mot_url" target="_self" v-if="item.childMenuItems[childrenIndex].grandsonList.length === 0">
                                    <el-menu-item :index="childrenItem.id" :class="{'is-active':Number(childrenItem.id) === Number(menuActive)}">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </el-menu-item>
                                </a>
                                <el-submenu v-else :index="item.id + '-' + childrenItem.id">
                                    <template slot="title">
                                        <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                        <span>{{ childrenItem.mot_name }}</span>
                                    </template>
                                    <template v-for="(grandsonItem) in item.childMenuItems[childrenIndex].grandsonList">
                                        <a :href="grandsonItem.mot_url" target="_self">
                                            <el-menu-item :index="grandsonItem.id" :class="{'is-active':Number(grandsonItem.id) === Number(menuActive)}">
                                                <i class="am-icon-angle-right" style="margin-right: 5px;"></i>
                                                <span>{{grandsonItem.mot_name}}</span>
                                            </el-menu-item>
                                        </a>
                                    </template>
                                </el-submenu>
                            </template>
                        </el-submenu>
                    </template>
                </template>
            </el-menu>
        </div>
    </div>
    <div class="tpl-content-wrapper" style="padding-top:0;">
        
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;position:relative;}.tpl-portlet-components:after{content:"";display:table;clear:both;}.am-form{position:relative;overflow:visible;}.portlet-title{display:flex;justify-content:space-between;align-items:center;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid #f0f0f0;}.caption{font-size:16px;color:#23b7e5;font-weight:500;}.caption .am-icon-circle-o{margin-right:5px;color:#23b7e5;}.tpl-portlet-input{position:relative;}.tpl-portlet-input input{height:32px;width:200px;padding:0 30px 0 10px;border:1px solid #e8e8e8;border-radius:4px;background:#fafafa;transition:all 0.3s;}.tpl-portlet-input input:focus{border-color:#23b7e5;background:#fff;box-shadow:0 0 0 2px rgba(35,183,229,0.1);}.tpl-portlet-input .am-icon-search{position:absolute;right:10px;top:50%;transform:translateY(-50%);color:#999;cursor:pointer;}.action-btn{display:inline-block;padding:4px 8px;background:#fff;border:1px solid #ddd;color:#23b7e5;border-radius:3px;font-size:12px;cursor:pointer;transition:all 0.3s;}.action-btn:hover{border-color:#23b7e5;color:#23b7e5;background-color:#f5fafd;}.am-table{border:1px solid #f0f0f0;border-radius:4px;font-size:13px;margin-bottom:15px;}.am-table > thead:first-child > tr:first-child > th{background-color:#f9f9f9;border-bottom:1px solid #eee;color:#333;font-weight:500;font-size:13px;text-align:center;padding:10px 8px;}.am-table > tbody > tr > td{padding:10px 8px;border-top:1px solid #f3f3f3;text-align:center;vertical-align:middle;color:#666;line-height:1.6;position:relative;}.am-table > tbody > tr:hover > td{background-color:#f5fafd;}.am-pagination{margin:10px 0;}.am-pagination > li > a{color:#666;background-color:#fff;border:1px solid #e8e8e8;margin:0 3px;border-radius:3px;}.am-pagination > .am-active > a{background-color:#23b7e5;border-color:#23b7e5;}.am-pagination > li > a:hover{background-color:#f5fafd;border-color:#e8e8e8;color:#23b7e5;}.filter-btn-group .am-btn{margin-right:10px;border-radius:3px;}.filter-btn-group .am-btn.active{background-color:#23b7e5;color:white;border-color:#23b7e5;}.am-badge-danger{color:#fff;background-color:#dd514c;}.shild-actions-container{display:flex;flex-wrap:wrap;justify-content:center;gap:8px;padding:10px 15px;}.shild-actions-container a{margin:0 25px;}.am-table tbody tr.am-table-striped-odd > td{background-color:#f9f9f9;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-circle-o"></span> 圈子列表
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="<?php echo $hazy_name; ?>" placeholder="搜索圈子...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <a href="javascript:void(0);" class="am-btn am-btn-success am-btn-sm" onclick="saloof();">
                        <span class="am-icon-plus"></span> 新增圈子
                    </a>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-6" style="display: flex; justify-content: flex-end;">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-sm filter-btn-group">
                        <a href="<?php echo url('compass/fence'); ?>&egon=0" class="am-btn am-btn-default <?php if($egon==0): ?>active<?php endif; ?>">正常显示</a>
                        <a href="<?php echo url('compass/fence'); ?>&egon=1" class="am-btn am-btn-default <?php if($egon==1): ?>active<?php endif; ?>">回收站</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-hover">
                        <thead>
                        <tr>
                            <?php if($egon==0): ?>
                            <th width="7%" class="text-center">
                                排序
                            </th>
                            <?php endif; ?>
                            <th width="7%" class="text-center">ID</th>
                            <th width="7%" class="text-center">图标</th>
                            <th width="10%" class="text-center">名称</th>
                            <th width="8%" class="text-center">类型</th>
                            <th width="7%" class="text-center">状态</th>
                            <th width="12%" class="text-center">创建时间</th>
                            <th width="8%" class="text-center">发帖限制</th>
                            <th width="8%" class="text-center">浏览限制</th>
                            <th width="8%" class="text-center">浏览权限</th>
                            <?php if($egon==0): ?>
                            <th width="8%" class="text-center">链接</th>
                            <?php endif; ?>
                            <th width="10%" class="text-center">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): $i = 0; $__LIST__ = $list;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <tr>
                            <?php if($egon==0): ?>
                            <td>
                                <input type="text" class="scfixed" id="sfixed-<?php echo $vo['id']; ?>" value="<?php echo $vo['scores']; ?>" data-score="<?php echo $vo['scores']; ?>" style="width: 50px; text-align: center;" onblur="supre('<?php echo $vo['id']; ?>','#sfixed-<?php echo $vo['id']; ?>');">
                            </td>
                            <?php endif; ?>
                            <td><?php echo $vo['id']; ?></td>
                            <td>
                                <img src="<?php echo $vo['realm_icon']; ?>" onerror="this.src='static/disappear/default.png'" style="width: 40px;height:40px; border-radius: 50%;"/>
                            </td>
                            <td title="<?php echo $vo['realm_name']; ?>">
                                <?php echo subtext(emoji_decode($vo['realm_name']),8); ?>
                            </td>
                            <td>
                                <a href="<?php echo url('compass/fence'); ?>&egon=<?php echo $egon; ?>&nid=<?php echo $vo['neid']; ?>" target="_blank">
                                <?php echo $vo['name']; ?>
                                </a>
                            </td>
                            <td>
                                <?php if($vo['status'] == 0): ?>
                                <span class="am-badge am-badge-warning">暂停</span>
                                <?php else: ?>
                                <span class="am-badge am-badge-success">正常</span>
                                <?php endif; ?>
                            </td>
                            <td title="<?php echo date('Y-m-d H:i:s',$vo['rising_time']); ?>">
                                <?php echo date('Y-m-d H:i',$vo['rising_time']); ?>
                            </td>
                            <td>Lv.<?php echo $vo['release_level']; ?></td>
                            <td>Lv.<?php echo $vo['visit_level']; ?></td>
                            <td>
                                <?php switch($vo['attention']): case "0": ?><span class="am-text-success">所有用户</span><?php break; case "1": ?><span class="am-text-warning">审核加入</span><?php break; case "2": ?><span class="am-text-secondary">会员用户</span><?php break; case "3": ?><span class="am-text-primary">用户关注</span><?php break; endswitch; ?>
                            </td>
                            <?php if($egon==0): ?>
                            <td>
                                <button type="button" class="action-btn" onclick="exerox(this);">
                                    <input type="hidden" value="/yl_welore/pages/packageA/circle_info/index?id=<?php echo $vo['id']; ?>"> 复制
                                </button>
                            </td>
                            <td>
                                <button class="action-btn" onclick="gains(this,'<?php echo $vo['id']; ?>');" title="更多操作">
                                    <span class="am-icon-angle-down"></span>
                                </button>
                            </td>
                            <?php else: ?>
                            <td>
                                <button type="button" class="action-btn am-btn-secondary" onclick="delGroup('<?php echo $vo['id']; ?>','1');">恢复</button>
                                <button type="button" class="action-btn am-btn-danger" onclick="delGroup('<?php echo $vo['id']; ?>','2');">彻底删除</button>
                            </td>
                            <?php endif; ?>
                        </tr>
                        <tr id="kshatri-<?php echo $vo['id']; ?>" style="display: none;">
                            <td colspan="12" style="padding: 0; background-color: #fff;">
                                <div class="shild-actions-container">
                                    <a href="<?php echo url('compass/dominator'); ?>&tyid=<?php echo $vo['id']; ?>" class="action-btn" target="_blank">管理团队</a>
                                    <a href="<?php echo url('compass/savour'); ?>&hazy_bering=<?php echo $vo['id']; ?>&egon=0" class="action-btn" target="_blank">关注审核</a>
                                    <a href="<?php echo url('essay/index'); ?>&hazy_name=<?php echo $vo['realm_name']; ?>&page=1" class="action-btn" target="_self">查看帖子</a>
                                    <a href="<?php echo url('leading/muted'); ?>&tyid=<?php echo $vo['id']; ?>" class="action-btn" target="_blank">禁言列表</a>
                                    <a href="<?php echo url('compass/topping'); ?>&tory_id=<?php echo $vo['id']; ?>" class="action-btn" target="_blank">置顶帖子</a>
                                    <a href="<?php echo url('compass/essence'); ?>&tory_id=<?php echo $vo['id']; ?>" class="action-btn" target="_blank">推荐帖子</a>
                                    <a href="<?php echo url('compass/uplfence'); ?>&uplid=<?php echo $vo['id']; ?>" class="action-btn" target="_blank">编辑圈子</a>
                                    <a href="javascript:void(0);" class="action-btn" onclick="delGroup('<?php echo $vo['id']; ?>','0');">删除圈子</a>
                                    <a href="javascript:void(0);" class="action-btn" onclick="showQrCode('<?php echo $vo['id']; ?>');">二维码</a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            <?php echo $list->render(); ?>
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>

        <div class="tpl-content-scope">
            <div class="note note-info" style="border: none;">
                <p style="text-align: center;">
                    <span class="label" style="color: #a3afb7;"><?php echo $knight['copyright']; ?></span>
                </p>
            </div>
        </div>
    </div>
</div>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<?php if($motUrl == 'index/index'): ?>
<script src="./assets/js/iscroll.js"></script>
<script src="./assets/js/app.js?v=<?php echo time(); ?>"></script>
<?php endif; ?>
<script src="./assets/js/common.js"></script>
<script src="./static/layer/layer.js"></script>
<?php if($much_role=='founder'): ?>
<script>
    var updateFix = function () {
        layer.confirm('您确定要修复数据库更新时所丢失的字段吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("<?php echo url('index/repairMissing'); ?>", function (data) {
                if (data !== false) {
                    layer.msg('数据库缺失字段修复完成', {time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('修复失败', {time: 1600}, function () {
                        location.reload();
                    });
                }
            });
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }
</script>
<?php endif; ?>
<script>
    new Vue({
        el: '#menu',
        data() {
            return {
                menuActive: '<?php echo $menuActive; ?>',
                menuList: []
            };
        },
        created() {
            const menuList = '<?php echo $menuList; ?>';
            if (menuList !== '') {
                const menuData = JSON.parse(decodeURIComponent(atob(menuList)));
                for (let i = 0; i < menuData.length; i++) {
                    if (menuData[i].pid === 0) {
                        menuData[i].childMenuItems = [];
                        this.menuList.push(menuData[i]);
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        if (menuData[i].pid === this.menuList[j].id) {
                            const childItem = JSON.parse(JSON.stringify(menuData[i]));
                            childItem.grandsonList = [];
                            this.menuList[j].childMenuItems.push(childItem);
                            break;
                        }
                    }
                }
                for (let i = 0; i < menuData.length; i++) {
                    for (let j = 0; j < this.menuList.length; j++) {
                        for (let k = 0; k < this.menuList[j].childMenuItems.length; k++) {
                            if (menuData[i].pid === this.menuList[j].childMenuItems[k].id) {
                                this.menuList[j].childMenuItems[k].grandsonList.push(menuData[i]);
                                break;
                            }
                        }
                    }
                }
            }
        }
    });

    $(function () {
        setInterval(reballot, 15000);
    });

    var reballot = function () {
        $.getJSON("<?php echo url('ordinary'); ?>", function (data) {
            var i = 0;
            if (data.notice > 0 || data.vacant > 0) {
                i++;
            }
            $('#notice-0,#notice-1').text(data.notice);
            $('#vacant-0,#vacant-1').text(data.vacant);
            if ((data.notice + data.vacant) <= data.preCount) {
                i = 0;
            } else {
                $.post("<?php echo url('receipt'); ?>", {'multiply': (data.notice + data.vacant)});
            }
            if (i > 0) {
                var player = $("#backPlayer")[0];
                player.play();
            }
        });
    }

    var retakeCache = function () {
        $.get("<?php echo url('index/purgeCache'); ?>", function () {
            layer.msg('缓存清理完成', {time: 1000}, function () {
                location.reload();
            });
        });
    }
</script>
<script>
// 添加全局变量
window.EDITOR_MODE = false;  // 标记是否在编辑器模式

// AJAX 拦截器 - 自动还原代理 URL 为原始 URL
(function() {
    // 代理 URL 的匹配模式
    var proxyUrlPattern = /[?&]url=([^&]+)/;
    var proxyBaseUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>";

    // URL 还原函数
    function restoreOriginalUrl(url) {
        try {
            // 检查是否是代理 URL
            if (typeof url === 'string' && url.indexOf(proxyBaseUrl) !== -1) {
                var match = url.match(proxyUrlPattern);
                if (match && match[1]) {
                    // 解码并返回原始 URL
                    return decodeURIComponent(match[1]);
                }
            }
            return url;
        } catch (e) {
            // 出错时返回原始值，确保不影响正常功能
            console.warn('URL restore failed:', e);
            return url;
        }
    }

    // 增强版 URL 还原函数 - 支持从 DOM 元素的 data 属性获取原始 URL
    window.getOriginalSrc = function(element) {
        try {
            if (element && element.getAttribute) {
                // 优先从 data-original-src 获取
                var originalSrc = element.getAttribute('data-original-src');
                if (originalSrc) {
                    return originalSrc;
                }
                // 其次尝试从当前 src 还原
                var currentSrc = element.src || element.getAttribute('src');
                return restoreOriginalUrl(currentSrc);
            }
            return element;
        } catch (e) {
            console.warn('getOriginalSrc failed:', e);
            return element;
        }
    };

    // 递归处理数据对象，还原其中的代理 URL
    function restoreDataUrls(data) {
        try {
            if (typeof data === 'string') {
                return restoreOriginalUrl(data);
            } else if (Array.isArray(data)) {
                return data.map(restoreDataUrls);
            } else if (data && typeof data === 'object') {
                var restored = {};
                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        restored[key] = restoreDataUrls(data[key]);
                    }
                }
                return restored;
            }
            return data;
        } catch (e) {
            // 出错时返回原始数据
            console.warn('Data restore failed:', e);
            return data;
        }
    }

    // 保存原始的 jQuery AJAX 方法
    var originalAjax = $.ajax;
    var originalPost = $.post;
    var originalGet = $.get;

    // 重写 $.ajax
    $.ajax = function(options) {
        try {
            if (options && options.data) {
                options.data = restoreDataUrls(options.data);
            }
        } catch (e) {
            console.warn('AJAX data restore failed:', e);
        }
        return originalAjax.apply(this, arguments);
    };

    // 重写 $.post
    $.post = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('POST data restore failed:', e);
        }
        return originalPost.call(this, url, data, success, dataType);
    };

    // 重写 $.get
    $.get = function(url, data, success, dataType) {
        try {
            if (data) {
                data = restoreDataUrls(data);
            }
        } catch (e) {
            console.warn('GET data restore failed:', e);
        }
        return originalGet.call(this, url, data, success, dataType);
    };
})();

// 修改检测函数
function checkResourceAccess() {
    var isHttps = window.location.protocol === 'https:';
    
    if (isHttps) {
        // 处理图片
        document.querySelectorAll('img[src^="http://"]').forEach(function(img) {
            // 如果是编辑器模式，所有图片都使用代理URL显示
            if(window.EDITOR_MODE) {
                var originalSrc = img.getAttribute('data-src') || img.src;
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
                return;
            }
            
            // 非编辑器模式下排除富文本编辑器内的图片
            if(img.closest('.w-e-text') || img.closest('.w-e-text-container')) {
                return;
            }
            
            var originalSrc = img.getAttribute('data-src') || img.src;
            var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);

            var tmpImg = new Image();
            tmpImg.onload = function() {
                // 原始图片可访问，不做处理
            }
            tmpImg.onerror = function() {
                // 存储原始 URL 到 data 属性
                img.setAttribute('data-original-src', originalSrc);
                img.src = proxyUrl;
            }
            tmpImg.src = originalSrc;
        });
        
        // 处理视频
        document.querySelectorAll('video').forEach(function(video) {
            // 处理video的src属性
            if (video.src && video.src.startsWith('http://')) {
                handleResource(video, 'src');
            }
            
            // 处理source标签
            video.querySelectorAll('source[src^="http://"]').forEach(function(source) {
                handleResource(source, 'src');
            });
        });
    }
}

// 统一的资源处理函数
function handleResource(element, srcAttr) {
    var originalSrc = element[srcAttr];
    var proxyUrl = "<?php echo url('urge/proxy/proxy_resource'); ?>&url=" + encodeURIComponent(originalSrc);
    
    // 创建临时对象测试资源是否可访问
    var tmpObj = new Image(); // 用Image对象测试连接可用性
    tmpObj.onload = function() {
        // 原始资源可访问，不做处理
    }
    tmpObj.onerror = function() {
        // 原始资源不可访问，切换到代理
        // 存储原始 URL 到 data 属性
        element.setAttribute('data-original-src', originalSrc);
        element[srcAttr] = proxyUrl;

        // 同时更新父级a标签的href
        var parent = element.parentElement;
        if (parent && parent.tagName === 'A') {
            parent.setAttribute('data-original-href', parent.href);
            parent.href = proxyUrl;
        }
    }
    tmpObj.src = originalSrc;
}

// 页面加载完成后执行检查
window.addEventListener('load', checkResourceAccess);

// 动态加载的内容处理
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            checkResourceAccess();
        }
    });
});

// 监听DOM变化
observer.observe(document.body, {
    childList: true,
    subtree: true
});
</script>

<script>
    $(function() {
        // Custom table striping logic
        $('tbody tr:not([id^="kshatri-"])').each(function(index) {
            if (index % 2 != 1) {
                $(this).addClass('am-table-striped-odd');
            }
        });
    });

    var gains = function (obj, suid) {
        var icon_span = $(obj).find('span');
        var is_open = icon_span.hasClass('am-icon-angle-up');

        // Close all other open rows and reset their icons
        $('.am-icon-angle-up').removeClass('am-icon-angle-up').addClass('am-icon-angle-down');
        $('tr[id^="kshatri-"]').hide();

        if (!is_open) {
            // If it was closed, open it and set the icon to 'up'
            icon_span.removeClass('am-icon-angle-down').addClass('am-icon-angle-up');
            $('#kshatri-' + suid).show();
        }
        // If it was already open, the code above has already closed it and reset the icon.
    }

    var supre = function (asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            var repair = exalter(asyId, dalue);
            if (repair.code > 0) {
                layer.msg(repair.msg, {icon: 1, time: 800});
                $(domId).attr('data-score', dalue);
            } else {
                layer.msg(repair.msg, {icon: 5, time: 1600});
            }
        }
    }

    var exalter = function (asyId, dalue) {
        var straw = new Array();
        $.ajax({
            type: "post",
            url: "<?php echo url('utsun'); ?>",
            data: {
                asyId: asyId,
                dalue: dalue
            },
            async: false,
            success: function (data) {
                straw = data;
            }
        });
        return straw;
    }

    var exerox = function (obj) {
        var linkUrl = $(obj).children('input').eq(0).val();
        var oInput = document.createElement('input');
        oInput.value = linkUrl;
        document.body.appendChild(oInput);
        oInput.select(); // 选择对象
        var carried = document.execCommand("Copy"); // 执行浏览器复制命令
        oInput.className = 'oInput';
        oInput.style.display = 'none';
        if (carried) {
            layer.alert('\u94fe\u63a5\u5730\u5740\u5df2\u6210\u529f\u590d\u5236\u5230\u526a\u8d34\u677f\uff0c\u8bf7\u4f7f\u7528\u9f20\u6807\u53f3\u952e\u6216\u952e\u76d8\u7684\u0020\u0043\u0074\u0072\u006c\u002b\u0056\u0020\u7ec4\u5408\u952e\u0020\u8fdb\u884c\u7c98\u8d34\uff01', {'title': '\u7cfb\u7edf\u63d0\u793a'});
        } else {
            layer.alert('\u590d\u5236\u94fe\u63a5\u5730\u5740\u5931\u8d25\uff0c\u8bf7\u6839\u636e\u8df3\u8f6c\u94fe\u63a5\u4e0b\u65b9\u63d0\u793a\u624b\u52a8\u66f4\u6539\uff01', {'title': '\u7cfb\u7edf\u63d0\u793a'});
        }
    }


    var saloof = function () {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "<?php echo url('compass/rulfence'); ?>");
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    var delGroup = function (tyid, restore) {
        if (restore == 0) {
            var unsolved = '您确定要删除此圈子吗？';
        } else if (restore == 1) {
            var unsolved = '您确定要恢复此圈子吗？';
        } else if (restore == 2) {
            var unsolved = '您确定要彻底删除此圈子吗 ( <span style="color:red;">数据不可恢复</span> ) ？';
        }
        layer.confirm(unsolved, {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("<?php echo url('compass/delLogicFlock'); ?>", {'tyid': tyid, 'restore': restore}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    var showQrCode = function (fid) {
        $.get("<?php echo url('tissue/createQrCode'); ?>&pagePath=yl_welore/pages/packageA/circle_info/index@id=" + fid, function (data) {
            try {
                var msg = JSON.parse(data);
                layer.open({
                    title: "errcode：" + msg.errcode,
                    content: msg.errmsg
                });
            } catch (e) {
                layer.open({
                    type: 1,
                    title: false,
                    shadeClose: true,
                    closeBtn: 1,
                    area: ['430px', '430px'],
                    content: "<img src=\"<?php echo url('tissue/createQrCode'); ?>&pagePath=yl_welore/pages/packageA/circle_info/index@id=" + fid + "\">",
                });
            }
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "<?php echo url('compass/fence'); ?>&egon=<?php echo $egon; ?>&hazy_name=" + fz_name + "&page=<?php echo $page; ?>";
        } else {
            location.href = "<?php echo url('compass/fence'); ?>&egon=<?php echo $egon; ?>&page=<?php echo $page; ?>";
        }
    }

</script>

</body>
</html>