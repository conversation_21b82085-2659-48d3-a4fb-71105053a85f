<?php
// 定义命名空间
namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

use OSS\OssClient;
use Qcloud\Cos\Client;
use Qiniu\Auth;
use Qiniu\Storage\UploadManager;
use think\Log;
use Upyun\Config;
use Upyun\Upyun;

/**
 * 文件上传核心类
 * 实现了将本地文件上传到多种主流云存储服务的功能，包括阿里云、七牛云、腾讯云、又拍云、FTP和AWS S3。
 * 通过统一的接口和包装器模式，简化了上传操作并减少了代码冗余。
 */
class Assembly
{

    /**
     * 文件上传任务分发器
     * 根据指定的存储类型，调用相应的云存储或FTP上传处理方法
     * 支持多种主流云存储服务和FTP服务器
     *
     * @param int $quickenType 存储类型枚举值：
     *                         1 = 阿里云OSS
     *                         2 = 七牛云
     *                         3 = 腾讯云COS
     *                         4 = 又拍云USS
     *                         5 = FTP服务器
     *                         6 = AWS S3
     *                         7 = 123盘
     *
     * @param array $config 上传配置数组，根据不同存储类型包含相应的认证信息：
     *                      - 通用字段: extend(文件扩展名), path(本地文件路径), far_url(访问URL前缀)
     *                      - FTP专用: ftp_host, ftp_port, ftp_username, ftp_password, ftp_pasv
     *                      - OSS专用: access_key_id, access_key_secret, bucket, endpoint
     *                      - 其他云存储有各自的认证字段
     *
     * @return array|null 上传结果数组：
     *                    成功: ['code' => 1, 'status' => 'success', 'url' => '文件URL', 'title' => '文件名']
     *                    失败: ['code' => 0, 'status' => 'error', 'msg' => '错误信息']
     *                    不支持的类型: null
     *
     * @since 1.0.0
     * <AUTHOR>
     *
     * @example
     * // FTP上传示例
     * $result = Assembly::transfer(5, [
     *     'ftp_host' => 'ftp.example.com',
     *     'ftp_username' => 'user',
     *     'ftp_password' => 'pass',
     *     'extend' => 'jpg',
     *     'path' => '/tmp/file.jpg'
     * ]);
     */
    public static function transfer($quickenType, $config)
    {
        //  判断上传类型
        switch (intval($quickenType)) {
            case 1: // 阿里云OSS
                return self::ossUpload($config);
            case 2: // 七牛云
                return self::qiniuUpload($config);
            case 3: // 腾讯云COS
                return self::cosUpload($config);
            case 4: // 又拍云
                return self::upyunUpload($config);
            case 5: // FTP
                return self::ftpUpload($config);
            case 6: // AWS S3
                return self::awsUpload($config);
            case 7: // 123盘
                return self::pan123Upload($config);
        }
    }

    /**
     * 阿里云OSS上传
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function ossUpload($config)
    {
        return self::_uploadWrapper($config, 'ossUploadHandler');
    }

    /**
     * 阿里云OSS上传处理器
     * @param string $object 远程文件路径
     * @param string $filePath 本地文件路径
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function ossUploadHandler($object, $filePath, $config)
    {
        $accessKeyId = $config['accessKeyId'];
        $accessKeySecret = $config['accessKeySecret'];
        $endpoint = $config['endpoint'];
        $bucket = $config['bucket'];
        $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
        $ossClient->uploadFile($bucket, $object, $filePath);
        return array();
    }

    /**
     * 七牛云Kodo上传
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function qiniuUpload($config)
    {
        return self::_uploadWrapper($config, 'qiniuUploadHandler');
    }

    /**
     * 七牛云Kodo上传处理器
     * @param string $object 远程文件路径
     * @param string $filePath 本地文件路径
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function qiniuUploadHandler($object, $filePath, $config)
    {
        $accessKey = $config['accessKey'];
        $secretKey = $config['secretKey'];
        $auth = new Auth($accessKey, $secretKey);
        $token = $auth->uploadToken($config['bucket']);
        $uploadMgr = new UploadManager();
        list(, $err) = $uploadMgr->putFile($token, $object, $filePath);
        if ($err !== null) {
            // 如果七牛云返回错误对象，则抛出异常，由wrapper统一处理
            throw new \Exception('qiniu upload error: ' . $err->message());
        }
        return array();
    }

    /**
     * 腾讯云COS上传
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function cosUpload($config)
    {
        return self::_uploadWrapper($config, 'cosUploadHandler');
    }

    /**
     * 腾讯云COS上传处理器
     * @param string $object 远程文件路径
     * @param string $filePath 本地文件路径
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function cosUploadHandler($object, $filePath, $config)
    {
        $qcloudConfig = array(
            'region' => $config['region'],
            'credentials' => array(
                'appId' => $config['appId'],
                'secretId' => $config['secretId'],
                'secretKey' => $config['secretKey']
            )
        );
        $cosClient = new Client($qcloudConfig);
        $cosClient->putObject(array(
            'Bucket' => $config['bucket'],
            'Key' => $object,
            // 以文件流方式上传
            'Body' => fopen($filePath, 'rb')
        ));
        return array();
    }

    /**
     * 又拍云USS上传
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function upyunUpload($config)
    {
        return self::_uploadWrapper($config, 'upyunUploadHandler');
    }

    /**
     * 又拍云USS上传处理器
     * @param string $object 远程文件路径
     * @param string $filePath 本地文件路径
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function upyunUploadHandler($object, $filePath, $config)
    {
        $serviceName = $config['service_name'];
        $operatorName = $config['operator_name'];
        $operatorPassword = $config['operator_password'];
        $bucketConfig = new Config($serviceName, $operatorName, $operatorPassword);
        $client = new Upyun($bucketConfig);
        $file = fopen($filePath, 'r');
        // 注意：又拍云的API要求远程路径以'/'开头
        $client->write('/' . $object, $file);
        return array();
    }

    /**
     * FTP文件上传
     * 该方法包含完整的FTP连接、认证、目录创建、文件上传和错误处理逻辑
     * 支持被动模式、超时设置，并提供详细的错误信息反馈
     *
     * @param array $config FTP上传配置数组，包含以下必需字段：
     *                      - ftp_host: string FTP服务器地址
     *                      - ftp_port: int FTP服务器端口
     *                      - ftp_username: string FTP用户名
     *                      - ftp_password: string FTP密码
     *                      - ftp_pasv: int 是否启用被动模式 (1=启用, 0=禁用)
     *                      - extend: string 文件扩展名
     *                      - path: string 本地文件路径
     *                      - far_url: string 远程访问URL前缀
     *
     * @return array 上传结果数组：
     *               成功时: ['code' => 1, 'status' => 'success', 'url' => '完整URL', 'title' => '文件名']
     *               失败时: ['code' => 0, 'status' => 'error', 'msg' => '错误信息']
     *
     * @throws none 所有异常都被捕获并转换为错误返回
     *
     * @example
     * $config = [
     *     'ftp_host' => 'ftp.example.com',
     *     'ftp_port' => 21,
     *     'ftp_username' => 'user',
     *     'ftp_password' => 'pass',
     *     'ftp_pasv' => 1,
     *     'extend' => 'jpg',
     *     'path' => '/tmp/upload.jpg',
     *     'far_url' => 'http://cdn.example.com'
     * ];
     * $result = self::ftpUpload($config);
     */
    private static function ftpUpload($config)
    {
        $errorMsg = '';

        // 上传成功，执行FTP上传
        $ftp = ftp_connect($config['ftp_host'], $config['ftp_port']);
        if (!$ftp) {
            $errorMsg = 'FTP连接失败: 主机=' . $config['ftp_host'] . ', 端口=' . $config['ftp_port'];
            return ['code' => 0, 'status' => 'error', 'msg' => $errorMsg];
        }

        // 设置FTP连接超时时间为30秒
        ftp_set_option($ftp, FTP_TIMEOUT_SEC, 30);

        $login = ftp_login($ftp, $config['ftp_username'], $config['ftp_password']);
        if (!$login) {
            ftp_close($ftp);
            $errorMsg = 'FTP登录失败: 用户名长度=' . strlen($config['ftp_username']) . ', 密码长度=' . strlen($config['ftp_password']);
            return ['code' => 0, 'status' => 'error', 'msg' => $errorMsg];
        }
        // 判断ftp被动模式是否开启
        if ($config['ftp_pasv'] == 1) {
            ftp_pasv($ftp, true);
        }
        $newly = self::uuid() . '.' . $config['extend'];
        $nowTime = time();
        $timeCatalog = date('Y/m/d', $nowTime);
        $remoteDirectory = '/' . $timeCatalog; // 远程目录路径
        $remoteFilePath = $remoteDirectory . '/' . $newly; // 完整的远程文件路径
        $localFilePath = $config['path']; // 本地文件路径
        // 检查本地文件是否存在
        if (!file_exists($localFilePath)) {
            ftp_close($ftp);
            $errorMsg = '本地文件不存在: 路径长度=' . strlen($localFilePath);
            return ['code' => 0, 'status' => 'error', 'msg' => $errorMsg];
        }
        // 创建目录
        $createResult = self::ftpCreateDirectory($ftp, $remoteDirectory);
        if (!$createResult['success']) {
            ftp_close($ftp);
            @unlink($config['path']);
            $errorMsg = '远程目录创建失败: ' . $createResult['error'] . ', 目录=' . $remoteDirectory;
            return ['code' => 0, 'status' => 'error', 'msg' => $errorMsg];
        }
        // 上传文件
        if (ftp_put($ftp, $remoteFilePath, $localFilePath, FTP_BINARY)) {
            $result = ['code' => 1, 'status' => 'success', 'url' => $config['far_url'] . $remoteFilePath, 'title' => $newly];
        } else {
            $error = error_get_last();
            $errorMsg = '文件上传失败: ' . ($error ? $error['message'] : '未知错误') . ', 远程路径长度=' . strlen($remoteFilePath);
            $result = ['code' => 0, 'status' => 'error', 'msg' => $errorMsg];
        }
        ftp_close($ftp);
        @unlink($config['path']);
        return $result;
    }

    /**
     * FTP递归创建目录
     * 该方法会逐级创建目录路径，并在操作完成后恢复到原始工作目录
     *
     * @param resource $ftp FTP连接资源
     * @param string $directory 要创建的目录路径，如 '/2024/01/15'
     * @return array 返回创建结果数组
     *               - success: bool 是否创建成功
     *               - error: string 错误信息，成功时为空字符串
     *
     * @example
     * $result = self::ftpCreateDirectory($ftp, '/uploads/2024/01/15');
     * if ($result['success']) {
     *     echo "目录创建成功";
     * } else {
     *     echo "创建失败: " . $result['error'];
     * }
     */
    private static function ftpCreateDirectory($ftp, $directory)
    {
        $directories = explode('/', $directory);
        $currentDirectory = '';
        $errorMsg = '';

        // 记录原始工作目录
        $originalDir = ftp_pwd($ftp);

        foreach ($directories as $dir) {
            if (empty($dir)) {
                continue;
            }
            $currentDirectory .= '/' . $dir;

            // 尝试切换到目录，如果失败则创建
            if (!@ftp_chdir($ftp, $currentDirectory)) {
                // 目录不存在，尝试创建
                if (!@ftp_mkdir($ftp, $currentDirectory)) {
                    $errorMsg = '无法创建目录: ' . $currentDirectory . ', 权限不足或路径无效';
                    // 恢复到原始目录
                    @ftp_chdir($ftp, $originalDir);
                    return ['success' => false, 'error' => $errorMsg];
                }
                // 创建成功后，尝试切换到新创建的目录进行验证
                if (!@ftp_chdir($ftp, $currentDirectory)) {
                    $errorMsg = '目录创建成功但无法访问: ' . $currentDirectory;
                    @ftp_chdir($ftp, $originalDir);
                    return ['success' => false, 'error' => $errorMsg];
                }
            }
        }

        // 恢复到原始目录
        @ftp_chdir($ftp, $originalDir);
        return ['success' => true, 'error' => ''];
    }

    /**
     * AWS S3 上传
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function awsUpload($config)
    {
        // 此处为特定业务逻辑，解锁某个属性后才可使用
        if (!Remotely::isUnLockProperty(base64_decode('6YCa55So5a2Y5YKo'))) {
            return array('code' => 0, 'msg' => 'error , access denied !');
        }
        return self::_uploadWrapper($config, 'awsUploadHandler');
    }

    /**
     * AWS S3上传处理器
     * @param string $object 远程文件路径
     * @param string $filePath 本地文件路径
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function awsUploadHandler($object, $filePath, $config)
    {
        // 按需加载AWS SDK，避免在不使用AWS上传时加载，提高性能。
        require_once "phar://" . EXTEND_PATH . "aws-sdk/aws.phar.gz/aws-autoloader.php";
        // 创建AWS SDK实例
        $region = isset($config['region']) && !empty($config['region']) ? $config['region'] : 'automatic';
        $forcePathStyle = isset($config['force_path_style']) ? $config['force_path_style'] : 0;
        $sdk = new \Aws\Sdk(array(
            'region' => $region,
            'version' => 'latest',
            'endpoint' => $config['endpoint'],
            'credentials' => array(
                'key' => $config['key'],
                'secret' => $config['secret'],
            ),
            'forcePathStyle' => (bool)$forcePathStyle,
        ));
        $s3Client = $sdk->createS3();
        $s3Result = $s3Client->putObject(array(
            'Bucket' => $config['bucket'],
            'Key' => $object,
            'Body' => fopen($filePath, 'rb'),
            'ACL' => 'public-read' // 设置文件为公开可读
        ));
        if ($s3Result['@metadata']['statusCode'] != 200) {
            // 如果S3返回的状态码不是200，则抛出异常
            throw new \Exception('AWS S3 upload failed with status code ' . $s3Result['@metadata']['statusCode']);
        }
        // 成功时，额外返回S3的ETag
        $etag = isset($s3Result['ETag']) ? $s3Result['ETag'] : 'unknown';
        return array('etag' => $etag);
    }

    /**
     * 123盘上传
     * @param array $config 上传配置，需包含client_id, client_secret等
     * @return array 上传结果
     */
    private static function pan123Upload($config)
    {
        return self::_uploadWrapper($config, 'pan123UploadHandler');
    }

    /**
     * 123盘上传处理器
     * @param string $object 远程文件路径
     * @param string $filePath 本地文件路径
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function pan123UploadHandler($object, $filePath, $config)
    {
        // 按需加载 123pan SDK
        require_once EXTEND_PATH . '123pan-sdk/Upload123Pan.php';
        // 从配置中获取凭证
        $clientID = $config['client_id'];
        $clientSecret = $config['client_secret'];
        // 实例化上传 SDK
        $uploader = new \Upload123Pan($clientID, $clientSecret);
        // 获取 Access Token 缓存
        $cacheName = "123pan_accessToken_" . md5($clientID);
        $accessToken = cache($cacheName);
        // 判断 Access Token 是否有效
        if (empty($accessToken) || (isset($accessToken['expiredAt']) && strtotime($accessToken['expiredAt']) - 60 < time())) {
            // 获取 Access Token
            $tokenData = $uploader->getAccessToken();
            // 设置 Access Token
            $accessToken = array(
                'token' => $tokenData['accessToken'],
                'expiredAt' => $tokenData['expiredAt'],
            );
            // 设置 Access Token
            $uploader->setAccessToken($accessToken['token'], $accessToken['expiredAt']);
            // 计算缓存有效期
            $expireInSeconds = strtotime($accessToken['expiredAt']) - time() - 60;
            // 如果缓存有效，则设置 Access Token
            if ($expireInSeconds > 0) {
                // 如果缓存有效，则设置 Access Token
                cache($cacheName, $accessToken, $expireInSeconds);
            }
        } else {
            // 如果缓存有效，则设置 Access Token
            $uploader->setAccessToken($accessToken['token'], $accessToken['expiredAt']);
        }
        // 调用 SDK 上传文件
        $fileID = $uploader->uploadFile(
            $filePath,
            0,
            $object,
            array('containDir' => true)
        );
        // 获取文件直链, 包含错误恢复和自动开启直链空间逻辑
        try {
            $directLink = $uploader->getDirectLink($fileID);
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), '目录未开启直链空间') !== false) {
                $currentFileID = $fileID;
                while (true) {
                    $details = $uploader->getFileDetail($currentFileID);
                    $parentFileID = $details['parentFileID'];
                    if ($parentFileID == 0) {
                        $topLevelDirID = $details['fileID'];
                        break;
                    }
                    $currentFileID = $parentFileID;
                }
                $uploader->enableDirectLinkSpace($topLevelDirID);
                $directLink = $uploader->getDirectLink($fileID);
            } else {
                throw $e;
            }
        }
        // 返回包含直链的数组，以便在 wrapper 中覆盖默认 URL
        return array('url' => $directLink);
    }

    /**
     * 生成标准化的云存储对象路径
     * 按照日期目录结构生成唯一的文件路径，便于文件管理和组织
     *
     * @param string $extension 文件扩展名（不含点），如 'jpg', 'png', 'pdf'
     *
     * @return array 包含路径信息的关联数组：
     *               - path: string 完整的对象路径，格式：'Y/m/d/uuid.extension'
     *               - title: string 带扩展名的文件名，格式：'uuid.extension'
     *
     * @since 1.0.0
     * <AUTHOR>
     *
     * @example
     * $pathInfo = self::_generateObjectPath('jpg');
     * // 返回类似：
     * // [
     * //     'path' => '2024/01/15/a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg',
     * //     'title' => 'a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg'
     * // ]
     *
     * @see uuid() 用于生成唯一文件名
     */
    private static function _generateObjectPath($extension)
    {
        $newly = self::uuid() . '.' . $extension;
        $objectPath = date('Y/m/d') . '/' . $newly;
        return array('path' => $objectPath, 'title' => $newly);
    }

    /**
     * 通用云存储上传包装器
     * 为各种云存储服务（OSS、七牛、COS、又拍云、AWS S3、123盘）提供统一的上传流程
     * 封装了路径生成、异常捕获、临时文件清理和结果格式化等通用逻辑
     *
     * @param array $config 上传配置数组，必须包含以下字段：
     *                      - path: string 本地文件路径
     *                      - extend: string 文件扩展名（不含点）
     *                      - far_url: string 远程访问URL前缀
     *                      - 其他云服务特定的认证字段
     *
     * @param string $handlerMethod 处理器方法名，用于执行特定云服务的上传操作
     *                              该方法必须接收三个参数：
     *                              - $object: string 远程对象路径
     *                              - $filePath: string 本地文件路径
     *                              - $config: array 配置数组
     *
     * @return array 上传结果数组：
     *               成功: ['code' => 1, 'status' => 'success', 'url' => '完整URL', 'title' => '文件名']
     *               失败: ['code' => 0, 'msg' => '错误信息']
     *
     * @throws \Exception 当处理器方法不存在或执行失败时抛出异常
     *
     * @since 1.0.0
     * <AUTHOR>
     *
     * @example
     * // 内部调用示例
     * $result = self::_uploadWrapper($config, 'ossUploadHandler');
     *
     * @see ossUploadHandler()
     * @see qiniuUploadHandler()
     * @see cosUploadHandler()
     */
    private static function _uploadWrapper($config, $handlerMethod)
    {
        $filePath = $config['path'];
        // 前置检查：确保本地文件存在
        if (!file_exists($filePath)) {
            return array('code' => 0, 'msg' => 'Error: Local file does not exist - ' . $filePath);
        }
        try {
            // 1. 生成远程文件路径
            $pathInfo = self::_generateObjectPath($config['extend']);
            $object = $pathInfo['path'];
            $newly = $pathInfo['title'];
            // 2. 执行由具体上传方法传入的处理器方法，进行实际的上传操作
            switch ($handlerMethod) {
                case 'ossUploadHandler':
                    $extraData = self::ossUploadHandler($object, $filePath, $config);
                    break;
                case 'qiniuUploadHandler':
                    $extraData = self::qiniuUploadHandler($object, $filePath, $config);
                    break;
                case 'cosUploadHandler':
                    $extraData = self::cosUploadHandler($object, $filePath, $config);
                    break;
                case 'upyunUploadHandler':
                    $extraData = self::upyunUploadHandler($object, $filePath, $config);
                    break;
                case 'awsUploadHandler':
                    $extraData = self::awsUploadHandler($object, $filePath, $config);
                    break;
                case 'pan123UploadHandler':
                    $extraData = self::pan123UploadHandler($object, $filePath, $config);
                    break;
                default:
                    throw new \Exception('Unknown upload handler: ' . $handlerMethod);
            }
            // 3. 准备标准化的成功返回结果
            $result = array(
                'code' => 1,
                'status' => 'success',
                'url' => rtrim($config['far_url'], '/') . '/' . $object,
                'title' => $newly
            );
            // 4. 如果处理器方法返回了额外数据（如S3的ETag），则合并到结果中
            if (is_array($extraData) && !empty($extraData)) {
                $result = array_merge($result, $extraData);
            }
            return $result;
        } catch (\Exception $e) {
            // 捕获上传过程中任何环节的异常
            Log::write('Upload error: ' . $e->getMessage(), 'log', true);
            // 返回标准化的错误信息
            return array('code' => 0, 'msg' => 'error , ' . $e->getMessage());
        } finally {
            // 5. 无论上传成功还是失败，最终都会执行此处的代码
            // 确保本地临时文件总是被尝试删除，防止文件残留
            if (isset($filePath) && file_exists($filePath)) {
                @unlink($filePath);
            }
        }
    }

    /**
     * 生成基于MD5的伪UUID字符串
     * 用于创建唯一的文件名，格式为标准UUID样式但基于MD5哈希
     *
     * @return string 返回格式化的UUID字符串，格式：xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
     *                总长度36个字符（包含4个连字符），基于MD5的32位哈希值
     *
     * @since 1.0.0
     * <AUTHOR>
     *
     * @example
     * $fileId = Assembly::uuid();
     * // 输出类似: "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
     *
     * $fileName = Assembly::uuid() . '.jpg';
     * // 输出类似: "a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg"
     */
    public static function uuid()
    {
        // 使用md5对随机生成的唯一ID进行哈希处理
        $str = md5(uniqid(mt_rand(), true));
        // 格式化UUID的第一部分，取哈希的前8个字符
        $uuid = substr($str, 0, 8) . '-';
        // 添加UUID的第二部分，取哈希的第9-12个字符
        $uuid .= substr($str, 8, 4) . '-';
        // 添加UUID的第三部分，取哈希的第13-16个字符
        $uuid .= substr($str, 12, 4) . '-';
        // 添加UUID的第四部分，取哈希的第17-20个字符
        $uuid .= substr($str, 16, 4) . '-';
        // 添加UUID的第五部分，取哈希的第21-32个字符
        $uuid .= substr($str, 20, 12);
        // 将UUID与随机数和当前时间戳组合并进行MD5哈希，进一步增强唯一性
        return md5($uuid . uniqid(mt_rand(), true) . time());
    }
}
